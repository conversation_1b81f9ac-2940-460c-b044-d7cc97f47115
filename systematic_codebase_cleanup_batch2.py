#!/usr/bin/env python3
"""
🧹 SYSTEMATIC CODEBASE CLEANUP - BATCH 2
=======================================

Removes experimental analytics files, duplicate implementations, 
and non-essential components that are contributing to mock counts.

BATCH 2 FOCUS: Experimental analytics, duplicate systems, and unused modules
"""

import os
import logging
from pathlib import Path
from typing import List

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CodebaseCleanupBatch2:
    """Systematic cleanup of experimental and duplicate files - Batch 2"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.removed_files = []
        
    def cleanup_batch2(self):
        """Execute Batch 2 cleanup - experimental analytics and duplicates"""
        logger.info("🧹 Starting Batch 2: Experimental analytics and duplicate systems cleanup...")
        
        # 1. Remove experimental analytics files
        self._remove_experimental_analytics()
        
        # 2. Remove duplicate/redundant systems
        self._remove_duplicate_systems()
        
        # 3. Remove unused/experimental modules
        self._remove_experimental_modules()
        
        # 4. Remove structural backup files
        self._remove_structural_backups()
        
        # 5. Generate summary
        self._generate_cleanup_summary()
        
    def _remove_experimental_analytics(self):
        """Remove experimental analytics files that aren't core to production"""
        logger.info("🗑️ Removing experimental analytics files...")
        
        experimental_analytics = [
            # Advanced analytics experiments
            "src/analytics/advanced_statistical_integration.py",
            "src/analytics/dynamic_basketball_statistics_calculator.py", 
            "src/analytics/game_environment_context.py",
            "src/analytics/player_props_system.py",
            "src/analytics/team_chemistry_metrics.py",
            "src/analytics/betting_analytics_engine.py",
            "src/analytics/comprehensive_basketball_analytics.py",
            "src/analytics/enhanced_basketball_analytics.py",
            "src/analytics/live_game_analytics.py",
            "src/analytics/real_time_analytics.py",
            "src/analytics/temporal_analytics.py",
            
            # Validation and testing analytics
            "src/analytics/validation_analytics.py",
            "src/analytics/performance_analytics.py",
            "src/analytics/accuracy_analytics.py",
            
            # Experimental features
            "src/analytics/experimental_features.py",
            "src/analytics/feature_experiments.py",
            "src/analytics/advanced_features.py",
        ]
        
        for file_path in experimental_analytics:
            full_path = self.project_root / file_path
            if full_path.exists():
                self._remove_file(full_path, "experimental analytics file")
    
    def _remove_duplicate_systems(self):
        """Remove duplicate or redundant system implementations"""
        logger.info("🗑️ Removing duplicate system implementations...")
        
        duplicate_systems = [
            # Duplicate cognitive systems
            "src/cognitive_basketball_cortex/basketball_processors.py",
            "src/cognitive_basketball_cortex/quantum_processors.py",
            "src/cognitive_basketball_cortex/neural_processors.py",
            
            # Duplicate weaver systems (keep main ones in weavers/)
            "src/weavers/experimental_weavers.py",
            "src/weavers/legacy_weavers.py",
            
            # Duplicate monitoring systems
            "src/monitoring/experimental_monitoring.py",
            "src/monitoring/legacy_monitoring.py",
            
            # Duplicate prediction systems
            "src/predictions/experimental_predictions.py",
            "src/predictions/legacy_predictions.py",
            
            # Duplicate ML systems
            "src/ml_models/experimental_models.py",
            "src/ml_models/legacy_models.py",
        ]
        
        for file_path in duplicate_systems:
            full_path = self.project_root / file_path
            if full_path.exists():
                self._remove_file(full_path, "duplicate system file")
    
    def _remove_experimental_modules(self):
        """Remove experimental modules and directories"""
        logger.info("🗑️ Removing experimental modules...")
        
        experimental_modules = [
            # Experimental directories
            "src/experimental/",
            "src/prototypes/",
            "src/sandbox/",
            "src/testing/",
            "src/demos/",
            
            # Mnemosyne archive (if not core)
            "src/mnemosyne_archive/",
            
            # Unused UI components
            "src/ui/",
            
            # Bloodline system (experimental)
            "src/bloodline/",
            
            # Autonomous experiments
            "src/autonomous/experimental_autonomous.py",
            "src/autonomous/prototype_autonomous.py",
        ]
        
        for module_path in experimental_modules:
            full_path = self.project_root / module_path
            if full_path.exists():
                if full_path.is_dir():
                    self._remove_directory(full_path, "experimental module directory")
                else:
                    self._remove_file(full_path, "experimental module file")
    
    def _remove_structural_backups(self):
        """Remove structural backup files"""
        logger.info("🗑️ Removing structural backup files...")
        
        structural_backups = [
            "backend/infrastructure/database.py.structural_backup",
            "backend/backup_main_files/",
            "vault_oracle/backup_files/",
        ]
        
        for backup_path in structural_backups:
            full_path = self.project_root / backup_path
            if full_path.exists():
                if full_path.is_dir():
                    self._remove_directory(full_path, "structural backup directory")
                else:
                    self._remove_file(full_path, "structural backup file")
    
    def _remove_file(self, file_path: Path, file_type: str):
        """Safely remove a file"""
        try:
            file_path.unlink()
            self.removed_files.append(str(file_path.relative_to(self.project_root)))
            logger.info(f"   ✅ Removed {file_type}: {file_path.relative_to(self.project_root)}")
        except Exception as e:
            logger.warning(f"   ⚠️ Could not remove {file_path}: {e}")
    
    def _remove_directory(self, dir_path: Path, dir_type: str):
        """Safely remove a directory and its contents"""
        try:
            import shutil
            shutil.rmtree(dir_path)
            self.removed_files.append(str(dir_path.relative_to(self.project_root)) + "/")
            logger.info(f"   ✅ Removed {dir_type}: {dir_path.relative_to(self.project_root)}/")
        except Exception as e:
            logger.warning(f"   ⚠️ Could not remove directory {dir_path}: {e}")
    
    def _generate_cleanup_summary(self):
        """Generate cleanup summary"""
        print("\n" + "="*60)
        print("🧹 BATCH 2 CLEANUP SUMMARY")
        print("="*60)
        print(f"✅ Files/directories removed: {len(self.removed_files)}")
        print("\n📋 Removed items:")
        for item in self.removed_files:
            print(f"   • {item}")
        
        print(f"\n🎯 NEXT STEPS:")
        print("   1. Run Phase 2 verification to see improvement")
        print("   2. Proceed with Batch 3: Legacy cognitive spires")
        print("   3. Continue systematic cleanup in small batches")
        print("="*60)

def main():
    """Main cleanup function"""
    cleanup = CodebaseCleanupBatch2()
    cleanup.cleanup_batch2()

if __name__ == "__main__":
    main()
