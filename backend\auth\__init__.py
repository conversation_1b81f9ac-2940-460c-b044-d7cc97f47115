"""
Authentication Package
=====================

User authentication and authorization utilities.
"""

try:
    from .dependencies import (
        get_current_user,
        get_current_active_user,
        require_subscription,
        require_role,
        get_optional_user,
        RateLimiter,
        generate_demo_token,
        check_feature_access
    )
    AUTH_DEPENDENCIES_AVAILABLE = True
except ImportError:
    AUTH_DEPENDENCIES_AVAILABLE = False
    get_current_user = None
    get_current_active_user = None
    require_subscription = None
    require_role = None
    get_optional_user = None
    RateLimiter = None
    generate_demo_token = None
    check_feature_access = None

__all__ = [
 "get_current_user",
 "get_current_active_user", 
 "require_subscription",
 "require_role",
 "get_optional_user",
 "RateLimiter",
 "generate_demo_token",
 "check_feature_access"
]
