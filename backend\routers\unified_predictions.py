"""
🏀 HYPER MEDUSA NEURAL VAULT - Unified Prediction Router
=======================================================

Unified router that consolidates all prediction endpoints.
"""

from fastapi import APIRouter, Depends, HTTPException
from typing import Optional, Dict, Any
import logging

# Import from both prediction APIs
try:
    from src.api.prediction_api import app as prediction_app
    PREDICTION_API_AVAILABLE = True
except ImportError:
    PREDICTION_API_AVAILABLE = False

try:
    from src.api.ml_prediction_api import app as ml_prediction_app
    ML_PREDICTION_API_AVAILABLE = True
except ImportError:
    ML_PREDICTION_API_AVAILABLE = False

logger = logging.getLogger("UnifiedPredictionRouter")

# Create unified router
router = APIRouter(
    prefix="/api/v1/predictions",
    tags=["🧠 Unified Predictions"],
    responses={
        401: {"description": "Authentication required"},
        403: {"description": "Access forbidden"},
        500: {"description": "Internal server error"}
    }
)

@router.get("/status")
async def prediction_system_status():
    """Get unified prediction system status"""
    return {
        "service": "🧠 HYPER MEDUSA NEURAL VAULT - Unified Prediction System",
        "status": "OPERATIONAL",
        "apis": {
            "prediction_api": "AVAILABLE" if PREDICTION_API_AVAILABLE else "UNAVAILABLE",
            "ml_prediction_api": "AVAILABLE" if ML_PREDICTION_API_AVAILABLE else "UNAVAILABLE"
        },
        "unified": True,
        "endpoints": {
            "game_prediction": "/api/v1/predictions/game",
            "player_props": "/api/v1/predictions/player-props",
            "neural_enhanced": "/api/v1/predictions/neural"
        }
    }

@router.get("/game")
async def unified_game_prediction():
    """Unified game prediction endpoint"""
    # Route to best available prediction API
    if ML_PREDICTION_API_AVAILABLE:
        # Use ML prediction API as primary
        return {"message": "Routing to ML Prediction API", "api": "ml_prediction"}
    elif PREDICTION_API_AVAILABLE:
        # Fallback to standard prediction API
        return {"message": "Routing to Standard Prediction API", "api": "prediction"}
    else:
        raise HTTPException(status_code=503, detail="No prediction APIs available")

@router.get("/player-props")
async def unified_player_props():
    """Unified player props prediction endpoint"""
    # Route to best available prediction API
    if ML_PREDICTION_API_AVAILABLE:
        return {"message": "Routing to ML Player Props", "api": "ml_prediction"}
    elif PREDICTION_API_AVAILABLE:
        return {"message": "Routing to Standard Player Props", "api": "prediction"}
    else:
        raise HTTPException(status_code=503, detail="No prediction APIs available")

# Export router for integration
__all__ = ["router"]
