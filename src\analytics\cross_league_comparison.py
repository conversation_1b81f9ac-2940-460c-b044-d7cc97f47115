#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Cross League Comparison System
=========================================================

Advanced cross-league comparison system for NBA and WNBA analysis.
Provides comprehensive comparison metrics, player equivalencies,
and league-specific adjustments for accurate predictions.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger("CrossLeagueComparison")

class League(Enum):
    NBA = "NBA"
    WNBA = "WNBA"

@dataclass
class CrossLeagueComparison:
    """Cross-league comparison metrics"""
    nba_value: float
    wnba_value: float
    conversion_factor: float
    confidence: float
    metric_name: str
    comparison_type: str = "statistical"

@dataclass
class LeagueAdjustments:
    """League-specific adjustment factors"""
    pace_adjustment: float = 1.0
    scoring_adjustment: float = 1.0
    defensive_adjustment: float = 1.0
    physicality_adjustment: float = 1.0
    experience_adjustment: float = 1.0

class CrossLeagueComparisonSystem:
    """
    Advanced cross-league comparison and conversion system
    """
    
    def __init__(self, db_path: str = "data/basketball_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("CrossLeagueComparison")
        
        # League baseline statistics
        self.league_baselines = {
            League.NBA: {
                'avg_pace': 100.0,
                'avg_points': 112.0,
                'avg_assists': 24.5,
                'avg_rebounds': 44.0,
                'avg_steals': 7.8,
                'avg_blocks': 4.9,
                'avg_turnovers': 14.2,
                'avg_fg_pct': 0.462,
                'avg_3pt_pct': 0.358,
                'avg_ft_pct': 0.783,
                'season_games': 82,
                'game_minutes': 48
            },
            League.WNBA: {
                'avg_pace': 92.0,
                'avg_points': 82.0,
                'avg_assists': 19.2,
                'avg_rebounds': 34.5,
                'avg_steals': 7.1,
                'avg_blocks': 3.2,
                'avg_turnovers': 15.8,
                'avg_fg_pct': 0.442,
                'avg_3pt_pct': 0.342,
                'avg_ft_pct': 0.815,
                'season_games': 40,
                'game_minutes': 40
            }
        }
        
        # Conversion factors between leagues
        self.conversion_factors = {
            'points': 0.73,  # WNBA to NBA points conversion
            'rebounds': 0.78,
            'assists': 0.78,
            'steals': 0.91,
            'blocks': 0.65,
            'turnovers': 1.11,
            'minutes': 0.83,  # 40 min vs 48 min games
            'pace': 0.92
        }
    
    def convert_stat_between_leagues(self, value: float, stat_type: str, 
                                   from_league: League, to_league: League) -> CrossLeagueComparison:
        """
        Convert a statistical value between NBA and WNBA
        """
        try:
            if from_league == to_league:
                return CrossLeagueComparison(
                    nba_value=value if from_league == League.NBA else value / self.conversion_factors.get(stat_type, 1.0),
                    wnba_value=value if from_league == League.WNBA else value * self.conversion_factors.get(stat_type, 1.0),
                    conversion_factor=1.0,
                    confidence=1.0,
                    metric_name=stat_type
                )
            
            conversion_factor = self.conversion_factors.get(stat_type, 0.8)
            
            if from_league == League.NBA and to_league == League.WNBA:
                converted_value = value * conversion_factor
                nba_value = value
                wnba_value = converted_value
            else:  # WNBA to NBA
                converted_value = value / conversion_factor
                nba_value = converted_value
                wnba_value = value
            
            # Calculate confidence based on how well-established the conversion is
            confidence = 0.85 if stat_type in ['points', 'rebounds', 'assists'] else 0.75
            
            return CrossLeagueComparison(
                nba_value=nba_value,
                wnba_value=wnba_value,
                conversion_factor=conversion_factor,
                confidence=confidence,
                metric_name=stat_type
            )
            
        except Exception as e:
            self.logger.error(f"Error converting {stat_type} from {from_league} to {to_league}: {e}")
            return CrossLeagueComparison(
                nba_value=value,
                wnba_value=value,
                conversion_factor=1.0,
                confidence=0.5,
                metric_name=stat_type
            )
    
    def get_league_adjustments(self, target_league: League) -> LeagueAdjustments:
        """
        Get adjustment factors for a specific league
        """
        try:
            if target_league == League.NBA:
                return LeagueAdjustments(
                    pace_adjustment=1.0,
                    scoring_adjustment=1.0,
                    defensive_adjustment=1.0,
                    physicality_adjustment=1.0,
                    experience_adjustment=1.0
                )
            else:  # WNBA
                return LeagueAdjustments(
                    pace_adjustment=0.92,  # Slower pace
                    scoring_adjustment=0.73,  # Lower scoring
                    defensive_adjustment=0.85,  # Different defensive style
                    physicality_adjustment=0.80,  # Different physicality
                    experience_adjustment=0.95  # Similar experience levels
                )
                
        except Exception as e:
            self.logger.error(f"Error getting league adjustments: {e}")
            return LeagueAdjustments()
    
    def compare_player_across_leagues(self, nba_stats: Dict[str, float], 
                                    wnba_stats: Dict[str, float]) -> Dict[str, CrossLeagueComparison]:
        """
        Compare equivalent players across leagues
        """
        try:
            comparisons = {}
            
            # Compare key statistics
            key_stats = ['points', 'rebounds', 'assists', 'steals', 'blocks', 'turnovers']
            
            for stat in key_stats:
                if stat in nba_stats and stat in wnba_stats:
                    # Convert both to common scale (NBA scale)
                    nba_normalized = nba_stats[stat]
                    wnba_to_nba = self.convert_stat_between_leagues(
                        wnba_stats[stat], stat, League.WNBA, League.NBA
                    )
                    
                    comparisons[stat] = CrossLeagueComparison(
                        nba_value=nba_normalized,
                        wnba_value=wnba_stats[stat],
                        conversion_factor=wnba_to_nba.conversion_factor,
                        confidence=wnba_to_nba.confidence,
                        metric_name=stat,
                        comparison_type="player_comparison"
                    )
            
            return comparisons
            
        except Exception as e:
            self.logger.error(f"Error comparing players across leagues: {e}")
            return {}
    
    def calculate_league_equivalency_score(self, player_stats: Dict[str, float], 
                                         from_league: League, 
                                         to_league: League) -> float:
        """
        Calculate how a player would perform in the other league
        """
        try:
            if from_league == to_league:
                return 1.0
            
            # Key performance indicators
            key_stats = ['points', 'rebounds', 'assists', 'field_goal_pct', 'usage_rate']
            equivalency_scores = []
            
            for stat in key_stats:
                if stat in player_stats:
                    conversion = self.convert_stat_between_leagues(
                        player_stats[stat], stat, from_league, to_league
                    )
                    
                    # Calculate relative performance in target league
                    target_baseline = self.league_baselines[to_league].get(f'avg_{stat}', 1.0)
                    if target_baseline > 0:
                        relative_performance = (conversion.nba_value if to_league == League.NBA 
                                              else conversion.wnba_value) / target_baseline
                        equivalency_scores.append(relative_performance * conversion.confidence)
            
            return np.mean(equivalency_scores) if equivalency_scores else 0.8
            
        except Exception as e:
            self.logger.error(f"Error calculating league equivalency: {e}")
            return 0.8
    
    def adjust_prediction_for_league(self, prediction: Dict[str, float], 
                                   target_league: League) -> Dict[str, float]:
        """
        Adjust prediction values for specific league characteristics
        """
        try:
            adjustments = self.get_league_adjustments(target_league)
            adjusted_prediction = prediction.copy()
            
            # Apply league-specific adjustments
            if 'points' in adjusted_prediction:
                adjusted_prediction['points'] *= adjustments.scoring_adjustment
            
            if 'total_points' in adjusted_prediction:
                adjusted_prediction['total_points'] *= adjustments.scoring_adjustment
            
            if 'pace' in adjusted_prediction:
                adjusted_prediction['pace'] *= adjustments.pace_adjustment
            
            # Adjust confidence based on league familiarity
            if 'confidence' in adjusted_prediction:
                adjusted_prediction['confidence'] *= adjustments.experience_adjustment
            
            return adjusted_prediction
            
        except Exception as e:
            self.logger.error(f"Error adjusting prediction for league: {e}")
            return prediction
    
    def get_cross_league_insights(self, nba_data: Dict[str, Any], 
                                wnba_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate insights from cross-league analysis
        """
        try:
            insights = {
                'league_differences': {},
                'conversion_reliability': {},
                'prediction_adjustments': {},
                'comparative_strengths': {}
            }
            
            # Analyze league differences
            for stat in ['points', 'pace', 'assists', 'rebounds']:
                if stat in nba_data and stat in wnba_data:
                    nba_avg = nba_data[stat]
                    wnba_avg = wnba_data[stat]
                    difference_pct = ((nba_avg - wnba_avg) / wnba_avg) * 100
                    
                    insights['league_differences'][stat] = {
                        'nba_average': nba_avg,
                        'wnba_average': wnba_avg,
                        'difference_percent': difference_pct,
                        'nba_higher': nba_avg > wnba_avg
                    }
            
            # Conversion reliability scores
            for stat, factor in self.conversion_factors.items():
                reliability = 0.9 if factor > 0.7 and factor < 1.3 else 0.7
                insights['conversion_reliability'][stat] = reliability
            
            return insights
            
        except Exception as e:
            self.logger.error(f"Error generating cross-league insights: {e}")
            return {
                'league_differences': {},
                'conversion_reliability': {},
                'prediction_adjustments': {},
                'comparative_strengths': {}
            }
