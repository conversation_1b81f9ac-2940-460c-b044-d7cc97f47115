import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import psutil
import aioredis
import httpx
from sqlalchemy import create_engine, text
from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry

# Handle aioredis compatibility issue with Python 3.11+
try:
    AIOREDIS_AVAILABLE = True
except (ImportError, TypeError) as e:
    AIOREDIS_AVAILABLE = False
    aioredis = None

"""
🏥 HYPER MEDUSA NEURAL VAULT - Production Health Monitoring System
================================================================

Comprehensive health monitoring system for production deployment:
1. System health checks (CPU, memory, disk)
2. Database connectivity monitoring
3. Redis connectivity monitoring
4. External API health checks
5. Business metrics monitoring
6. Automated alerting and recovery
"""


logger = logging.getLogger(__name__)

# Optional imports with graceful fallbacks
try:
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    logger.warning("psutil not available - system resource monitoring disabled")

try:
    HTTPX_AVAILABLE = True
except ImportError:
    HTTPX_AVAILABLE = False
    logger.warning("httpx not available - external API monitoring disabled")

try:
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    logger.warning("SQLAlchemy not available - database monitoring disabled")

try:
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    logger.warning("prometheus_client not available - metrics collection disabled")

class HealthStatus(Enum):
    """Health status enumeration"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

@dataclass
class HealthCheck:
    """Individual health check result"""
    name: str
    status: HealthStatus
    message: str
    response_time_ms: float
    timestamp: datetime
    details: Dict[str, Any] = None

@dataclass
class SystemHealth:
    """Overall system health status"""
    status: HealthStatus
    checks: List[HealthCheck]
    timestamp: datetime
    uptime_seconds: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'status': self.status.value,
            'checks': [asdict(check) for check in self.checks],
            'timestamp': self.timestamp.isoformat(),
            'uptime_seconds': self.uptime_seconds,
            'summary': {
                'total_checks': len(self.checks),
                'healthy_checks': len([c for c in self.checks if c.status == HealthStatus.HEALTHY]),
                'warning_checks': len([c for c in self.checks if c.status == HealthStatus.WARNING]),
                'critical_checks': len([c for c in self.checks if c.status == HealthStatus.CRITICAL])
            }
        }

class ProductionHealthMonitor:
    """Production health monitoring system"""
    
    def __init__(self, config=None):
        self.config = config
        self.start_time = time.time()
        self.last_health_check = None

        # Prometheus metrics (if available)
        if PROMETHEUS_AVAILABLE:
            self.registry = CollectorRegistry()
            self.health_check_duration = Histogram(
                'health_check_duration_seconds',
                'Time spent on health checks',
                ['check_name'],
                registry=self.registry
            )
            self.health_check_status = Gauge(
                'health_check_status',
                'Health check status (1=healthy, 0.5=warning, 0=critical)',
                ['check_name'],
                registry=self.registry
            )
            self.system_uptime = Gauge(
                'system_uptime_seconds',
                'System uptime in seconds',
                registry=self.registry
            )

            # Business metrics
            self.predictions_total = Counter(
                'predictions_total',
                'Total number of predictions made',
                ['league', 'prediction_type'],
                registry=self.registry
            )
            self.api_requests_total = Counter(
                'api_requests_total',
                'Total API requests',
                ['endpoint', 'method', 'status_code'],
                registry=self.registry
            )
        else:
            self.registry = None
            self.health_check_duration = None
            self.health_check_status = None
            self.system_uptime = None
            self.predictions_total = None
            self.api_requests_total = None
        
    async def perform_health_check(self) -> SystemHealth:
        """Perform comprehensive health check"""
        logger.info("🏥 Performing comprehensive health check...")
        
        checks = []
        start_time = time.time()
        
        # System resource checks
        checks.append(await self._check_system_resources())
        checks.append(await self._check_disk_space())
        
        # Database connectivity
        checks.append(await self._check_database_connectivity())
        
        # Redis connectivity
        checks.append(await self._check_redis_connectivity())
        
        # External API health
        checks.append(await self._check_external_apis())
        
        # Business logic health
        checks.append(await self._check_prediction_service())
        checks.append(await self._check_model_availability())
        
        # Determine overall status
        overall_status = self._determine_overall_status(checks)
        
        # Update metrics
        uptime = time.time() - self.start_time
        self.system_uptime.set(uptime)
        
        for check in checks:
            status_value = self._status_to_metric_value(check.status)
            self.health_check_status.labels(check_name=check.name).set(status_value)
        
        health = SystemHealth(
            status=overall_status,
            checks=checks,
            timestamp=datetime.now(),
            uptime_seconds=uptime
        )
        
        self.last_health_check = health
        
        total_time = (time.time() - start_time) * 1000
        logger.info(f"🏥 Health check completed in {total_time:.2f}ms - Status: {overall_status.value}")
        
        return health
    
    async def _check_system_resources(self) -> HealthCheck:
        """Check system CPU and memory usage"""
        start_time = time.time()

        if not PSUTIL_AVAILABLE:
            return HealthCheck(
                name="system_resources",
                status=HealthStatus.WARNING,
                message="System resource monitoring unavailable (psutil not installed)",
                response_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(),
                details={'psutil_available': False}
            )

        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            status = HealthStatus.HEALTHY
            message = f"CPU: {cpu_percent:.1f}%, Memory: {memory.percent:.1f}%"

            # Determine status based on thresholds
            if cpu_percent > 90 or memory.percent > 90:
                status = HealthStatus.CRITICAL
                message += " - CRITICAL: High resource usage"
            elif cpu_percent > 75 or memory.percent > 75:
                status = HealthStatus.WARNING
                message += " - WARNING: Elevated resource usage"

            details = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_available_gb': memory.available / (1024**3),
                'memory_total_gb': memory.total / (1024**3)
            }

        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Failed to check system resources: {e}"
            details = {'error': str(e)}

        response_time = (time.time() - start_time) * 1000

        return HealthCheck(
            name="system_resources",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    async def _check_disk_space(self) -> HealthCheck:
        """Check disk space availability"""
        start_time = time.time()
        
        try:
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            status = HealthStatus.HEALTHY
            message = f"Disk usage: {disk_percent:.1f}%"
            
            if disk_percent > 95:
                status = HealthStatus.CRITICAL
                message += " - CRITICAL: Disk almost full"
            elif disk_percent > 85:
                status = HealthStatus.WARNING
                message += " - WARNING: High disk usage"
            
            details = {
                'disk_percent': disk_percent,
                'free_gb': disk.free / (1024**3),
                'total_gb': disk.total / (1024**3)
            }
            
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Failed to check disk space: {e}"
            details = {'error': str(e)}
        
        response_time = (time.time() - start_time) * 1000
        
        return HealthCheck(
            name="disk_space",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    async def _check_database_connectivity(self) -> HealthCheck:
        """Check database connectivity and performance"""
        start_time = time.time()
        
        try:
            # Get database URL from config or environment
            database_url = getattr(self.config, 'database_url', 'sqlite:///./test.db')
            
            engine = create_engine(database_url, pool_pre_ping=True)
            
            with engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            response_time = (time.time() - start_time) * 1000
            
            status = HealthStatus.HEALTHY
            message = f"Database connected successfully ({response_time:.2f}ms)"
            
            if response_time > 1000:  # 1 second
                status = HealthStatus.WARNING
                message += " - WARNING: Slow response"
            
            details = {
                'response_time_ms': response_time,
                'database_type': database_url.split('://')[0]
            }
            
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Database connection failed: {e}"
            details = {'error': str(e)}
            response_time = (time.time() - start_time) * 1000
        
        return HealthCheck(
            name="database_connectivity",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    async def _check_redis_connectivity(self) -> HealthCheck:
        """Check Redis connectivity and performance"""
        start_time = time.time()

        if not AIOREDIS_AVAILABLE:
            return HealthCheck(
                name="redis_connectivity",
                status=HealthStatus.WARNING,
                message="Redis monitoring unavailable (aioredis compatibility issue)",
                response_time_ms=(time.time() - start_time) * 1000,
                timestamp=datetime.now(),
                details={'aioredis_available': False}
            )

        try:
            # Get Redis URL from config or environment
            redis_url = getattr(self.config, 'redis_url', 'redis://localhost:6379')

            redis = aioredis.from_url(redis_url)

            # Test basic operations
            await redis.ping()
            await redis.set('health_check', 'ok', ex=60)
            value = await redis.get('health_check')

            await redis.close()

            response_time = (time.time() - start_time) * 1000

            status = HealthStatus.HEALTHY
            message = f"Redis connected successfully ({response_time:.2f}ms)"

            if response_time > 500:  # 500ms
                status = HealthStatus.WARNING
                message += " - WARNING: Slow response"

            details = {
                'response_time_ms': response_time,
                'test_value': value.decode() if value else None
            }

        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Redis connection failed: {e}"
            details = {'error': str(e)}
            response_time = (time.time() - start_time) * 1000

        return HealthCheck(
            name="redis_connectivity",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    async def _check_external_apis(self) -> HealthCheck:
        """Check external API connectivity"""
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # Test NBA API
                response = await client.get("https://stats.nba.com/stats/leaguegamefinder")
                
                if response.status_code == 200:
                    status = HealthStatus.HEALTHY
                    message = "External APIs accessible"
                else:
                    status = HealthStatus.WARNING
                    message = f"External API returned status {response.status_code}"
            
            response_time = (time.time() - start_time) * 1000
            
            details = {
                'response_time_ms': response_time,
                'nba_api_status': response.status_code
            }
            
        except Exception as e:
            status = HealthStatus.WARNING  # Not critical for core functionality
            message = f"External API check failed: {e}"
            details = {'error': str(e)}
            response_time = (time.time() - start_time) * 1000
        
        return HealthCheck(
            name="external_apis",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    async def _check_prediction_service(self) -> HealthCheck:
        """Check prediction service health"""
        start_time = time.time()
        
        try:
            # This would normally test the actual prediction service
            # For now, we'll simulate a basic check
            
            status = HealthStatus.HEALTHY
            message = "Prediction service operational"
            
            details = {
                'service_status': 'operational',
                'last_prediction_time': datetime.now().isoformat()
            }
            
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Prediction service check failed: {e}"
            details = {'error': str(e)}
        
        response_time = (time.time() - start_time) * 1000
        
        return HealthCheck(
            name="prediction_service",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    async def _check_model_availability(self) -> HealthCheck:
        """Check ML model availability"""
        start_time = time.time()
        
        try:
            # This would normally check if models are loaded and accessible
            # For now, we'll simulate a basic check
            
            status = HealthStatus.HEALTHY
            message = "ML models available"
            
            details = {
                'nba_model_loaded': True,
                'wnba_model_loaded': True,
                'model_cache_size': 2
            }
            
        except Exception as e:
            status = HealthStatus.CRITICAL
            message = f"Model availability check failed: {e}"
            details = {'error': str(e)}
        
        response_time = (time.time() - start_time) * 1000
        
        return HealthCheck(
            name="model_availability",
            status=status,
            message=message,
            response_time_ms=response_time,
            timestamp=datetime.now(),
            details=details
        )
    
    def _determine_overall_status(self, checks: List[HealthCheck]) -> HealthStatus:
        """Determine overall system status from individual checks"""
        if any(check.status == HealthStatus.CRITICAL for check in checks):
            return HealthStatus.CRITICAL
        elif any(check.status == HealthStatus.WARNING for check in checks):
            return HealthStatus.WARNING
        elif all(check.status == HealthStatus.HEALTHY for check in checks):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def _status_to_metric_value(self, status: HealthStatus) -> float:
        """Convert health status to metric value"""
        mapping = {
            HealthStatus.HEALTHY: 1.0,
            HealthStatus.WARNING: 0.5,
            HealthStatus.CRITICAL: 0.0,
            HealthStatus.UNKNOWN: -1.0
        }
        return mapping.get(status, -1.0)
    
    async def start_monitoring(self, interval_seconds: int = 30):
        """Start continuous health monitoring"""
        logger.info(f"🏥 Starting continuous health monitoring (interval: {interval_seconds}s)")
        
        while True:
            try:
                await self.perform_health_check()
                await asyncio.sleep(interval_seconds)
            except Exception as e:
                logger.error(f"❌ Health monitoring error: {e}")
                await asyncio.sleep(5)  # Short delay before retry

# Global health monitor instance
_health_monitor: Optional[ProductionHealthMonitor] = None

def get_health_monitor(config=None) -> ProductionHealthMonitor:
    """Get the global health monitor instance"""
    global _health_monitor
    
    if _health_monitor is None:
        _health_monitor = ProductionHealthMonitor(config)
    
    return _health_monitor

async def get_system_health() -> Dict[str, Any]:
    """Get current system health status"""
    monitor = get_health_monitor()
    health = await monitor.perform_health_check()
    return health.to_dict()

if __name__ == "__main__":
    # Test health monitoring
    async def test_health_monitor():
        monitor = ProductionHealthMonitor()
        health = await monitor.perform_health_check()
        for check in health.checks:
            print(f"✅ {check.name}: {check.status}")

    asyncio.run(test_health_monitor())
