import sqlite3
import os

print("🔍 Detailed Database Schema Check")
print("=" * 50)

# Check the main database
db_file = "hyper_medusa_consolidated.db"
if os.path.exists(db_file):
    print(f"✅ Found: {db_file}")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Check player_game_stats table specifically
    print("\n📊 player_game_stats table schema:")
    cursor.execute("PRAGMA table_info(player_game_stats)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"   {col[1]} ({col[2]}) - {col}")
    
    # Check a few sample records
    print("\n📋 Sample records from player_game_stats:")
    cursor.execute("SELECT * FROM player_game_stats LIMIT 3")
    rows = cursor.fetchall()
    for i, row in enumerate(rows):
        print(f"   Row {i+1}: {row[:10]}...")  # First 10 columns
    
    # Check players table
    print("\n📊 players table schema:")
    cursor.execute("PRAGMA table_info(players)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"   {col[1]} ({col[2]})")
    
    # Check games table
    print("\n📊 games table schema:")
    cursor.execute("PRAGMA table_info(games)")
    columns = cursor.fetchall()
    for col in columns:
        print(f"   {col[1]} ({col[2]})")
    
    conn.close()
else:
    print(f"❌ Not found: {db_file}")
