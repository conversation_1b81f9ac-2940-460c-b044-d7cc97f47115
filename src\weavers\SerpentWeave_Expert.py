import logging
import async<PERSON>
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from sklearn.cluster import KMeans, DBSCAN
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import warnings
from src.features.feature_feedback import FeatureFeedback
# Lazy import to avoid circular dependency
# from src.features.feature_alchemist import SelfLearningFeatureAlchemist
import sqlite3

"""
SerpentWeave_Expert.py
======================

Expert-level pattern weaving and multi-dimensional stream fusion system.
Specializes in weaving complex patterns across multiple data streams,
temporal analysis, and advanced predictive pattern recognition for HYPER MEDUSA NEURAL VAULT analytics.

Author: Cognitive Spires Expert System
"""


warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class PatternStream:
    """Individual pattern stream in the weave"""
    stream_id: str
    stream_type: str # 'temporal', 'spatial', 'statistical', 'behavioral'
    data_points: List[Dict[str, Any]]
    pattern_strength: float
    temporal_weight: float
    confidence: float
    last_updated: datetime = field(default_factory=datetime.now)


@dataclass
class WeavedPattern:
    """Weaved pattern from multiple streams"""
    pattern_id: str
    contributing_streams: List[str]
    pattern_type: str
    strength: float
    confidence: float
    frequency: float
    prediction_horizon: int # minutes
    key_features: List[str]
    emergence_time: datetime = field(default_factory=datetime.now)
    expiration_time: Optional[datetime] = None


@dataclass
class StreamFusion:
    """Result of stream fusion analysis"""
    fusion_id: str
    fused_streams: List[str]
    fusion_strength: float
    correlation_matrix: Dict[str, Dict[str, float]]
    dominant_patterns: List[str]
    anomaly_signals: List[str]
    fusion_confidence: float


class SerpentWeave_Expert:
    """
    Expert-level pattern weaving and stream fusion system.
    
    Features:
    - Multi-dimensional pattern stream analysis
    - Advanced temporal pattern recognition
    - Real-time stream fusion and correlation
    - Anomaly detection in pattern flows
    - Predictive pattern emergence modeling
    - Dynamic pattern strength adjustment
    """
    
    def __init__(self, max_streams: int = 20, pattern_memory_hours: int = 24, enable_pattern_detection: bool = True, **kwargs):
        self.max_streams = max_streams
        self.pattern_memory_hours = pattern_memory_hours
        self.active_streams = {}
        self.pattern_history = []
        self.weaved_patterns = {}
        self.fusion_cache = {}
        
        # Pattern analysis models
        self.pattern_clusterer = KMeans(n_clusters=8, random_state=42, n_init='auto') # Added n_init='auto'
        self.anomaly_detector = DBSCAN(eps=0.3, min_samples=3)
        self.scaler = StandardScaler()
        self.dimensionality_reducer = PCA(n_components=0.95) # Keep 95% variance
        
        # Weaving parameters
        self.correlation_threshold = 0.6
        self.pattern_strength_threshold = 0.7
        self.fusion_confidence_threshold = 0.65
        
        self._initialize_stream_patterns()
        logger.info(" MEDUSA VAULT: SerpentWeave_Expert initialized with advanced pattern analysis")
    
    def _initialize_stream_patterns(self):
        """Initialize base pattern streams"""
        try:
            # Create foundational pattern streams
            base_streams = [
                {
                    'stream_id': 'scoring_rhythm',
                    'stream_type': 'temporal',
                    'pattern_strength': 0.8,
                    'temporal_weight': 1.0,
                    'confidence': 0.85
                },
                {
                    'stream_id': 'momentum_flow',
                    'stream_type': 'behavioral',
                    'pattern_strength': 0.7,
                    'temporal_weight': 0.9,
                    'confidence': 0.75
                },
                {
                    'stream_id': 'defensive_pressure',
                    'stream_type': 'spatial',
                    'pattern_strength': 0.6,
                    'temporal_weight': 0.8,
                    'confidence': 0.7
                },
                {
                    'stream_id': 'statistical_trends',
                    'stream_type': 'statistical',
                    'pattern_strength': 0.9,
                    'temporal_weight': 0.7,
                    'confidence': 0.9
                }
            ]
            
            for stream_data in base_streams:
                stream = PatternStream(
                    stream_id=stream_data['stream_id'],
                    stream_type=stream_data['stream_type'],
                    data_points=self._get_real_pattern_data(stream_data['stream_type']),
                    pattern_strength=stream_data['pattern_strength'],
                    temporal_weight=stream_data['temporal_weight'],
                    confidence=stream_data['confidence']
                )
                self.active_streams[stream.stream_id] = stream
            
        
        except Exception as e:
            logger.error(f" Stream pattern initialization failed: {e}")
    
    def predict(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main prediction interface for pattern weaving and stream fusion.
        
        Args:
            input_data: Dictionary containing pattern analysis requirements
            
        Returns:
            Dictionary with weaved patterns and fusion analysis
        """
        # Lazy import to avoid circular dependency
        try:
            from src.features.feature_alchemist import SelfLearningFeatureAlchemist
            feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)
        except ImportError:
            feature_alchemist = None
        try:
            start_time = datetime.now()
            
            # Extract analysis parameters
            home_patterns = input_data.get('home_patterns', {})
            away_patterns = input_data.get('away_patterns', {})
            game_context = input_data.get('game_context', {})
            analysis_depth = input_data.get('analysis_depth', 'comprehensive')
            
            # Update streams with new pattern data
            self._update_pattern_streams(home_patterns, away_patterns, game_context)
            
            # Perform pattern weaving
            result = self._weave_patterns(analysis_depth)
            confidence = result.confidence if hasattr(result, 'confidence') else 1.0
            # --- Feedback wiring: send feedback if confidence is low ---
            if confidence < 0.3:
                feedback = FeatureFeedback(self.__class__.__name__, input_data, confidence, message="Low confidence. Requesting feature improvement.")
                feature_alchemist.receive_feedback(feedback)
            return {
                'pattern_weaving': {
                    'weaved_patterns': [p.__dict__ for p in result],
                    'active_streams': list(self.active_streams.keys()),
                    'stream_fusion': stream_fusion.__dict__ if stream_fusion else None,
                    'pattern_predictions': pattern_predictions,
                    'emergence_analysis': emergence_analysis
                },
                'anomaly_detection': {
                    'detected_anomalies': anomalies,
                    'anomaly_confidence': np.mean([a.get('confidence', 0.5) for a in anomalies]) if anomalies else 0.5,
                    'risk_level': anomaly_risk_level
                },
                'stream_analytics': stream_analytics,
                'pattern_recommendations': pattern_recommendations,
                'processing_time_seconds': processing_time,
                'weaving_timestamp': datetime.now().isoformat(),
                'serpent_version': '2.0_expert',
                'confidence': confidence,
            }
        except Exception as e:
            logger.error(f" SerpentWeave prediction failed: {e}")
            return self._get_fallback_prediction()
    
    def _update_pattern_streams(self, home_patterns: Dict, away_patterns: Dict, game_context: Dict):
        """Update pattern streams with new data"""
        try:
            current_time = datetime.now()
            
            # Process home team patterns
            if home_patterns:
                self._process_team_patterns('home_team', home_patterns, current_time)
            
            # Process away team patterns
            if away_patterns:
                self._process_team_patterns('away_team', away_patterns, current_time)
            
            # Process game context patterns
            if game_context:
                self._process_context_patterns(game_context, current_time)
            
            # Clean old pattern data
            self._clean_expired_patterns(current_time)
            
        
        except Exception as e:
            logger.error(f" Pattern stream update failed: {e}")
    
    def _process_team_patterns(self, team_prefix: str, patterns: Dict, timestamp: datetime):
        """Process patterns for a specific team"""
        try:
            # Create team-specific streams if they don't exist
            team_streams = [
                f'{team_prefix}_offensive_flow',
                f'{team_prefix}_defensive_sets',
                f'{team_prefix}_player_rotations',
                f'{team_prefix}_tempo_control'
            ]
            
            for stream_id in team_streams:
                if stream_id not in self.active_streams:
                    self.active_streams[stream_id] = PatternStream(
                        stream_id=stream_id,
                        stream_type='behavioral',
                        data_points=[],
                        pattern_strength=0.5,
                        temporal_weight=0.8,
                        confidence=0.6
                    )
                
                # Add pattern data point
                pattern_data = {
                    'timestamp': timestamp,
                    'pattern_values': patterns,
                    'team': team_prefix,
                    'strength': np.random.uniform(0.4, 0.9), # Mock strength calculation
                    'confidence': np.random.uniform(0.5, 0.8)
                }
                
                stream = self.active_streams[stream_id]
                stream.data_points.append(pattern_data)
                stream.last_updated = timestamp
                
                # Update stream metrics
                recent_strengths = [dp.get('strength', 0.5) for dp in stream.data_points[-10:]]
                stream.pattern_strength = np.mean(recent_strengths) if recent_strengths else 0.5
                
                recent_confidences = [dp.get('confidence', 0.5) for dp in stream.data_points[-10:]]
                stream.confidence = np.mean(recent_confidences) if recent_confidences else 0.5
            
        except Exception as e:
            logger.error(f" Team pattern processing failed: {e}")
    
    def _process_context_patterns(self, game_context: Dict, timestamp: datetime):
        """Process game context patterns"""
        try:
            context_streams = [
                'game_flow_context',
                'situational_pressure',
                'time_context',
                'score_differential_impact'
            ]
            
            for stream_id in context_streams:
                if stream_id not in self.active_streams:
                    self.active_streams[stream_id] = PatternStream(
                        stream_id=stream_id,
                        stream_type='temporal',
                        data_points=[],
                        pattern_strength=0.6,
                        temporal_weight=1.0,
                        confidence=0.7
                    )
                
                # Extract context-specific data
                context_data = {
                    'timestamp': timestamp,
                    'context_values': game_context,
                    'urgency_factor': game_context.get('urgency_factor', 0.5),
                    'pressure_level': game_context.get('pressure_level', 0.5),
                    'time_sensitivity': game_context.get('time_sensitivity', 0.5)
                }
                
                stream = self.active_streams[stream_id]
                stream.data_points.append(context_data)
                stream.last_updated = timestamp # Update temporal weight based on recency
                minutes_old = (timestamp - stream.last_updated).total_seconds() / 60
                stream.temporal_weight = max(0.1, 1.0 - minutes_old / 60) # Decay over hour
            
        except Exception as e:
            logger.error(f" Context pattern processing failed: {e}")
    
    def _clean_expired_patterns(self, current_time: datetime):
        """Clean expired pattern data from streams"""
        try:
            expiry_time = current_time - timedelta(hours=self.pattern_memory_hours)
            
            for stream in self.active_streams.values():
                # Remove expired data points
                valid_data_points = []
                for dp in stream.data_points:
                    timestamp = dp.get('timestamp', current_time)
                    # Convert string timestamp to datetime if needed
                    if isinstance(timestamp, str):
                        try:
                            timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                        except (ValueError, AttributeError):
                            # If parsing fails, keep the data point (assume it's recent)
                            timestamp = current_time
                    
                    if timestamp > expiry_time:
                        valid_data_points.append(dp)
                
                stream.data_points = valid_data_points
                
                # Remove empty streams
                empty_streams = [
                    stream_id for stream_id, stream in self.active_streams.items()
                    if not stream.data_points
                ]
                for stream_id in empty_streams:
                    if stream_id not in ['scoring_rhythm', 'momentum_flow', 'defensive_pressure', 'statistical_trends']:
                        del self.active_streams[stream_id]
            
        except Exception as e:
            logger.error(f" Pattern cleanup failed: {e}")
    
    def _weave_patterns(self, analysis_depth: str) -> List[WeavedPattern]:
        """Weave patterns from multiple streams"""
        weaved_patterns = []
        
        try:
            # Get streams with sufficient data
            active_streams = [
                stream for stream in self.active_streams.values()
                if len(stream.data_points) >= 3 and stream.confidence > 0.5
            ]
            
            if len(active_streams) < 2:
                logger.warning(" Insufficient streams for pattern weaving")
                return weaved_patterns
            
            # Analyze stream correlations
            correlations = self._calculate_stream_correlations(active_streams)
            
            # Identify pattern clusters
            pattern_clusters = self._identify_pattern_clusters(active_streams)
            
            # Generate weaved patterns
            weaved_patterns = self._generate_weaved_patterns(
                active_streams, correlations, pattern_clusters, analysis_depth
            )
            
            # Filter and rank patterns
            filtered_patterns = self._filter_patterns_by_strength(weaved_patterns)
            
            return filtered_patterns
        
        except Exception as e:
            logger.error(f" Pattern weaving failed: {e}")
            return weaved_patterns
    
    def _calculate_stream_correlations(self, streams: List[PatternStream]) -> Dict[str, Dict[str, float]]:
        """Calculate correlations between pattern streams"""
        correlations = {}
        
        try:
            for i, stream1 in enumerate(streams):
                correlations[stream1.stream_id] = {}
                
                for j, stream2 in enumerate(streams):
                    if i != j:
                        # Extract numerical features for correlation
                        features1 = self._extract_stream_features(stream1)
                        features2 = self._extract_stream_features(stream2)
                        
                        if len(features1) > 1 and len(features2) > 1:
                            # Calculate correlation
                            min_length = min(len(features1), len(features2))
                            correlation_matrix = np.corrcoef(
                                features1[:min_length], 
                                features2[:min_length]
                            )
                            correlation = correlation_matrix[0, 1]
                            
                            # Handle NaN correlations (e.g., if data is constant)
                            if np.isnan(correlation):
                                correlation = 0.0
                            
                            correlations[stream1.stream_id][stream2.stream_id] = correlation
                        else:
                            correlations[stream1.stream_id][stream2.stream_id] = 0.0
                    else:
                        correlations[stream1.stream_id][stream2.stream_id] = 1.0
            
            return correlations
        
        except Exception as e:
            logger.error(f" Stream correlation calculation failed: {e}")
            return {}
    
    def _extract_stream_features(self, stream: PatternStream) -> List[float]:
        """Extract numerical features from a pattern stream"""
        features = []
        
        try:
            for data_point in stream.data_points:
                # Extract numerical values from data point
                if 'strength' in data_point:
                    features.append(data_point['strength'])
                elif 'pattern_values' in data_point:
                    values = data_point['pattern_values']
                    if isinstance(values, dict):
                        numerical_values = [v for v in values.values() if isinstance(v, (int, float))]
                        if numerical_values:
                            features.append(np.mean(numerical_values))
                        else:
                            features.append(0.5) # Default
                    else:
                        features.append(0.5)
                else:
                    features.append(stream.pattern_strength)
            
            return features
        
        except Exception as e:
            logger.error(f" Feature extraction failed for stream {stream.stream_id}: {e}")
            return [0.5] * len(stream.data_points) if stream.data_points else [0.5] # Default features
    
    def _identify_pattern_clusters(self, streams: List[PatternStream]) -> Dict[str, List[str]]:
        """Identify clusters of similar patterns"""
        clusters = {}
        
        try:
            if len(streams) < 3:
                return clusters
            
            # Prepare feature matrix
            feature_matrix = []
            stream_ids = []
            
            for stream in streams:
                features = self._extract_stream_features(stream)
                if features:
                    # Use statistical features of the stream
                    # Ensure numerical stability for std if features is empty or has one element
                    std_dev = np.std(features) if len(features) > 1 else 0.0
                    
                    stream_features = [
                        np.mean(features) if features else 0.5,
                        std_dev,
                        np.max(features) if features else 0.5,
                        np.min(features) if features else 0.5,
                        stream.pattern_strength,
                        stream.confidence,
                        stream.temporal_weight
                    ]
                    feature_matrix.append(stream_features)
                    stream_ids.append(stream.stream_id)
            
            if len(feature_matrix) < 3:
                return clusters
            
            # Scale features
            feature_matrix = self.scaler.fit_transform(feature_matrix)
            
            # Perform clustering
            n_clusters = min(4, len(streams) // 2) # Reasonable number of clusters
            # Ensure n_clusters is at least 1 and not greater than the number of samples
            n_clusters = max(1, min(n_clusters, len(feature_matrix) - 1))
            
            clusterer = KMeans(n_clusters=n_clusters, random_state=42, n_init='auto')
            cluster_labels = clusterer.fit_predict(feature_matrix)
            
            # Group streams by cluster
            for i, label in enumerate(cluster_labels):
                cluster_key = f'cluster_{label}'
                if cluster_key not in clusters:
                    clusters[cluster_key] = []
                clusters[cluster_key].append(stream_ids[i])
            
            return clusters
        
        except Exception as e:
            logger.error(f" Pattern clustering failed: {e}")
            return {}
    
    def _generate_weaved_patterns(self, streams: List[PatternStream], 
                                  correlations: Dict[str, Dict[str, float]],
                                  clusters: Dict[str, List[str]], 
                                  analysis_depth: str) -> List[WeavedPattern]:
        """Generate weaved patterns from stream analysis"""
        weaved_patterns = []
        
        try:
            pattern_counter = 0
            
            # Generate patterns from high correlations
            for stream1_id, stream_correlations in correlations.items():
                for stream2_id, correlation in stream_correlations.items():
                    if (correlation > self.correlation_threshold and 
                        stream1_id < stream2_id): # Avoid duplicates
                        
                        pattern_counter += 1
                        pattern = self._create_correlation_pattern(
                            stream1_id, stream2_id, correlation, streams, pattern_counter
                        )
                        if pattern:
                            weaved_patterns.append(pattern)
            
            # Generate patterns from clusters
            for cluster_name, cluster_streams in clusters.items():
                if len(cluster_streams) >= 2:
                    pattern_counter += 1
                    pattern = self._create_cluster_pattern(
                        cluster_streams, streams, pattern_counter, cluster_name
                    )
                    if pattern:
                        weaved_patterns.append(pattern)
            
            # Generate temporal patterns if deep analysis
            if analysis_depth in ['comprehensive', 'deep']:
                temporal_patterns = self._generate_temporal_patterns(streams, pattern_counter)
                weaved_patterns.extend(temporal_patterns)
            
            return weaved_patterns
        
        except Exception as e:
            logger.error(f" Weaved pattern generation failed: {e}")
            return weaved_patterns
    
    def _create_correlation_pattern(self, stream1_id: str, stream2_id: str, 
                                    correlation: float, streams: List[PatternStream],
                                    pattern_id: int) -> Optional[WeavedPattern]:
        """Create a pattern from correlated streams"""
        try:
            # Find the streams
            stream1 = next((s for s in streams if s.stream_id == stream1_id), None)
            stream2 = next((s for s in streams if s.stream_id == stream2_id), None)
            
            if not stream1 or not stream2:
                return None
            
            # Calculate pattern strength
            strength = (abs(correlation) + stream1.pattern_strength + stream2.pattern_strength) / 3
            
            # Calculate confidence
            confidence = (stream1.confidence + stream2.confidence) / 2
            
            # Determine pattern type
            pattern_type = self._determine_pattern_type(stream1, stream2)
            
            # Calculate frequency (based on data points)
            frequency = min(len(stream1.data_points), len(stream2.data_points)) / 10.0 if len(stream1.data_points) > 0 and len(stream2.data_points) > 0 else 0.0
            
            return WeavedPattern(
                pattern_id=f"correlation_pattern_{pattern_id}",
                contributing_streams=[stream1_id, stream2_id],
                pattern_type=pattern_type,
                strength=strength,
                confidence=confidence,
                frequency=frequency,
                prediction_horizon=15, # 15 minutes
                key_features=[f"{stream1_id}_correlation", f"{stream2_id}_correlation"],
                expiration_time=datetime.now() + timedelta(hours=2)
            )
        
        except Exception as e:
            logger.error(f" Correlation pattern creation failed: {e}")
            return None
    
    def _create_cluster_pattern(self, cluster_streams: List[str], 
                                streams: List[PatternStream],
                                pattern_id: int, cluster_name: str) -> Optional[WeavedPattern]:
        """Create a pattern from clustered streams"""
        try:
            # Find streams in cluster
            cluster_stream_objects = [
                s for s in streams if s.stream_id in cluster_streams
            ]
            
            if not cluster_stream_objects:
                return None
            
            # Calculate aggregate metrics
            avg_strength = np.mean([s.pattern_strength for s in cluster_stream_objects]) if cluster_stream_objects else 0.5
            avg_confidence = np.mean([s.confidence for s in cluster_stream_objects]) if cluster_stream_objects else 0.5
            total_data_points = sum(len(s.data_points) for s in cluster_stream_objects)
            
            # Determine cluster pattern type
            stream_types = [s.stream_type for s in cluster_stream_objects]
            if len(set(stream_types)) == 1:
                pattern_type = f"{stream_types[0]}_cluster"
            else:
                pattern_type = "mixed_cluster"
            
            return WeavedPattern(
                pattern_id=f"cluster_pattern_{pattern_id}",
                contributing_streams=cluster_streams,
                pattern_type=pattern_type,
                strength=avg_strength,
                confidence=avg_confidence,
                frequency=total_data_points / (len(cluster_stream_objects) * 10.0) if len(cluster_stream_objects) > 0 else 0.0,
                prediction_horizon=20, # 20 minutes
                key_features=[f"{cluster_name}_coherence", "multi_stream_alignment"],
                expiration_time=datetime.now() + timedelta(hours=1)
            )
        
        except Exception as e:
            logger.error(f" Cluster pattern creation failed: {e}")
            return None
    
    def _generate_temporal_patterns(self, streams: List[PatternStream], 
                                    pattern_counter: int) -> List[WeavedPattern]:
        """Generate temporal patterns from stream analysis"""
        temporal_patterns = []
        
        try:
            # Find streams with temporal characteristics
            temporal_streams = [s for s in streams if s.stream_type in ['temporal', 'behavioral']]
            
            if len(temporal_streams) < 2:
                return temporal_patterns
            
            # Analyze temporal trends
            for stream in temporal_streams:
                features = self._extract_stream_features(stream)
                if len(features) >= 5:
                    # Check for trending patterns
                    trend_strength = self._calculate_trend_strength(features)
                    
                    if abs(trend_strength) > 0.3: # Significant trend
                        pattern_counter += 1
                        temporal_pattern = WeavedPattern(
                            pattern_id=f"temporal_pattern_{pattern_counter}",
                            contributing_streams=[stream.stream_id],
                            pattern_type=f"temporal_trend_{'up' if trend_strength > 0 else 'down'}",
                            strength=abs(trend_strength),
                            confidence=stream.confidence * 0.9, # Slightly lower for temporal
                            frequency=len(features) / 20.0,
                            prediction_horizon=30, # 30 minutes
                            key_features=[f"{stream.stream_id}_trend", "temporal_momentum"],
                            expiration_time=datetime.now() + timedelta(minutes=45)
                        )
                        temporal_patterns.append(temporal_pattern)
            
            return temporal_patterns
        
        except Exception as e:
            logger.error(f" Temporal pattern generation failed: {e}")
            return []
    
    def _calculate_trend_strength(self, features: List[float]) -> float:
        """Calculate trend strength from feature sequence"""
        try:
            if len(features) < 2: # Need at least two points for a trend
                return 0.0
            
            # Simple linear trend calculation
            x = np.arange(len(features))
            correlation = np.corrcoef(x, features)[0, 1]
            
            return correlation if not np.isnan(correlation) else 0.0
        
        except Exception:
            return 0.0
    
    def _determine_pattern_type(self, stream1: PatternStream, stream2: PatternStream) -> str:
        """Determine pattern type from stream combination"""
        type_combinations = {
            ('temporal', 'temporal'): 'temporal_sync',
            ('temporal', 'behavioral'): 'tempo_behavior',
            ('temporal', 'spatial'): 'spatiotemporal',
            ('temporal', 'statistical'): 'trend_stats',
            ('behavioral', 'behavioral'): 'behavioral_sync',
            ('behavioral', 'spatial'): 'tactical_flow',
            ('behavioral', 'statistical'): 'performance_behavior',
            ('spatial', 'spatial'): 'spatial_coordination',
            ('spatial', 'statistical'): 'positional_stats',
            ('statistical', 'statistical'): 'statistical_convergence'
        }
        
        type_key = tuple(sorted([stream1.stream_type, stream2.stream_type]))
        return type_combinations.get(type_key, 'mixed_pattern')
    
    def _filter_patterns_by_strength(self, patterns: List[WeavedPattern]) -> List[WeavedPattern]:
        """Filter patterns by strength and confidence thresholds"""
        filtered = []
        
        for pattern in patterns:
            if (pattern.strength >= self.pattern_strength_threshold and 
                pattern.confidence >= 0.6):
                filtered.append(pattern)
        
        # Sort by combined strength and confidence
        filtered.sort(key=lambda p: (p.strength + p.confidence) / 2, reverse=True)
        
        # Return top patterns
        return filtered[:15]
    
    def _fuse_streams(self) -> Optional[StreamFusion]:
        """Perform advanced stream fusion analysis"""
        try:
            if len(self.active_streams) < 3:
                return None
            
            # Select streams for fusion
            fusion_streams = [
                stream for stream in self.active_streams.values()
                if stream.confidence > 0.6 and len(stream.data_points) >= 3
            ]
            
            if len(fusion_streams) < 3:
                return None
            
            # Calculate fusion correlation matrix
            correlations = self._calculate_stream_correlations(fusion_streams)
            
            # Calculate fusion strength
            all_correlations = []
            for stream_corr in correlations.values():
                all_correlations.extend([abs(corr) for corr in stream_corr.values() if corr != 1.0])
            
            fusion_strength = np.mean(all_correlations) if all_correlations else 0.0
            
            # Identify dominant patterns
            dominant_patterns = self._identify_dominant_patterns(fusion_streams)
            
            # Detect anomaly signals
            anomaly_signals = self._detect_fusion_anomalies(fusion_streams)
            
            # Calculate fusion confidence
            fusion_confidence = min(0.95, fusion_strength * 1.2)
            
            if fusion_confidence >= self.fusion_confidence_threshold:
                return StreamFusion(
                    fusion_id=f"fusion_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    fused_streams=[s.stream_id for s in fusion_streams],
                    fusion_strength=fusion_strength,
                    correlation_matrix=correlations,
                    dominant_patterns=dominant_patterns,
                    anomaly_signals=anomaly_signals,
                    fusion_confidence=fusion_confidence
                )
            
            return None
        
        except Exception as e:
            logger.error(f" Stream fusion failed: {e}")
            return None
    
    def _identify_dominant_patterns(self, streams: List[PatternStream]) -> List[str]:
        """Identify dominant patterns in fusion"""
        dominant = []
        
        try:
            # Sort streams by strength
            sorted_streams = sorted(streams, key=lambda s: s.pattern_strength, reverse=True)
            
            # Top 3 streams are considered dominant
            for stream in sorted_streams[:3]:
                if stream.pattern_strength > 0.7:
                    dominant.append(f"{stream.stream_id}_dominance")
            
            return dominant
        
        except Exception as e:
            logger.error(f" Dominant pattern identification failed: {e}")
            return []
    
    def _detect_fusion_anomalies(self, streams: List[PatternStream]) -> List[str]:
        """Detect anomalies in stream fusion"""
        anomalies = []
        
        try:
            # Check for strength imbalances
            strengths = [s.pattern_strength for s in streams]
            if strengths and np.std(strengths) > 0.4:
                anomalies.append("strength_imbalance")
            
            # Check for confidence disparities
            confidences = [s.confidence for s in streams]
            if confidences and np.std(confidences) > 0.3:
                anomalies.append("confidence_disparity")
            
            # Check for temporal misalignment
            temporal_weights = [s.temporal_weight for s in streams]
            if temporal_weights and np.std(temporal_weights) > 0.4:
                anomalies.append("temporal_misalignment")
            
            return anomalies
        
        except Exception as e:
            logger.error(f" Fusion anomaly detection failed: {e}")
            return []
    
    def _detect_pattern_anomalies(self) -> List[Dict[str, Any]]:
        """Detect anomalies in pattern streams"""
        anomalies = []
        
        try:
            for stream_id, stream in self.active_streams.items():
                features = self._extract_stream_features(stream)
                
                if len(features) >= 5:
                    # Statistical anomaly detection
                    mean_val = np.mean(features)
                    std_val = np.std(features)
                    
                    if std_val == 0: # Avoid division by zero if all values are identical
                        continue
                    
                    for i, value in enumerate(features[-3:]): # Check last 3 points
                        z_score = abs((value - mean_val) / std_val)
                        
                        if z_score > 2.5: # 2.5 sigma outlier
                            anomalies.append({
                                'stream_id': stream_id,
                                'anomaly_type': 'statistical_outlier',
                                'value': value,
                                'expected_range': [mean_val - 2*std_val, mean_val + 2*std_val],
                                'confidence': 0.8,
                                'severity': 'high' if z_score > 3.0 else 'medium'
                            })
            
            return anomalies
        
        except Exception as e:
            logger.error(f" Anomaly detection failed: {e}")
            return []
    
    def _generate_pattern_predictions(self, patterns: List[WeavedPattern], 
                                      fusion: Optional[StreamFusion]) -> Dict[str, Any]:
        """Generate predictions based on weaved patterns"""
        predictions = {
            'pattern_evolution': [],
            'fusion_forecast': None,
            'confidence_distribution': {},
            'prediction_accuracy_estimate': 0.0
        }
        
        try:
            if not patterns:
                return predictions
            
            # Pattern evolution predictions
            for pattern in patterns[:5]: # Top 5 patterns
                evolution_pred = {
                    'pattern_id': pattern.pattern_id,
                    'evolution_direction': 'strengthening' if pattern.strength > 0.8 else 'weakening',
                    'time_to_peak': pattern.prediction_horizon,
                    'peak_strength_estimate': min(1.0, pattern.strength * 1.2),
                    'decay_estimate': pattern.prediction_horizon * 2
                }
                predictions['pattern_evolution'].append(evolution_pred)
            
            # Fusion forecast
            if fusion:
                predictions['fusion_forecast'] = {
                    'fusion_trajectory': 'strengthening' if fusion.fusion_strength > 0.7 else 'stabilizing',
                    'coherence_forecast': min(1.0, fusion.fusion_confidence * 1.1),
                    'dominant_pattern_persistence': len(fusion.dominant_patterns) * 10 # minutes
                }
            
            # Confidence distribution
            confidences = [p.confidence for p in patterns]
            predictions['confidence_distribution'] = {
                'high_confidence': len([c for c in confidences if c > 0.8]),
                'medium_confidence': len([c for c in confidences if 0.6 <= c <= 0.8]),
                'low_confidence': len([c for c in confidences if c < 0.6]),
                'average_confidence': np.mean(confidences) if confidences else 0.5
            }
            
            # Accuracy estimate
            predictions['prediction_accuracy_estimate'] = min(0.95, np.mean(confidences) * 1.1) if confidences else 0.5
            
            return predictions
        
        except Exception as e:
            logger.error(f" Pattern prediction generation failed: {e}")
            return predictions
    
    def _analyze_pattern_emergence(self, patterns: List[WeavedPattern]) -> Dict[str, Any]:
        """Analyze pattern emergence characteristics"""
        analysis = {
            'emergence_rate': 0.0,
            'pattern_lifecycle': {},
            'emergence_triggers': [],
            'stability_indicators': []
        }
        
        try:
            if not patterns:
                return analysis
            
            # Calculate emergence rate
            recent_patterns = [p for p in patterns if 
                               (datetime.now() - p.emergence_time).total_seconds() < 3600] # Last hour
            analysis['emergence_rate'] = len(recent_patterns) / max(1, len(patterns))
            
            # Pattern lifecycle analysis
            for pattern in patterns:
                age_minutes = (datetime.now() - pattern.emergence_time).total_seconds() / 60
                lifecycle_stage = 'emerging' if age_minutes < 15 else 'mature' if age_minutes < 60 else 'aging'
                
                if lifecycle_stage not in analysis['pattern_lifecycle']:
                    analysis['pattern_lifecycle'][lifecycle_stage] = 0
                analysis['pattern_lifecycle'][lifecycle_stage] += 1
            
            # Emergence triggers
            pattern_types = [p.pattern_type for p in patterns]
            type_counts = {ptype: pattern_types.count(ptype) for ptype in set(pattern_types)}
            analysis['emergence_triggers'] = [
                f"{ptype}_trigger" for ptype, count in type_counts.items() if count > 1
            ]
            
            # Stability indicators
            avg_strength = np.mean([p.strength for p in patterns]) if patterns else 0.5
            if avg_strength > 0.8:
                analysis['stability_indicators'].append('high_pattern_strength')
            
            avg_confidence = np.mean([p.confidence for p in patterns]) if patterns else 0.5
            if avg_confidence > 0.75:
                analysis['stability_indicators'].append('high_confidence_patterns')
            
            return analysis
        
        except Exception as e:
            logger.error(f" Pattern emergence analysis failed: {e}")
            return analysis
    
    def _assess_anomaly_risk(self, anomalies: List[Dict[str, Any]]) -> str:
        """Assess overall risk level from detected anomalies"""
        if not anomalies:
            return 'low'
        
        high_severity_count = len([a for a in anomalies if a.get('severity') == 'high'])
        medium_severity_count = len([a for a in anomalies if a.get('severity') == 'medium'])
        
        if high_severity_count > 0:
            return 'high'
        elif medium_severity_count > 2:
            return 'medium'
        elif len(anomalies) > 5:
            return 'medium'
        else:
            return 'low'
    
    def _generate_stream_analytics(self) -> Dict[str, Any]:
        """Generate analytics about current stream state"""
        active_stream_values = list(self.active_streams.values())
        return {
            'total_active_streams': len(active_stream_values),
            'stream_types': {
                stream_type: len([s for s in active_stream_values if s.stream_type == stream_type])
                for stream_type in ['temporal', 'spatial', 'statistical', 'behavioral']
            },
            'average_stream_strength': np.mean([s.pattern_strength for s in active_stream_values]) if active_stream_values else 0.5,
            'average_stream_confidence': np.mean([s.confidence for s in active_stream_values]) if active_stream_values else 0.5,
            'data_richness': np.mean([len(s.data_points) for s in active_stream_values]) if active_stream_values else 0.0,
            'temporal_alignment': np.mean([s.temporal_weight for s in active_stream_values]) if active_stream_values else 0.0
        }
    
    def _generate_pattern_recommendations(self, patterns: List[WeavedPattern]) -> List[str]:
        """Generate recommendations based on pattern analysis"""
        recommendations = []
        
        try:
            if not patterns:
                recommendations.append("🔍 Increase pattern stream data collection")
                return recommendations
            
            # Strength-based recommendations
            avg_strength = np.mean([p.strength for p in patterns]) if patterns else 0.5
            if avg_strength > 0.85:
                recommendations.append("💪 Strong patterns detected - consider aggressive positioning")
            elif avg_strength < 0.6:
                recommendations.append(" Weak patterns - increase analysis depth")
            
            # Confidence-based recommendations
            avg_confidence = np.mean([p.confidence for p in patterns]) if patterns else 0.5
            if avg_confidence > 0.8:
                recommendations.append(" High confidence patterns - excellent for decision making")
            elif avg_confidence < 0.65:
                recommendations.append(" Lower confidence - gather more validation data")
            
            # Pattern diversity recommendations
            unique_types = len(set(p.pattern_type for p in patterns))
            if unique_types > 4:
                recommendations.append("🌈 Rich pattern diversity detected")
            elif unique_types < 2:
                recommendations.append(" Limited pattern types - expand analysis scope")
            # Temporal recommendations
            short_horizon_patterns = [p for p in patterns if p.prediction_horizon < 20]
            if len(short_horizon_patterns) > len(patterns) * 0.7:
                recommendations.append(" Many short-term patterns - consider real-time decisions")
            
            if not recommendations:
                recommendations.append(" Pattern analysis completed successfully")
            
            return recommendations
        
        except Exception as e:
            logger.error(f" Recommendation generation failed: {e}")
            return [" Unable to generate recommendations"]
    
    def _get_real_pattern_data(self, stream_type: str) -> List[Dict]:
        """Get real pattern data from database instead of mock data"""
        try:
            
            # Connect to database and get real game data for pattern analysis
            conn = sqlite3.connect('medusa_vault.db')
            cursor = conn.cursor()
            # Query for recent game data to extract patterns
            cursor.execute("""
                SELECT home_team_name, away_team_name, home_team_score, away_team_score, game_date
                FROM nba_games 
                WHERE game_date >= date('now', '-30 days')
                AND home_team_score IS NOT NULL AND away_team_score IS NOT NULL
                ORDER BY game_date DESC
                LIMIT 50
            """)
            
            games = cursor.fetchall()
            conn.close()
            # Convert to pattern data
            pattern_data = []
            for home_team_name, away_team_name, home_team_score, away_team_score, game_date in games:
                # Calculate various patterns from real data
                total_score = (home_team_score or 0) + (away_team_score or 0)
                scoring_rate = total_score / 48.0 if total_score > 0 else 2.0 # points per minute
                margin = abs((home_team_score or 0) - (away_team_score or 0))
                
                pattern_data.append({
                    'timestamp': game_date,
                    'value': scoring_rate,
                    'intensity': min(1.0, margin / 20.0), # Normalize margin to intensity
                    'trend': 'positive' if (home_team_score or 0) > (away_team_score or 0) else 'negative',
                    'confidence': 0.8, # Based on data recency
                    'teams': f"{home_team_name} vs {away_team_name}",
                    'pattern_type': stream_type,
                    'data_source': 'real_game_data'
                })
            
            # If no real data, return basic template
            if not pattern_data:
                pattern_data = [{
                    'timestamp': '2024-01-01',
                    'value': 2.2,
                    'intensity': 0.5,
                    'trend': 'neutral',
                    'confidence': 0.5,
                    'teams': 'Default',
                    'pattern_type': stream_type,
                    'data_source': 'fallback'
                }]
            
            return pattern_data[:20] # Return most recent 20 patterns
        
        except Exception as e:
            logger.warning(f" TITAN PROCESSING FAILED: get real pattern data: {e}")
            # Return basic fallback pattern
            return [{
                'timestamp': '2024-01-01',
                'value': 2.0,
                'intensity': 0.5,
                'trend': 'neutral',
                'confidence': 0.5,
                'teams': 'Fallback',
                'pattern_type': stream_type,
                'data_source': 'error_fallback'
            }]
    
    def _get_fallback_weaving(self) -> Dict[str, Any]:
        """Fallback weaving when main prediction fails"""
        return {
            'pattern_weaving': {
                'weaved_patterns': [],
                'active_streams': list(self.active_streams.keys()),
                'stream_fusion': None,
                'pattern_predictions': {
                    'pattern_evolution': [],
                    'fusion_forecast': None,
                    'confidence_distribution': {'average_confidence': 0.3},
                    'prediction_accuracy_estimate': 0.3
                },
                'emergence_analysis': {
                    'emergence_rate': 0.0,
                    'pattern_lifecycle': {},
                    'emergence_triggers': [],
                    'stability_indicators': []
                }
            },
            'anomaly_detection': {
                'detected_anomalies': [],
                'anomaly_confidence': 0.0,
                'risk_level': 'unknown'
            },
            'stream_analytics': {
                'total_active_streams': len(self.active_streams),
                'stream_types': {
                    stream_type: len([s for s in self.active_streams.values() if s.stream_type == stream_type])
                    for stream_type in ['temporal', 'spatial', 'statistical', 'behavioral']
                },
                'average_stream_strength': 0.5,
                'average_stream_confidence': 0.5,
                'data_richness': 0.0,
                'temporal_alignment': 0.0
            },
            'pattern_recommendations': ['🚨 Pattern weaving system requires attention'],
            'processing_time_seconds': 0.01,
            'weaving_timestamp': datetime.now().isoformat(),
            'serpent_version': '2.0_expert_fallback'
        }
    
    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """
        Self-learning and feedback-driven adaptation for SerpentWeave_Expert.
        Adjusts pattern detection, fusion logic, or other parameters based on feedback from the War Council, spires, or system performance.
        """
        if feedback:
            logger.info(f"[SerpentWeave_Expert] Received feedback: {feedback}")
            # Example: Adjust pattern strength or detection thresholds
            if 'pattern_strength_updates' in feedback:
                for pattern_id, strength in feedback['pattern_strength_updates'].items():
                    for pattern in self.weaved_patterns:
                        if pattern.pattern_id == pattern_id:
                            pattern.strength = strength
            # Log feedback for meta-learning
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)
            logger.info(f"[SerpentWeave_Expert] Feedback processed and logged. Current feedback log size: {len(self.feedback_log)}")


# Compatibility functions for legacy integration
def weave_patterns(pattern_data: Dict[str, Any]) -> Dict[str, Any]:
    """Legacy compatibility function"""
    weaver = SerpentWeave_Expert()
    mock_input = {
        'home_patterns': pattern_data.get('home_patterns', {}),
        'away_patterns': pattern_data.get('away_patterns', {}),
        'game_context': pattern_data.get('game_context', {}),
        'analysis_depth': 'comprehensive'
    }
    return weaver.predict(mock_input)


if __name__ == "__main__":
    # Test the expert serpent weave
    weaver = SerpentWeave_Expert()
    
    test_data = {
        'home_patterns': {
            'offensive_rhythm': 0.78,
            'defensive_intensity': 0.65,
            'pace_factor': 0.82
        },
        'away_patterns': {
            'offensive_rhythm': 0.72,
            'defensive_intensity': 0.88,
            'pace_factor': 0.69
        },
        'game_context': {
            'time_pressure': 0.75,
            'score_differential': 5,
            'momentum_shift': 0.3
        },
        'analysis_depth': 'comprehensive'
    }
    
    result = weaver.predict(test_data)
    
    weaving = result['pattern_weaving']
    anomaly_data = result['anomaly_detection']
    stream_analytics = result['stream_analytics']
    recommendations = result['pattern_recommendations']

