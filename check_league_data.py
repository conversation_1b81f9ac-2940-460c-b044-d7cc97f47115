import sqlite3
import os

print("🔍 Checking League Data in player_game_stats")
print("=" * 50)

db_file = "hyper_medusa_consolidated.db"
if os.path.exists(db_file):
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Check league column values
    print("📊 League column analysis:")
    cursor.execute("SELECT league, COUNT(*) FROM player_game_stats GROUP BY league ORDER BY COUNT(*) DESC")
    league_counts = cursor.fetchall()
    for league, count in league_counts:
        print(f"   '{league}': {count:,} records")
    
    # Check sample records
    print("\n📋 Sample records:")
    cursor.execute("SELECT hero_id, league, points, season, date FROM player_game_stats WHERE points > 0 LIMIT 5")
    rows = cursor.fetchall()
    for i, row in enumerate(rows):
        print(f"   Row {i+1}: hero_id={row[0]}, league='{row[1]}', points={row[2]}, season='{row[3]}', date='{row[4]}'")
    
    # Check if we can join with players table for league info
    print("\n🔗 Checking join with players table:")
    cursor.execute("""
        SELECT p.league, COUNT(*) 
        FROM player_game_stats pgs 
        JOIN players p ON pgs.hero_id = p.id 
        WHERE p.league IS NOT NULL 
        GROUP BY p.league 
        ORDER BY COUNT(*) DESC
    """)
    player_league_counts = cursor.fetchall()
    for league, count in player_league_counts:
        print(f"   Player league '{league}': {count:,} game records")
    
    conn.close()
