import sqlite3
import os

print("🔍 Database Schema Check")
print("=" * 40)

# Check the main database
db_file = "hyper_medusa_consolidated.db"
if os.path.exists(db_file):
    print(f"✅ Found: {db_file}")
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [t[0] for t in cursor.fetchall()]
    print(f"📊 Tables: {tables}")
    
    # Check each table
    for table in tables[:3]:  # First 3 tables
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"   {table}: {count:,} records")
        
        # Check columns
        cursor.execute(f"PRAGMA table_info({table})")
        columns = [col[1] for col in cursor.fetchall()]
        print(f"   Columns: {columns[:5]}...")
    
    conn.close()
else:
    print(f"❌ Not found: {db_file}")

# Check the data directory database
db_file2 = "data/unified_nba_wnba_data.db"
if os.path.exists(db_file2):
    print(f"\n✅ Found: {db_file2}")
    conn = sqlite3.connect(db_file2)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [t[0] for t in cursor.fetchall()]
    print(f"📊 Tables: {tables}")
    
    # Check each table
    for table in tables[:3]:  # First 3 tables
        cursor.execute(f"SELECT COUNT(*) FROM {table}")
        count = cursor.fetchone()[0]
        print(f"   {table}: {count:,} records")
    
    conn.close()
else:
    print(f"❌ Not found: {db_file2}")
