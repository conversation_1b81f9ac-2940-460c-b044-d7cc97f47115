import logging
import importlib
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime
from src.model_forge.UnifiedModelForge import get_unified_model_forge

"""
CognitiveSpiresFactory_Expert.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

Expert-Level Cognitive Spires Factory
A centralized factory for creating and managing all expert-level cognitive spires.

Features:
- Lazy loading of spires
- Validation and health checks
- Expert-level configuration
- Basketball intelligence integration
"""

# UNIFIED FORGE MIGRATION: Use UnifiedModelForge for all model forging

logger = logging.getLogger(__name__)


@dataclass
class SpireStatus:
    """Status information for a cognitive spire"""
    name: str
    loaded: bool
    error_message: Optional[str] = None
    load_time: Optional[datetime] = None
    instance: Any = None


class CognitiveSpiresFactory_Expert:
    """
    Expert-Level Cognitive Spires Factory
    
    Manages the creation, loading, and validation of all expert cognitive spires.
    """
    
    def __init__(self):
        """Initialize the Expert Cognitive Spires Factory"""
        self.spires: Dict[str, SpireStatus] = {}
        self.initialized = False
        logger.info(" MEDUSA VAULT: 🏭 Initializing CognitiveSpiresFactory_Expert...")
        self._initialize_spires()
        logger.info(" MEDUSA VAULT: CognitiveSpiresFactory_Expert initialized successfully")
    
    def _initialize_spires(self):
        """Initialize all expert cognitive spires"""
        spire_definitions = [
            ("ChronosOracle", "ChronosFatigueOracle_Expert", "ChronosFatigueOracle_Expert"),
            ("NikeOracle", "NikeVictoryOracle_Expert", "NikeVictoryOracle_Expert"),
            ("AthenaOracle", "AthenaStrategyEngine_Expert", "AthenaStrategyEngine_Expert"),
            ("MetisOracle", "MetisOracle_Expert", "MetisOracle_Expert"),
            ("AresOracle", "AresDefenseOracle_Expert", "AresDefenseOracle_Expert"),
            # ("FateForge", "FateForge_Expert", "FateForge_Expert"),  # Deprecated: Use UnifiedModelForge with FateArchetypeStrategy
            ("FateWeaver", "FateWeaver_Expert", "FateWeaver_Expert"),
            ("GorgonWeave", "GorgonWeave_Expert", "GorgonWeave_Expert"),
            # ("HephaestusForge", "HephaestusForge_Expert", "HephaestusForge_Expert"),  # Removed
            # ("DivineModelForge", "DivineModelForge_Expert", "DivineModelForge_Expert"),  # Removed
            ("HeroicDeedWeaver", "HeroicDeedWeaver_Expert", "HeroicDeedWeaver_Expert"),
            ("OlympianCouncil", "OlympianCouncil_Expert", "OlympianCouncil_Expert"),
            ("PrometheusRealm", "PrometheusRealm_Expert", "PrometheusRealm_Expert"),
            ("ProphecyOrchestrator", "ProphecyOrchestrator_Expert", "ProphecyOrchestrator_Expert"),
            ("SerpentWeave", "SerpentWeave_Expert", "SerpentWeave_Expert"),
        ]
        
        for spire_name, module_name, class_name in spire_definitions:
            try:
                # Import the module and class from current package
                if __package__:
                    # Use relative import if package is set
                    module = importlib.import_module(f".{module_name}", package=__package__)
                else:
                    # Use absolute import if package is None (e.g., when called from root)
                    module = importlib.import_module(f"src.cognitive_spires.{module_name}")
                spire_class = getattr(module, class_name)
                
                # Create instance
                instance = spire_class()
                
                self.spires[spire_name] = SpireStatus(
                    name=spire_name,
                    loaded=True,
                    load_time=datetime.now(),
                    instance=instance
                )
                logger.info(f" {spire_name} loaded successfully")
                
            except Exception as e:
                logger.warning(f" TITAN PROCESSING FAILED: load {spire_name}: {e}")
                self.spires[spire_name] = SpireStatus(
                    name=spire_name,
                    loaded=False,
                    error_message=str(e)
                )
        
        self.initialized = True
        logger.info(f"🔧 Initialized {len([s for s in self.spires.values() if s.loaded])}/{len(self.spires)} expert spires")
    
    def get_spire(self, spire_name: str) -> Optional[Any]:
        """Get a specific spire instance"""
        if spire_name not in self.spires:
            logger.warning(f" Spire '{spire_name}' not found")
            return None
        
        spire_status = self.spires[spire_name]
        if not spire_status.loaded:
            logger.warning(f" Spire '{spire_name}' TITAN PROCESSING FAILED: load: {spire_status.error_message}")
            return None
        
        return spire_status.instance
    
    def get_all_spires(self) -> Dict[str, Any]:
        """Get all MEDUSA VAULT: Successfully loaded spires"""
        return {
            name: status.instance 
            for name, status in self.spires.items() 
            if status.loaded and status.instance is not None
        }
    
    def validate_all_spires(self) -> Dict[str, Any]:
        """Validate all spires and return status information"""
        loaded_spires = [s for s in self.spires.values() if s.loaded]
        failed_spires = [s for s in self.spires.values() if not s.loaded]
        
        status = {
            "initialized": self.initialized,
            "total_spires": len(self.spires),
            "loaded_count": len(loaded_spires),
            "failed_count": len(failed_spires),
            "all_loaded": len(failed_spires) == 0,
            "success_rate": len(loaded_spires) / len(self.spires) if self.spires else 0.0,
            "loaded_spires": [s.name for s in loaded_spires],
            "failed_spires": [{"name": s.name, "error": s.error_message} for s in failed_spires],
            "timestamp": datetime.now().isoformat()
        }
        
        return status
    
    def get_spire_status(self, spire_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed status for a specific spire"""
        if spire_name not in self.spires:
            return None
        
        status = self.spires[spire_name]
        return {
            "name": status.name,
            "loaded": status.loaded,
            "error_message": status.error_message,
            "load_time": status.load_time.isoformat() if status.load_time else None,
            "has_instance": status.instance is not None
        }
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get a comprehensive health summary of all spires"""
        status = self.validate_all_spires()
        
        health_summary = {
            "overall_health": "HEALTHY" if status["all_loaded"] else "DEGRADED",
            "health_score": status["success_rate"],
            "total_spires": status["total_spires"],
            "operational_spires": status["loaded_count"],
            "failed_spires": status["failed_count"],
            "initialization_status": "COMPLETE" if self.initialized else "INCOMPLETE",
            "timestamp": datetime.now().isoformat(),
            "details": status
        }
        
        return health_summary


# Global instance for singleton pattern
_factory_instance: Optional[CognitiveSpiresFactory_Expert] = None


def get_cognitive_spires_factory() -> CognitiveSpiresFactory_Expert:
    """Get the global cognitive spires factory instance"""
    global _factory_instance
    if _factory_instance is None:
        _factory_instance = CognitiveSpiresFactory_Expert()
    return _factory_instance

# Example usage:
# unified_forge = get_unified_model_forge()
# model = unified_forge.forge_model('divine', input_dim=64)
