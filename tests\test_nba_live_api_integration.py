#!/usr/bin/env python3
"""
Test NBA Live API Integration - HYPER MEDUSA NEURAL VAULT
========================================================

Test the enhanced NBA Live API integration for ultra-low latency data
"""

import asyncio
import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from src.integrations.live_realtime_data_integrator import (
    LiveRealTimeDataIntegrator, 
    NBALiveBoxScore,
    NBA_LIVE_API_AVAILABLE
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestNBALiveAPIIntegration:
    """Test NBA Live API integration capabilities"""
    
    @pytest.fixture
    def integrator(self):
        """Create test integrator instance"""
        return LiveRealTimeDataIntegrator()
    
    @pytest.fixture
    def mock_live_boxscore_data(self):
        """Mock NBA Live API boxscore response"""
        return {
            "game": {
                "gameId": "0022300001",
                "gameTimeUTC": "2024-01-15T20:00:00Z",
                "attendance": 18500,
                "sellout": "1",
                "arena": {
                    "arenaId": 1,
                    "arenaName": "Madison Square Garden",
                    "arenaCity": "New York",
                    "arenaState": "NY",
                    "arenaTimezone": "America/New_York"
                },
                "officials": [
                    {
                        "personId": 123,
                        "name": "John Doe",
                        "jerseyNum": "10",
                        "assignment": "Referee"
                    },
                    {
                        "personId": 124,
                        "name": "Jane Smith", 
                        "jerseyNum": "15",
                        "assignment": "Umpire"
                    }
                ],
                "homeTeam": {
                    "teamId": 1610612752,
                    "teamName": "Knicks",
                    "teamTricode": "NYK",
                    "score": 105,
                    "statistics": {
                        "points": 105,
                        "fieldGoalsMade": 40,
                        "fieldGoalsAttempted": 85,
                        "trueShootingPercentage": 0.58,
                        "fieldGoalsEffectiveAdjusted": 0.52,
                        "assistsTurnoverRatio": 1.8,
                        "benchPoints": 25,
                        "pointsFastBreak": 15,
                        "pointsInThePaint": 45,
                        "pointsSecondChance": 12
                    },
                    "players": [
                        {
                            "personId": 203999,
                            "firstName": "Nikola",
                            "familyName": "Jokic",
                            "jerseyNum": "15",
                            "position": "C",
                            "starter": "1",
                            "oncourt": "1",
                            "played": "1",
                            "statistics": {
                                "minutes": "PT35M12.00S",
                                "points": 28,
                                "fieldGoalsMade": 12,
                                "fieldGoalsAttempted": 20,
                                "trueShootingPercentage": 0.65,
                                "plusMinusPoints": 15,
                                "assists": 8,
                                "reboundsTotal": 12
                            }
                        }
                    ]
                },
                "awayTeam": {
                    "teamId": 1610612738,
                    "teamName": "Celtics",
                    "teamTricode": "BOS",
                    "score": 98,
                    "statistics": {
                        "points": 98,
                        "fieldGoalsMade": 35,
                        "fieldGoalsAttempted": 80,
                        "trueShootingPercentage": 0.55,
                        "fieldGoalsEffectiveAdjusted": 0.48
                    },
                    "players": []
                }
            }
        }
    
    @pytest.mark.asyncio
    async def test_enhanced_live_boxscore_with_api_available(self, integrator, mock_live_boxscore_data):
        """Test enhanced live boxscore when NBA Live API is available"""
        
        with patch('src.integrations.live_realtime_data_integrator.NBA_LIVE_API_AVAILABLE', True):
            with patch('src.integrations.live_realtime_data_integrator.NBALiveBoxScore') as mock_boxscore:
                # Setup mock
                mock_instance = Mock()
                mock_instance.game_details.get_dict.return_value = mock_live_boxscore_data["game"]
                mock_instance.arena.get_dict.return_value = mock_live_boxscore_data["game"]["arena"]
                mock_instance.officials.get_dict.return_value = mock_live_boxscore_data["game"]["officials"]
                mock_instance.home_team_stats.get_dict.return_value = mock_live_boxscore_data["game"]["homeTeam"]["statistics"]
                mock_instance.away_team_stats.get_dict.return_value = mock_live_boxscore_data["game"]["awayTeam"]["statistics"]
                mock_instance.home_team_player_stats.get_dict.return_value = mock_live_boxscore_data["game"]["homeTeam"]["players"]
                mock_instance.away_team_player_stats.get_dict.return_value = mock_live_boxscore_data["game"]["awayTeam"]["players"]
                
                mock_boxscore.return_value = mock_instance
                
                # Test enhanced boxscore
                result = await integrator.get_enhanced_live_boxscore("0022300001")
                
                # Verify enhanced data structure
                assert result['data_source'] == 'NBA_LIVE_API'
                assert 'arena' in result
                assert 'officials' in result
                assert 'advanced_metrics' in result
                assert 'contextual_data' in result
                
                # Verify arena data
                assert result['arena']['arenaName'] == "Madison Square Garden"
                assert result['arena']['arenaCity'] == "New York"
                
                # Verify officials data
                assert len(result['officials']) == 2
                assert result['officials'][0]['name'] == "John Doe"
                assert result['officials'][1]['assignment'] == "Umpire"
                
                # Verify advanced metrics
                assert 'home_team' in result['advanced_metrics']
                assert 'away_team' in result['advanced_metrics']
                assert result['advanced_metrics']['home_team']['true_shooting_percentage'] == 0.58
                
                logger.info("✅ Enhanced live boxscore test passed")
    
    @pytest.mark.asyncio
    async def test_comprehensive_data_with_enhanced_api(self, integrator, mock_live_boxscore_data):
        """Test comprehensive live data with enhanced NBA Live API"""
        
        with patch('src.integrations.live_realtime_data_integrator.NBA_LIVE_API_AVAILABLE', True):
            with patch.object(integrator, 'get_enhanced_live_boxscore') as mock_enhanced:
                with patch.object(integrator, 'get_live_play_by_play') as mock_plays:
                    
                    # Setup mocks
                    mock_enhanced.return_value = {
                        'data_source': 'NBA_LIVE_API',
                        'officials': mock_live_boxscore_data["game"]["officials"],
                        'arena': mock_live_boxscore_data["game"]["arena"],
                        'advanced_metrics': {'home_team': {'true_shooting_percentage': 0.58}}
                    }
                    mock_plays.return_value = []
                    
                    # Test comprehensive data
                    result = await integrator.get_comprehensive_live_data("0022300001")
                    
                    # Verify enhanced capabilities
                    assert result['data_source'] == 'NBA_LIVE_API_ENHANCED'
                    assert result['capabilities']['ultra_low_latency'] == True
                    assert result['capabilities']['advanced_metrics'] == True
                    assert result['capabilities']['referee_tracking'] == True
                    assert result['capabilities']['venue_context'] == True
                    assert result['capabilities']['true_shooting_percentage'] == True
                    
                    # Verify data completeness
                    assert result['data_completeness']['enhanced_metrics'] == True
                    assert result['data_completeness']['officials_data'] == True
                    assert result['data_completeness']['venue_data'] == True
                    
                    logger.info("✅ Comprehensive enhanced data test passed")
    
    @pytest.mark.asyncio
    async def test_fallback_to_standard_api(self, integrator):
        """Test fallback to standard API when NBA Live API unavailable"""
        
        with patch('src.integrations.live_realtime_data_integrator.NBA_LIVE_API_AVAILABLE', False):
            with patch.object(integrator, 'get_live_boxscore') as mock_standard:
                with patch.object(integrator, 'get_live_play_by_play') as mock_plays:
                    
                    # Setup mocks
                    mock_standard.return_value = [Mock()]
                    mock_plays.return_value = []
                    
                    # Test fallback
                    result = await integrator.get_comprehensive_live_data("0022300001")
                    
                    # Verify fallback behavior
                    assert result['data_source'] == 'STANDARD_API'
                    assert result['capabilities']['ultra_low_latency'] == False
                    assert result['capabilities']['advanced_metrics'] == False
                    assert result['capabilities']['referee_tracking'] == False
                    
                    # Verify data completeness reflects standard API
                    assert result['data_completeness']['enhanced_metrics'] == False
                    assert result['data_completeness']['officials_data'] == False
                    assert result['data_completeness']['venue_data'] == False
                    
                    logger.info("✅ Standard API fallback test passed")
    
    def test_convert_titan_clash_id_to_nba_game_id(self, integrator):
        """Test titan_clash_id to NBA game_id conversion"""
        
        # Test direct NBA game ID
        result = integrator._convert_to_nba_game_id("0022300001")
        assert result == "0022300001"
        
        # Test titan clash ID conversion
        result = integrator._convert_to_nba_game_id("titan_clash_0022300001")
        assert result == "0022300001"
        
        logger.info("✅ ID conversion test passed")


if __name__ == "__main__":
    # Run basic integration test
    async def main():
        integrator = LiveRealTimeDataIntegrator()
        logger.info(f"🔍 NBA Live API Available: {NBA_LIVE_API_AVAILABLE}")
        
        if NBA_LIVE_API_AVAILABLE:
            logger.info("✅ NBA Live API integration ready for ultra-low latency data")
        else:
            logger.info("⚠️ NBA Live API not available - using standard API fallback")
    
    asyncio.run(main())
