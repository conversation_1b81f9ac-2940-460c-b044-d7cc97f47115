from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union, AsyncIterator
from dataclasses import dataclass, field


#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Data Access Interfaces
==================================================

Interface abstractions for unified data access across the Living City.
These interfaces provide standardized contracts for database operations,
configuration management, and data processing.

Key Features:
- Unified database access patterns
- Configuration management abstraction
- Service-aware data routing
- Connection health monitoring
- Query optimization and caching
"""



class QueryType(Enum):
    """Types of database queries"""
    SELECT = "select"
    INSERT = "insert"
    UPDATE = "update"
    DELETE = "delete"
    AGGREGATE = "aggregate"
    SEARCH = "search"
    ANALYTICS = "analytics"


class DataFormat(Enum):
    """Data format types"""
    JSON = "json"
    CSV = "csv"
    PARQUET = "parquet"
    PICKLE = "pickle"
    BINARY = "binary"
    TEXT = "text"


class ConnectionHealth(Enum):
    """Database connection health status"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    DISCONNECTED = "disconnected"
    UNKNOWN = "unknown"


@dataclass
class Query:
    """Standardized query object"""
    id: str
    query_type: QueryType
    statement: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    service_name: str = "unknown"
    timeout_seconds: int = 30
    cache_enabled: bool = True
    cache_ttl_seconds: int = 300
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class DataResult:
    """Standardized data result object"""
    query_id: str
    success: bool
    data: List[Dict[str, Any]] = field(default_factory=list)
    row_count: int = 0
    execution_time_ms: float = 0.0
    from_cache: bool = False
    error: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ConnectionInfo:
    """Database connection information"""
    service_name: str
    database_name: str
    connection_url: str
    pool_size: int
    active_connections: int
    health: ConnectionHealth
    last_health_check: datetime
    total_queries: int = 0
    failed_queries: int = 0
    average_response_time_ms: float = 0.0


class DatabaseInterface(ABC):
    """
    Interface for database operations
    
    Provides standardized database access patterns that abstract
    away specific database implementations and enable service-aware
    data routing through the unified database system.
    """
    
    @abstractmethod
    async def execute_query(self, query: Query) -> DataResult:
        """
        Execute a database query

        Args:
            query: The query to execute

        Returns:
            DataResult: Query execution result
        """
        raise NotImplementedError("Subclasses must implement execute_query method")
    
    @abstractmethod
    async def execute_batch(self, queries: List[Query]) -> List[DataResult]:
        """
        Execute multiple queries in a batch

        Args:
            queries: List of queries to execute

        Returns:
            List[DataResult]: Results for each query
        """
        raise NotImplementedError("Subclasses must implement execute_batch method")
    
    @abstractmethod
    async def stream_query(self, query: Query) -> AsyncIterator[Dict[str, Any]]:
        """
        Stream query results for large datasets

        Args:
            query: The query to stream

        Yields:
            Dict[str, Any]: Individual result rows
        """
        raise NotImplementedError("Subclasses must implement stream_query method")
    
    @abstractmethod
    async def get_connection_health(self) -> ConnectionHealth:
        """
        Get database connection health status

        Returns:
            ConnectionHealth: Current connection health
        """
        raise NotImplementedError("Subclasses must implement get_connection_health method")

    @abstractmethod
    async def get_connection_info(self) -> ConnectionInfo:
        """
        Get detailed connection information

        Returns:
            ConnectionInfo: Connection details and metrics
        """
        raise NotImplementedError("Subclasses must implement get_connection_info method")

    @abstractmethod
    async def test_connection(self) -> bool:
        """
        Test database connectivity

        Returns:
            bool: True if connection is working, False otherwise
        """
        raise NotImplementedError("Subclasses must implement test_connection method")


class ConfigurationInterface(ABC):
    """
    Interface for configuration management
    
    Provides standardized configuration access that abstracts
    away specific configuration sources and formats.
    """
    
    @abstractmethod
    async def get_config(self, path: str, default: Any = None) -> Any:
        """
        Get configuration value by path

        Args:
            path: Configuration path (dot notation)
            default: Default value if not found

        Returns:
            Any: Configuration value
        """
        raise NotImplementedError("Subclasses must implement get_config method")

    @abstractmethod
    async def get_service_config(self, service_name: str) -> Dict[str, Any]:
        """
        Get configuration for a specific service

        Args:
            service_name: Name of the service

        Returns:
            Dict[str, Any]: Service configuration
        """
        raise NotImplementedError("Subclasses must implement get_service_config method")

    @abstractmethod
    async def set_config(self, path: str, value: Any) -> bool:
        """
        Set configuration value

        Args:
            path: Configuration path
            value: Value to set

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement set_config method")

    @abstractmethod
    async def reload_config(self) -> bool:
        """
        Reload configuration from sources

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement reload_config method")

    @abstractmethod
    async def validate_config(self) -> Dict[str, Any]:
        """
        Validate current configuration

        Returns:
            Dict[str, Any]: Validation results
        """
        raise NotImplementedError("Subclasses must implement validate_config method")


class DataAccessInterface(ABC):
    """
    Unified data access interface
    
    Combines database and configuration access into a single
    interface for comprehensive data operations.
    """
    
    @abstractmethod
    async def get_database(self, service_name: str) -> DatabaseInterface:
        """
        Get database interface for a service

        Args:
            service_name: Name of the service

        Returns:
            DatabaseInterface: Database interface for the service
        """
        raise NotImplementedError("Subclasses must implement get_database method")

    @abstractmethod
    async def get_config(self) -> ConfigurationInterface:
        """
        Get configuration interface

        Returns:
            ConfigurationInterface: Configuration interface
        """
        raise NotImplementedError("Subclasses must implement get_config method")

    @abstractmethod
    async def execute_service_query(self, service_name: str, query: Query) -> DataResult:
        """
        Execute query for a specific service

        Args:
            service_name: Name of the service
            query: Query to execute

        Returns:
            DataResult: Query result
        """
        raise NotImplementedError("Subclasses must implement execute_service_query method")

    @abstractmethod
    async def get_service_data_health(self, service_name: str) -> Dict[str, Any]:
        """
        Get data health status for a service

        Args:
            service_name: Name of the service

        Returns:
            Dict[str, Any]: Data health information
        """
        raise NotImplementedError("Subclasses must implement get_service_data_health method")


class CacheInterface(ABC):
    """
    Interface for caching operations
    
    Provides standardized caching capabilities for improved
    performance and reduced database load.
    """
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache

        Args:
            key: Cache key

        Returns:
            Optional[Any]: Cached value or None if not found
        """
        raise NotImplementedError("Subclasses must implement get method")

    @abstractmethod
    async def set(self, key: str, value: Any, ttl_seconds: int = 300) -> bool:
        """
        Set value in cache

        Args:
            key: Cache key
            value: Value to cache
            ttl_seconds: Time to live in seconds

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement set method")

    @abstractmethod
    async def delete(self, key: str) -> bool:
        """
        Delete value from cache

        Args:
            key: Cache key

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement delete method")

    @abstractmethod
    async def clear(self, pattern: Optional[str] = None) -> bool:
        """
        Clear cache entries

        Args:
            pattern: Optional pattern to match keys

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement clear method")

    @abstractmethod
    async def get_stats(self) -> Dict[str, Any]:
        """
        Get cache statistics

        Returns:
            Dict[str, Any]: Cache statistics
        """
        raise NotImplementedError("Subclasses must implement get_stats method")


class DataProcessor(ABC):
    """
    Interface for data processing operations
    
    Provides standardized data transformation and processing
    capabilities for analytics and machine learning pipelines.
    """
    
    @abstractmethod
    async def process_data(self, data: List[Dict[str, Any]],
                          processing_type: str,
                          parameters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        Process data with specified transformation

        Args:
            data: Input data to process
            processing_type: Type of processing to apply
            parameters: Processing parameters

        Returns:
            List[Dict[str, Any]]: Processed data
        """
        raise NotImplementedError("Subclasses must implement process_data method")
    
    @abstractmethod
    async def aggregate_data(self, data: List[Dict[str, Any]],
                           aggregation_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aggregate data based on configuration

        Args:
            data: Input data to aggregate
            aggregation_config: Aggregation configuration

        Returns:
            Dict[str, Any]: Aggregated results
        """
        raise NotImplementedError("Subclasses must implement aggregate_data method")
    
    @abstractmethod
    async def export_data(self, data: List[Dict[str, Any]],
                         format: DataFormat,
                         destination: str) -> bool:
        """
        Export data to specified format and destination

        Args:
            data: Data to export
            format: Export format
            destination: Export destination

        Returns:
            bool: True if successful, False otherwise
        """
        raise NotImplementedError("Subclasses must implement export_data method")


class DataAccessManager:
    """
    Manager for coordinating data access across Living City services
    
    Provides centralized data access coordination, connection pooling,
    and service-aware data routing.
    """
    
    def __init__(self):
        self.databases: Dict[str, DatabaseInterface] = {}
        self.config: Optional[ConfigurationInterface] = None
        self.cache: Optional[CacheInterface] = None
        self.processor: Optional[DataProcessor] = None
    
    async def register_database(self, service_name: str, database: DatabaseInterface):
        """Register a database interface for a service"""
        self.databases[service_name] = database
    
    async def register_config(self, config: ConfigurationInterface):
        """Register configuration interface"""
        self.config = config
    
    async def register_cache(self, cache: CacheInterface):
        """Register cache interface"""
        self.cache = cache
    
    async def register_processor(self, processor: DataProcessor):
        """Register data processor"""
        self.processor = processor
    
    async def get_service_database(self, service_name: str) -> Optional[DatabaseInterface]:
        """Get database interface for a service"""
        return self.databases.get(service_name)
    
    async def execute_with_cache(self, service_name: str, query: Query) -> DataResult:
        """Execute query with caching support"""
        if not query.cache_enabled or not self.cache:
            database = self.databases.get(service_name)
            if database:
                return await database.execute_query(query)
            else:
                return DataResult(
                    query_id=query.id,
                    success=False,
                    error=f"No database registered for service: {service_name}"
                )
        
        # Try cache first
        cache_key = f"{service_name}:{query.id}:{hash(query.statement)}"
        cached_result = await self.cache.get(cache_key)
        
        if cached_result:
            cached_result.from_cache = True
            return cached_result
        
        # Execute query and cache result
        database = self.databases.get(service_name)
        if database:
            result = await database.execute_query(query)
            if result.success:
                await self.cache.set(cache_key, result, query.cache_ttl_seconds)
            return result
        else:
            return DataResult(
                query_id=query.id,
                success=False,
                error=f"No database registered for service: {service_name}"
            )
    
    async def get_all_connection_health(self) -> Dict[str, ConnectionHealth]:
        """Get connection health for all registered databases"""
        health_status = {}
        for service_name, database in self.databases.items():
            try:
                health_status[service_name] = await database.get_connection_health()
            except Exception:
                health_status[service_name] = ConnectionHealth.UNKNOWN
        return health_status

    async def get_system_data_overview(self) -> Dict[str, Any]:
        """Get comprehensive data system overview"""
        overview = {
            "registered_databases": len(self.databases),
            "services": list(self.databases.keys()),
            "connection_health": await self.get_all_connection_health(),
            "cache_available": self.cache is not None,
            "processor_available": self.processor is not None,
            "config_available": self.config is not None
        }

        if self.cache:
            overview["cache_stats"] = await self.cache.get_stats()

        return overview


# Export main components
__all__ = [
    "QueryType",
    "DataFormat",
    "ConnectionHealth",
    "Query",
    "DataResult",
    "ConnectionInfo",
    "DatabaseInterface",
    "ConfigurationInterface",
    "DataAccessInterface",
    "CacheInterface",
    "DataProcessor",
    "DataAccessManager"
]
