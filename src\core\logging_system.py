import asyncio
import json
import logging
import logging.handlers
import os
import re
import sys
import traceback
import uuid
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
import threading
from contextlib import contextmanager
import functools
import time
from functools import wraps


#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - STRUCTURED LOGGING SYSTEM 
===============================================================

Comprehensive structured logging framework providing:
- Standardized log formats with context and metadata
- Multiple log levels with appropriate usage guidelines
- Performance logging and metrics
- Security-aware logging (PII scrubbing)
- Integration with monitoring and alerting systems
- Audit trail capabilities

 PRODUCTION-READY LOGGING INFRASTRUCTURE 
"""


# Performance timing


class LogLevel(Enum):
    """Standardized log levels with usage guidelines"""
    MEDUSA_DEBUG = "MEDUSA_DEBUG" # Detailed debugging information
    INFO = "INFO" # General informational messages
    WARNING = "WARNING" # Warning messages for potential issues
    ERROR = "ERROR" # Error messages for handled exceptions
    CRITICAL = "CRITICAL" # Critical errors requiring immediate attention


class LogCategory(Enum):
    """Log categories for filtering and organization"""
    SYSTEM = "system"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BUSINESS = "business"
    AUDIT = "audit"
    MEDUSA_DEBUG = "debug"
    PREDICTION = "prediction"
    ML_MODEL = "ml_model"
    DATABASE = "database"
    API = "api"
    USER_ACTION = "user_action"


@dataclass
class LogContext:
    """Structured context for log entries"""
    log_id: str
    timestamp: str
    level: str
    category: LogCategory
    component: str
    operation: str
    vault_user_id: Optional[str] = None
    aegis_session_id: Optional[str] = None
    request_id: Optional[str] = None
    trace_id: Optional[str] = None
    duration_ms: Optional[float] = None
    additional_data: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'log_id': self.log_id,
            'timestamp': self.timestamp,
            'level': self.level,
            'category': self.category.value,
            'component': self.component,
            'operation': self.operation,
            'vault_user_id': self.vault_user_id,
            'aegis_session_id': self.aegis_session_id,
            'request_id': self.request_id,
            'trace_id': self.trace_id,
            'duration_ms': self.duration_ms,
            'additional_data': self.additional_data or {}
        }


class PIIScrubber:
    """Utility class to scrub PII from log messages"""
    
    # Patterns for common PII
    PII_PATTERNS = {
        'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        'phone': re.compile(r'\b\d{3}[-.]?\d{3}[-.]?\d{4}\b'),
        'ssn': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
        'credit_card': re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
        'ip_address': re.compile(r'\b\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}\b'),
        'password': re.compile(r'(password|pwd|pass)["\']?\s*[:=]\s*["\']?[^\s"\']*', re.IGNORECASE),
        'api_key': re.compile(r'(api[_-]?key|apikey|access[_-]?token)["\']?\s*[:=]\s*["\']?[^\s"\']*', re.IGNORECASE),
    }
    
    @classmethod
    def scrub_message(cls, message: str) -> str:
        """Scrub PII from log message"""
        scrubbed = message
        
        for pii_type, pattern in cls.PII_PATTERNS.items():
            if pii_type in ['password', 'api_key']:
                # For sensitive fields, replace the value part
                scrubbed = pattern.sub(lambda m: m.group().split('=')[0] + '=***REDACTED***', scrubbed)
            else:
                # For other PII, replace with redacted placeholder
                scrubbed = pattern.sub(f'***{pii_type.upper()}_REDACTED***', scrubbed)
        
        return scrubbed
    
    @classmethod
    def scrub_dict(cls, data: Dict[str, Any]) -> Dict[str, Any]:
        """Scrub PII from dictionary data"""
        if not isinstance(data, dict):
            return data
        
        scrubbed = {}
        sensitive_keys = {'password', 'pwd', 'pass', 'secret', 'key', 'token', 'auth', 'credential'}
        
        for key, value in data.items():
            key_lower = key.lower()
            
            # Check if key contains sensitive information
            if any(sensitive in key_lower for sensitive in sensitive_keys):
                scrubbed[key] = '***REDACTED***'
            elif isinstance(value, str):
                scrubbed[key] = cls.scrub_message(value)
            elif isinstance(value, dict):
                scrubbed[key] = cls.scrub_dict(value)
            elif isinstance(value, list):
                scrubbed[key] = [cls.scrub_dict(item) if isinstance(item, dict) else 
                                 cls.scrub_message(item) if isinstance(item, str) else item 
                                 for item in value]
            else:
                scrubbed[key] = value
        
        return scrubbed


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured JSON logging"""
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON"""
        
        # Base log structure
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': PIIScrubber.scrub_message(record.getMessage()),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'thread_name': record.threadName,
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }
        
        # Add custom fields from extra
        if hasattr(record, 'extra_data'):
            log_entry['extra'] = PIIScrubber.scrub_dict(record.extra_data)
        
        # Add context if available
        if hasattr(record, 'log_context'):
            log_entry['context'] = record.log_context.to_dict()
        
        # Add performance metrics if available
        if hasattr(record, 'performance'):
            log_entry['performance'] = record.performance
        
        return json.dumps(log_entry, separators=(',', ':'))


class MedusaLogger:
    """Enhanced logger with structured logging and context management"""
    
    _instances = {}
    _lock = threading.Lock()
    
    def __new__(cls, name: str = None):
        if name is None:
            name = 'medusa'
        
        with cls._lock:
            if name not in cls._instances:
                cls._instances[name] = super().__new__(cls)
                cls._instances[name]._initialized = False
            return cls._instances[name]
    
    def __init__(self, name: str = None):
        if getattr(self, '_initialized', False):
            return
        
        self.name = name or 'medusa'
        self.logger = logging.getLogger(self.name)
        self._setup_logger()
        self._initialized = True
    
    def _setup_logger(self):
        """Setup logger with structured formatting"""
        
        # Clear existing handlers
        self.logger.handlers.clear()
        # Set level (use DEBUG if MEDUSA_DEBUG not available)
        try:
            self.logger.setLevel(logging.DEBUG)  # Using logging.DEBUG directly
        except AttributeError:
            self.logger.setLevel(logging.DEBUG)
        
        # Create logs directory
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Console handler with structured format
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_formatter = StructuredFormatter()
        console_handler.setFormatter(console_formatter)
        self.logger.addHandler(console_handler)
        
        # File handler for all logs
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / f"{self.name}.log",
            maxBytes=10 * 1024 * 1024, # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)  # Using logging.DEBUG directly
        file_handler.setFormatter(console_formatter)
        self.logger.addHandler(file_handler)
        
        # Error file handler
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / f"{self.name}_errors.log",
            maxBytes=10 * 1024 * 1024, # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(console_formatter)
        self.logger.addHandler(error_handler)
        
        # Performance log handler
        perf_handler = logging.handlers.RotatingFileHandler(
            log_dir / f"{self.name}_performance.log",
            maxBytes=5 * 1024 * 1024, # 5MB
            backupCount=3,
            encoding='utf-8' )
        perf_handler.setLevel(logging.INFO)
        perf_handler.setFormatter(console_formatter)
        
        # Add filter for performance logs
        perf_handler.addFilter(lambda record: hasattr(record, 'performance'))
        self.logger.addHandler(perf_handler)
    
    def _create_log_context(
        self,
        category: LogCategory,
        component: str,
        operation: str,
        level: str,
        vault_user_id: Optional[str] = None,
        aegis_session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        trace_id: Optional[str] = None,
        duration_ms: Optional[float] = None,
        additional_data: Optional[Dict[str, Any]] = None,
        **kwargs # Accept any additional keyword arguments
    ) -> LogContext:
        """Create structured log context"""
        # Merge kwargs into additional_data
        if additional_data is None:
            additional_data = {}
        
        # Add any extra kwargs (except 'extra' which is handled separately)
        for key, value in kwargs.items():
            if key != 'extra':
                additional_data[key] = value
        
        return LogContext(
            log_id=str(uuid.uuid4()),
            timestamp=datetime.utcnow().isoformat() + 'Z',
            level=level,
            category=category,
            component=component,
            operation=operation,
            vault_user_id=vault_user_id,
            aegis_session_id=aegis_session_id,
            request_id=request_id,
            trace_id=trace_id,
            duration_ms=duration_ms,
            additional_data=additional_data
        )
    
    def debug(
        self,
        message: str,
        component: str = "unknown",
        operation: str = "unknown",
        category: LogCategory = LogCategory.MEDUSA_DEBUG,
        extra_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log debug message with context"""
        context = self._create_log_context(category, component, operation, "MEDUSA_DEBUG", **kwargs)
        self.logger.debug(
            message,
            extra={'log_context': context, 'extra_data': extra_data}
        )
    
    def info(
        self,
        message: str,
        component: str = "unknown",
        operation: str = "unknown",
        category: LogCategory = LogCategory.SYSTEM,
        extra_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log info message with context"""
        context = self._create_log_context(category, component, operation, "INFO", **kwargs)
        self.logger.info(
            message,
            extra={'log_context': context, 'extra_data': extra_data}
        )
    
    def warning(
        self,
        message: str,
        component: str = "unknown",
        operation: str = "unknown",
        category: LogCategory = LogCategory.SYSTEM,
        extra_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log warning message with context"""
        context = self._create_log_context(category, component, operation, "WARNING", **kwargs)
        self.logger.warning(
            message,
            extra={'log_context': context, 'extra_data': extra_data}
        )
    
    def error(
        self,
        message: str,
        component: str = "unknown",
        operation: str = "unknown",
        category: LogCategory = LogCategory.SYSTEM,
        exception: Optional[Exception] = None,
        extra_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log error message with context"""
        context = self._create_log_context(category, component, operation, "ERROR", **kwargs)
        
        if exception:
            self.logger.error(
                message,
                extra={'log_context': context, 'extra_data': extra_data},
                exc_info=exception
            )
        else:
            self.logger.error(
                message,
                extra={'log_context': context, 'extra_data': extra_data}
            )
    
    def critical(
        self,
        message: str,
        component: str = "unknown",
        operation: str = "unknown",
        category: LogCategory = LogCategory.SYSTEM,
        exception: Optional[Exception] = None,
        extra_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log critical message with context"""
        context = self._create_log_context(category, component, operation, "CRITICAL", **kwargs)
        
        if exception:
            self.logger.critical(
                message,
                extra={'log_context': context, 'extra_data': extra_data},
                exc_info=exception
            )
        else:
            self.logger.critical(
                message,
                extra={'log_context': context, 'extra_data': extra_data}
            )
    
    def performance(
        self,
        operation: str,
        duration_ms: float,
        component: str = "unknown",
        success: bool = True,
        metrics: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log performance metrics"""
        perf_data = {
            'operation': operation,
            'duration_ms': duration_ms,
            'success': success,
            'metrics': metrics or {}
        }
        
        context = self._create_log_context(
            LogCategory.PERFORMANCE, 
            component, 
            operation, 
            "INFO",
            duration_ms=duration_ms,
            **kwargs
        )
        
        self.logger.info(
            f"Performance: {operation} completed in {duration_ms:.2f}ms",
            extra={'log_context': context, 'performance': perf_data}
        )
    
    def audit(
        self,
        action: str,
        resource: str,
        component: str = "unknown",
        vault_user_id: Optional[str] = None,
        result: str = "success",
        extra_data: Optional[Dict[str, Any]] = None,
        **kwargs
    ):
        """Log audit trail events"""
        audit_data = {
            'action': action,
            'resource': resource,
            'result': result,
            'additional_data': extra_data or {}
        }
        
        context = self._create_log_context(
            LogCategory.AUDIT,
            component,
            action,
            "INFO",
            vault_user_id=vault_user_id,
            additional_data=audit_data,
            **kwargs
        )
        
        self.logger.info(
            f"Audit: {action} on {resource} - {result}",
            extra={'log_context': context, 'extra_data': audit_data}
        )


class PerformanceTimer:
    """Context manager and decorator for performance timing"""
    
    def __init__(
        self,
        operation: str,
        component: str = "unknown",
        logger: Optional[MedusaLogger] = None,
        threshold_ms: Optional[float] = None,
        log_slow_operations: bool = True
    ):
        self.operation = operation
        self.component = component
        self.logger = logger or MedusaLogger()
        self.threshold_ms = threshold_ms
        self.log_slow_operations = log_slow_operations
        self.start_time = None
        self.end_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration_ms = (self.end_time - self.start_time) * 1000
        
        success = exc_type is None
        
        # Log performance
        self.logger.performance(
            operation=self.operation,
            duration_ms=duration_ms,
            component=self.component,
            success=success
        )
        
        # Log slow operations
        if self.log_slow_operations and self.threshold_ms and duration_ms > self.threshold_ms:
            self.logger.warning(
                f"Slow operation detected: {self.operation} took {duration_ms:.2f}ms (threshold: {self.threshold_ms}ms)",
                component=self.component,
                operation=self.operation,
                category=LogCategory.PERFORMANCE,
                extra_data={'duration_ms': duration_ms, 'threshold_ms': self.threshold_ms}
            )


def performance_log(
    operation: str = None,
    component: str = "unknown",
    threshold_ms: float = None,
    log_slow_operations: bool = True
):
    """Decorator for automatic performance logging"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            
            with PerformanceTimer(
                operation=op_name,
                component=component,
                threshold_ms=threshold_ms,
                log_slow_operations=log_slow_operations
            ):
                return func(*args, **kwargs)
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            op_name = operation or f"{func.__module__}.{func.__name__}"
            
            with PerformanceTimer(
                operation=op_name,
                component=component,
                threshold_ms=threshold_ms,
                log_slow_operations=log_slow_operations
            ):
                return await func(*args, **kwargs)
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


# Global logger instance
medusa_logger = MedusaLogger("medusa")

# Convenience functions
def get_logger(name: str = None) -> MedusaLogger:
    """Get a logger instance"""
    return MedusaLogger(name)


def log_debug(message: str, **kwargs):
    """Quick debug log"""


def log_info(message: str, **kwargs):
    """Quick info log"""
    medusa_logger.info(message, **kwargs)


def log_warning(message: str, **kwargs):
    """Quick warning log"""
    medusa_logger.warning(message, **kwargs)


def log_error(message: str, exception: Exception = None, **kwargs):
    """Quick error log"""
    medusa_logger.error(message, exception=exception, **kwargs)


def log_critical(message: str, exception: Exception = None, **kwargs):
    """Quick critical log"""
    medusa_logger.critical(message, exception=exception, **kwargs)


def log_performance(operation: str, duration_ms: float, success: bool = True, **kwargs):
    """Quick performance log"""
    medusa_logger.performance(
        operation=operation,
        duration_ms=duration_ms,
        success=success,
        **kwargs
    )


def log_performance_decorator(func):
    """
    Decorator for performance logging of function execution
    
    Usage:
    @log_performance_decorator
    async def my_function():
    # function code
    """
    @wraps(func)
    async def async_wrapper(*args, **kwargs):
        start_time = time.time()
        function_name = func.__name__
        module_name = func.__module__
        
        logger = get_logger(module_name)
        
        try:
            
            result = await func(*args, **kwargs)
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            logger.performance(
                operation=function_name,
                duration_ms=duration_ms,
                component=module_name,
                success=True
            )
            
            return result
        
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            logger.performance(
                operation=function_name,
                duration_ms=duration_ms,
                component=module_name,
                success=False,
                metrics={'error': str(e), 'exception_type': type(e).__name__}
            )
            logger.error(
                f"Failed {function_name}",
                component=module_name,
                operation=function_name,
                exception=e
            )
            
            raise
    
    @wraps(func)
    def sync_wrapper(*args, **kwargs):
        start_time = time.time()
        function_name = func.__name__
        module_name = func.__module__
        
        logger = get_logger(module_name)
        
        try:
            
            result = func(*args, **kwargs)
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            logger.performance(
                operation=function_name,
                duration_ms=duration_ms,
                component=module_name,
                success=True
            )
            
            return result
        
        except Exception as e:
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            logger.performance(
                operation=function_name,
                duration_ms=duration_ms,
                component=module_name,
                success=False,
                metrics={'error': str(e), 'exception_type': type(e).__name__}
            )
            logger.error(
                f"Failed {function_name}",
                component=module_name,
                operation=function_name,
                exception=e
            )
            
            raise
    
    # Return appropriate wrapper based on function type
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


def audit_log(action: str, context: Dict[str, Any] = None, **kwargs):
    """
    Convenience function for audit logging
    
    Args:
    action: The action being logged
    context: Additional context information
    **kwargs: Additional key-value pairs to log
    """
    logger = get_logger("audit")
    
    audit_data = {
        "action": action,
        "timestamp": datetime.now().isoformat(),
        "category": LogCategory.SECURITY.value,
        "audit": True
    }
    
    if context:
        audit_data.update(context)
    
    if kwargs:
        audit_data.update(kwargs)
    
    # Use additional_data instead of extra to avoid conflict
    logger.info(f"AUDIT: {action}", 
                category=LogCategory.SECURITY, 
                component="audit",
                operation="audit_log",
                additional_data=audit_data)


@contextmanager
def log_context(**context_vars):
    """
    Context manager for adding context to all log messages within the block
    
    Usage:
    with log_context(vault_user_id="123", operation="user_login"):
    logger.info(" MEDUSA VAULT: User attempting login")
    # ... more operations
    """
    # This is a placeholder. A real implementation would use a thread-local storage
    # or a similar mechanism to propagate context through logging calls.
    # For now, it just returns a LogContext object which the caller can manually pass.
    yield LogContext(**context_vars)


if __name__ == "__main__":
    # Example usage and testing
    
    logger = get_logger("test")
    
    # Basic logging
    logger.info(" MEDUSA VAULT: System starting up", component="main", operation="startup")
    logger.warning(" TITAN WARNING: This is a warning", component="test", operation="warning_test")
    
    # Error logging with exception
    try:
        1 / 0
    except Exception as e:
        logger.error(" MEDUSA ERROR: Division by zero occurred", component="test", operation="math", exception=e)
    
    # Performance logging
    with PerformanceTimer("test_operation", "test_component"):
        time.sleep(0.1) # Simulate work
    
    # Decorator example
    @performance_log(component="test", threshold_ms=50)
    def slow_function():
        time.sleep(0.1)
        return "done"
    
    result = slow_function()
    
    # Audit logging
    logger.audit("user_login", "authentication_system", vault_user_id="user123", result="success")
    
