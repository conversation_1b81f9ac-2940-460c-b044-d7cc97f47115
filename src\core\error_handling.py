import json
import traceback
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, asdict
import logging
from src.core.logging_system import get_logger


#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - STANDARDIZED ERROR HANDLING SYSTEM
=====================================================================

Comprehensive error handling framework providing:
- Standardized exception classes with context and metadata
- Error classification and severity levels
- Actionable error messages and recovery suggestions
- Error tracking and metrics
- Integration with logging and monitoring systems

 PRODUCTION-READY ERROR MANAGEMENT
"""


logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels for classification and response prioritization"""
    CRITICAL = "critical" # System-breaking errors requiring immediate attention
    HIGH = "high" # Major functionality impacted, urgent fix needed
    MEDIUM = "medium" # Moderate impact, should be addressed soon
    LOW = "low" # Minor issues, can be addressed in regular maintenance
    INFO = "info" # Informational, not necessarily an error


class ErrorCategory(Enum):
    """Error categories for classification and handling strategies"""
    # System-level errors
    SYSTEM = "system"
    CONFIGURATION = "configuration"
    NETWORK = "network"
    DATABASE = "database"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"

    # Application-level errors
    VALIDATION = "validation"
    BUSINESS_LOGIC = "business_logic"
    DATA_PROCESSING = "data_processing"
    ML_MODEL = "ml_model"
    PREDICTION = "prediction"

    # External dependencies
    EXTERNAL_API = "external_api"
    FIREBASE = "firebase"
    HOOPS_PANTHEON_API = "nba_api"

    # Performance and resources
    PERFORMANCE = "performance"
    RESOURCE = "resource"
    TIMEOUT = "timeout"

    # Unknown or uncategorized
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """Rich context information for errors"""
    error_id: str
    timestamp: str
    severity: ErrorSeverity
    category: ErrorCategory
    component: str
    operation: str
    vault_user_id: Optional[str] = None
    aegis_session_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'error_id': self.error_id,
            'timestamp': self.timestamp,
            'severity': self.severity.value,
            'category': self.category.value,
            'component': self.component,
            'operation': self.operation,
            'vault_user_id': self.vault_user_id,
            'aegis_session_id': self.aegis_session_id,
            'request_id': self.request_id,
            'additional_data': self.additional_data or {}
        }


class MedusaError(Exception):
    """Base exception class for all Hyper Medusa Neural Vault errors"""

    def __init__(
        self,
        message: str,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        category: ErrorCategory = ErrorCategory.UNKNOWN,
        error_code: Optional[str] = None,
        component: str = "unknown",
        operation: str = "unknown",
        user_message: Optional[str] = None,
        recovery_suggestions: Optional[List[str]] = None,
        additional_data: Optional[Dict[str, Any]] = None,
        vault_user_id: Optional[str] = None,
        aegis_session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        original_exception: Optional[Exception] = None,
        **kwargs # Accept additional keyword arguments for subclass compatibility
    ):
        super().__init__(message)

        self.error_id = str(uuid.uuid4())
        self.timestamp = datetime.utcnow().isoformat()
        self.severity = severity
        self.category = category
        self.error_code = error_code or f"{category.value.upper()}_{severity.value.upper()}"
        self.component = component
        self.operation = operation
        self.user_message = user_message or self._generate_user_message()
        self.recovery_suggestions = recovery_suggestions or []
        self.additional_data = additional_data or {}
        self.vault_user_id = vault_user_id
        self.aegis_session_id = aegis_session_id
        self.request_id = request_id
        self.original_exception = original_exception

        # Merge any additional kwargs into additional_data
        if kwargs:
            self.additional_data.update(kwargs)

        # Create error context
        self.context = ErrorContext(
            error_id=self.error_id,
            timestamp=self.timestamp,
            severity=self.severity,
            category=self.category,
            component=self.component,
            operation=self.operation,
            vault_user_id=self.vault_user_id,
            aegis_session_id=self.aegis_session_id,
            request_id=self.request_id,
            additional_data=self.additional_data
        )

        # Log the error
        self._log_error()

    def _generate_user_message(self) -> str:
        """Generate user-friendly error message"""
        if self.severity == ErrorSeverity.CRITICAL:
            return "A critical system error occurred. Please contact support immediately."
        elif self.severity == ErrorSeverity.HIGH:
            return "An error occurred that may affect system functionality. Our team has been notified."
        elif self.severity == ErrorSeverity.MEDIUM:
            return "An error occurred. Please try again or contact support if the issue persists."
        else:
            return "A minor issue occurred. Please try again."

    def _log_error(self) -> None:
        """Log the error with appropriate level"""
        error_data = {
            'error_id': self.error_id,
            'category': self.category.value,
            'component': self.component,
            'operation': self.operation,
            'severity': self.severity.value,
            'message': str(self),
            'user_message': self.user_message,
            'additional_data': self.additional_data
        }

        if self.severity in [ErrorSeverity.CRITICAL, ErrorSeverity.HIGH]:
            logger.error(f"🚨 {self.severity.value.upper()} ERROR: {error_data}")
        elif self.severity == ErrorSeverity.MEDIUM:
            logger.warning(f" MEDIUM ERROR: {error_data}")
        else:
            logger.info(f"ℹ️ LOW/INFO ERROR: {error_data}")

        # Log stack trace for debugging
        if self.original_exception:
            logger.error("Original exception:", exc_info=self.original_exception)

    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary for API responses"""
        return {
            'error': {
                'id': self.error_id,
                'timestamp': self.timestamp,
                'severity': self.severity.value,
                'category': self.category.value,
                'component': self.component,
                'operation': self.operation,
                'message': str(self),
                'user_message': self.user_message,
                'recovery_suggestions': self.recovery_suggestions,
                'additional_data': self.additional_data
            }
        }

    def to_json(self) -> str:
        """Convert error to JSON string"""
        return json.dumps(self.to_dict(), indent=2)


# Specific exception classes for different error types

class MedusaValidationError(MedusaError):
    """Input validation errors"""
    def __init__(self, message: str, field_name: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.VALIDATION)
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        if field_name:
            kwargs.setdefault('additional_data', {}).update({'field_name': field_name})
        super().__init__(message, **kwargs)


class MedusaConfigurationError(MedusaError):
    """Configuration-related errors"""
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.CONFIGURATION)
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        if config_key:
            kwargs.setdefault('additional_data', {}).update({'config_key': config_key})
        super().__init__(message, **kwargs)


class MedusaDatabaseError(MedusaError):
    """Database operation errors"""
    def __init__(self, message: str, operation: str = "unknown", table: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.DATABASE)
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('operation', operation)
        if table:
            kwargs.setdefault('additional_data', {}).update({'table': table})
        super().__init__(message, **kwargs)


class MedusaNetworkError(MedusaError):
    """Network and connectivity errors"""
    def __init__(self, message: str, endpoint: Optional[str] = None, status_code: Optional[int] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.NETWORK)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        additional_data = kwargs.setdefault('additional_data', {})
        if endpoint:
            additional_data['endpoint'] = endpoint
        if status_code:
            additional_data['status_code'] = status_code
        super().__init__(message, **kwargs)


class MedusaMLModelError(MedusaError):
    """Machine learning model errors"""
    def __init__(self, message: str, model_name: Optional[str] = None, model_version: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.ML_MODEL)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        additional_data = kwargs.setdefault('additional_data', {})
        if model_name:
            additional_data['model_name'] = model_name
        if model_version:
            additional_data['model_version'] = model_version
        super().__init__(message, **kwargs)


class MedusaPredictionError(MedusaError):
    """Prediction generation errors"""
    def __init__(self, message: str, titan_clash_id: Optional[str] = None, prediction_type: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.PREDICTION)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        additional_data = kwargs.setdefault('additional_data', {})
        if titan_clash_id:
            additional_data['titan_clash_id'] = titan_clash_id
        if prediction_type:
            additional_data['prediction_type'] = prediction_type
        super().__init__(message, **kwargs)


class MedusaExternalAPIError(MedusaError):
    """External API errors"""
    def __init__(self, message: str, api_name: str, endpoint: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.EXTERNAL_API)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('additional_data', {}).update({
            'api_name': api_name,
            'endpoint': endpoint
        })
        super().__init__(message, **kwargs)


class MedusaPerformanceError(MedusaError):
    """Performance-related errors"""
    def __init__(self, message: str, operation_time: Optional[float] = None, threshold: Optional[float] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.PERFORMANCE)
        kwargs.setdefault('severity', ErrorSeverity.LOW)
        additional_data = kwargs.setdefault('additional_data', {})
        if operation_time:
            additional_data['operation_time'] = operation_time
        if threshold:
            additional_data['threshold'] = threshold
        super().__init__(message, **kwargs)


class MedusaTimeoutError(MedusaError):
    """Timeout errors"""
    def __init__(self, message: str, timeout_duration: Optional[float] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.TIMEOUT)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        if timeout_duration:
            kwargs.setdefault('additional_data', {}).update({'timeout_duration': timeout_duration})
        super().__init__(message, **kwargs)


class MedusaAuthenticationError(MedusaError):
    """Authentication errors"""
    def __init__(self, message: str, **kwargs):
        kwargs.setdefault('category', ErrorCategory.AUTHENTICATION)
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', " AEGIS SHIELD: Authentication failed. Please check your credentials.")
        super().__init__(message, **kwargs)


class MedusaAuthorizationError(MedusaError):
    """Authorization errors"""
    def __init__(self, message: str, required_permission: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.AUTHORIZATION)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', "Access denied. You don't have permission to perform this operation.")
        if required_permission:
            kwargs.setdefault('additional_data', {}).update({'required_permission': required_permission})
        super().__init__(message, **kwargs)


class MedusaServiceError(MedusaError):
    """Service operation errors"""
    def __init__(self, message: str, service_name: Optional[str] = None, operation: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.SYSTEM)
        kwargs.setdefault('severity', ErrorSeverity.HIGH)
        kwargs.setdefault('user_message', "Service operation failed. Please try again.")
        if service_name:
            kwargs.setdefault('additional_data', {}).update({'service_name': service_name})
        if operation:
            kwargs.setdefault('additional_data', {}).update({'operation': operation})
        super().__init__(message, **kwargs)


class MedusaDataProcessingError(MedusaError):
    """Data processing errors"""
    def __init__(self, message: str, data_type: Optional[str] = None, stage: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.DATA_PROCESSING)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', "Data processing failed. Please check your input data.")
        if data_type:
            kwargs.setdefault('additional_data', {}).update({'data_type': data_type})
        if stage:
            kwargs.setdefault('additional_data', {}).update({'stage': stage})
        super().__init__(message, **kwargs)


class MedusaBusinessLogicError(MedusaError):
    """Business logic validation errors"""
    def __init__(self, message: str, rule: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.BUSINESS_LOGIC)
        kwargs.setdefault('severity', ErrorSeverity.MEDIUM)
        kwargs.setdefault('user_message', "Business rule validation failed.")
        if rule:
            kwargs.setdefault('additional_data', {}).update({'rule': rule})
        super().__init__(message, **kwargs)


class MedusaSystemError(MedusaError):
    """System-level errors"""
    def __init__(self, message: str, component: Optional[str] = None, **kwargs):
        kwargs.setdefault('category', ErrorCategory.SYSTEM)
        kwargs.setdefault('severity', ErrorSeverity.CRITICAL)
        kwargs.setdefault('user_message', "System error occurred. Please contact support.")
        if component:
            kwargs.setdefault('additional_data', {}).update({'component': component})
        super().__init__(message, **kwargs)


class ErrorHandler:
    """Central error handler for consistent error processing"""

    @staticmethod
    def handle_exception(
        exception: Exception,
        component: str,
        operation: str,
        vault_user_id: Optional[str] = None,
        aegis_session_id: Optional[str] = None,
        request_id: Optional[str] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> MedusaError:
        """Convert any exception to MedusaError with proper classification"""

        # If it's already a MedusaError, return as-is
        if isinstance(exception, MedusaError):
            return exception

        # Classify the exception
        category = ErrorCategory.UNKNOWN
        severity = ErrorSeverity.MEDIUM

        # Database errors
        if any(db_keyword in str(exception).lower() for db_keyword in ['database', 'sql', 'connection', 'sqlite']):
            category = ErrorCategory.DATABASE
            severity = ErrorSeverity.HIGH

        # Network errors
        elif any(net_keyword in str(exception).lower() for net_keyword in ['network', 'connection', 'timeout', 'http']):
            category = ErrorCategory.NETWORK
            severity = ErrorSeverity.MEDIUM

        # Validation errors
        elif any(val_keyword in str(exception).lower() for val_keyword in ['validation', 'invalid', 'required']):
            category = ErrorCategory.VALIDATION
            severity = ErrorSeverity.LOW

        # Authentication/Authorization
        elif any(auth_keyword in str(exception).lower() for auth_keyword in ['auth', 'permission', 'unauthorized']):
            category = ErrorCategory.AUTHENTICATION
            severity = ErrorSeverity.HIGH

        # Create MedusaError
        return MedusaError(
            message=str(exception),
            severity=severity,
            category=category,
            component=component,
            operation=operation,
            vault_user_id=vault_user_id,
            aegis_session_id=aegis_session_id,
            request_id=request_id,
            additional_data=additional_context,
            original_exception=exception
        )

    @staticmethod
    def log_and_raise(
        message: str,
        exception_class: type = MedusaError,
        **kwargs
    ):
        """Log and raise an exception in one call"""
        error = exception_class(message, **kwargs)
        raise error

    @staticmethod
    def handle_error(error: MedusaError) -> MedusaError:
        """Handle and log a MedusaError"""
        try:
            # Import here to avoid circular imports

            logger = get_logger(__name__)

            # Log the error with appropriate level
            if error.severity == ErrorSeverity.CRITICAL:
                logger.critical(f"🚨 {error.severity.value.upper()} ERROR: {error.to_dict()}")
            elif error.severity == ErrorSeverity.HIGH:
                logger.error(f"🚨 {error.severity.value.upper()} ERROR: {error.to_dict()}")
            elif error.severity == ErrorSeverity.MEDIUM:
                logger.warning(f" {error.severity.value.upper()} ERROR: {error.to_dict()}")
            else:
                logger.info(f"ℹ️ {error.severity.value.upper()} ERROR: {error.to_dict()}")

            return error

        except Exception as e:
            # Fallback if logging fails
            return error


# Convenience functions for common error scenarios
def validation_error(message: str, field_name: Optional[str] = None, **kwargs):
    """Raise a validation error"""
    raise MedusaValidationError(message, field_name=field_name, **kwargs)


def configuration_error(message: str, config_key: Optional[str] = None, **kwargs):
    """Raise a configuration error"""
    raise MedusaConfigurationError(message, config_key=config_key, **kwargs)


def database_error(message: str, operation: str = "unknown", table: Optional[str] = None, **kwargs):
    """Raise a database error"""
    raise MedusaDatabaseError(message, operation=operation, table=table, **kwargs)


def network_error(message: str, endpoint: Optional[str] = None, status_code: Optional[int] = None, **kwargs):
    """Raise a network error"""
    raise MedusaNetworkError(message, endpoint=endpoint, status_code=status_code, **kwargs)


def ml_model_error(message: str, model_name: Optional[str] = None, **kwargs):
    """Raise an ML model error"""
    raise MedusaMLModelError(message, model_name=model_name, **kwargs)


def prediction_error(message: str, titan_clash_id: Optional[str] = None, **kwargs):
    """Raise a prediction error"""
    raise MedusaPredictionError(message, titan_clash_id=titan_clash_id, **kwargs)


def external_api_error(message: str, api_name: str, endpoint: Optional[str] = None, **kwargs):
    """Raise an external API error"""
    raise MedusaExternalAPIError(message, api_name=api_name, endpoint=endpoint, **kwargs)


# Convenience aliases for easier imports
ValidationError = MedusaValidationError
ServiceError = MedusaServiceError
DataProcessingError = MedusaDataProcessingError
BusinessLogicError = MedusaBusinessLogicError
NetworkError = MedusaNetworkError
DatabaseError = MedusaDatabaseError
AuthenticationError = MedusaAuthenticationError
AuthorizationError = MedusaAuthorizationError
ExternalAPIError = MedusaExternalAPIError
SystemError = MedusaSystemError
ConfigurationError = MedusaConfigurationError


if __name__ == "__main__":
    # Example usage and testing
    try:
        # Example validation error
        validation_error("Invalid game ID format", field_name="titan_clash_id", component="prediction_service")
    except MedusaValidationError as e:
        print(f"🏀 Validation Error: {e}")

    try:
        # Example with exception handling
        try:
            1 / 0 # This will raise ZeroDivisionError
        except Exception as ex:
            error = ErrorHandler.handle_exception(
                ex,
                component="calculation_service",
                operation="divide_numbers"
            )
            raise error
    except MedusaError as e:
        print(f"🏀 MEDUSA Error: {e}")
