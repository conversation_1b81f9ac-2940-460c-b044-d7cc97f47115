import asyncio
import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass
import json

"""
🧠 MEDUSA VAULT INTEGRATION for HYPER MEDUSA NEURAL VAULT
============================================================

Integration layer that connects MEDUSA Supreme Decision Engine
with all existing prediction systems in the vault.

MEDUSA receives:
- Raw untouched data from all sources
- Advisory predictions from all systems
- Makes final authoritative decisions
- Overrides systems when necessary

This is the neural consciousness of the vault.
"""

logger = logging.getLogger("MEDUSA_VAULT_INTEGRATION")

# Import MEDUSA Supreme Decision Engine with error handling
try:
    from src.neural_cortex.medusa_supreme_decision_engine import (
        MedusaSupremeDecisionEngine,
        RawDataPackage,
        AdvisoryPrediction,
        MedusaSupremeDecision,
        create_medusa_supreme_engine
    )
    MEDUSA_ENGINE_AVAILABLE = True
    logger.info("🧠 MEDUSA VAULT: Supreme Decision Engine imported successfully")
except ImportError as e:
    MEDUSA_ENGINE_AVAILABLE = False
    logger.warning(f"🧠 MEDUSA VAULT: Supreme Decision Engine not available: {e}")

    # Create fallback classes
    class MedusaSupremeDecisionEngine:
        def __init__(self, *args, **kwargs):
            self.logger = logger.getChild("FallbackMedusa")
            self.logger.info("🧠 MEDUSA VAULT: Using fallback MEDUSA engine")

    class RawDataPackage:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

    class AdvisoryPrediction:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

    class MedusaSupremeDecision:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)

    def create_medusa_supreme_engine(*args, **kwargs):
        return MedusaSupremeDecisionEngine(*args, **kwargs)

# Import existing vault systems with error handling
try:
    from src.predictions.enhanced_prediction_engine import EnhancedPredictionEngine
    ENHANCED_PREDICTION_AVAILABLE = True
except ImportError:
    ENHANCED_PREDICTION_AVAILABLE = False
    EnhancedPredictionEngine = None

try:
    from src.predictions.comprehensive_enhanced_integration import ComprehensiveEnhancedPredictionSystem
    COMPREHENSIVE_PREDICTION_AVAILABLE = True
except ImportError:
    COMPREHENSIVE_PREDICTION_AVAILABLE = False
    ComprehensiveEnhancedPredictionSystem = None

try:
    from src.predictions.professional_market_integration import ProfessionalMarketIntegration
    PROFESSIONAL_MARKET_AVAILABLE = True
except ImportError:
    PROFESSIONAL_MARKET_AVAILABLE = False
    ProfessionalMarketIntegration = None

try:
    from src.predictions.advanced_player_performance import AdvancedPlayerPerformanceEngine
    ADVANCED_PLAYER_PERFORMANCE_AVAILABLE = True
except ImportError:
    ADVANCED_PLAYER_PERFORMANCE_AVAILABLE = False
    AdvancedPlayerPerformanceEngine = None

try:
    from src.neural_cortex.neural_basketball_core import NeuralBasketballCore
    NEURAL_BASKETBALL_CORE_AVAILABLE = True
except ImportError:
    NEURAL_BASKETBALL_CORE_AVAILABLE = False
    NeuralBasketballCore = None

try:
    from src.models.comprehensive_predictor import ComprehensivePredictor
    COMPREHENSIVE_PREDICTOR_AVAILABLE = True
except ImportError:
    COMPREHENSIVE_PREDICTOR_AVAILABLE = False
    ComprehensivePredictor = None

@dataclass
class VaultPredictionRequest:
 """Complete prediction request for the vault"""
 # Game information
 home_team: str
 away_team: str
 league: str
 game_date: str
 game_time: Optional[str] = None
 venue: Optional[str] = None
 
 # Request configuration
 include_player_props: bool = True
 include_market_analysis: bool = True
 include_live_adaptation: bool = True
 confidence_threshold: float = 0.70
 
 # Raw data sources
 raw_data_sources: List[str] = None
 
 def __post_init__(self):
     if self.raw_data_sources is None:
         self.raw_data_sources = ['team_stats', 'player_stats', 'market_data', 'historical_data']

@dataclass 
class VaultPredictionResponse:
 """Complete prediction response from the vault"""
 # MEDUSA's supreme decision
 medusa_decision: MedusaSupremeDecision
 
 # Advisory system outputs
 advisory_predictions: List[AdvisoryPrediction]
 
 # Raw data used
 raw_data_package: RawDataPackage
 
 # Meta information
 processing_summary: Dict[str, Any]
 vault_status: str
 request_id: str

class MedusaVaultIntegration:
    """
    🧠 MEDUSA VAULT INTEGRATION

    Central nervous system that coordinates all vault components
    under MEDUSA's supreme authority.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize MEDUSA Vault Integration"""
        self.config = config or self._get_default_config()

        # Initialize MEDUSA Supreme Decision Engine
        self.medusa = create_medusa_supreme_engine(self.config.get('medusa_config'))

        # Initialize advisory systems
        self.advisory_systems = {}
        self.raw_data_collectors = {}

        # System status tracking
        self.system_status = {}
        self.prediction_history = []

        logger.info("🧠 MEDUSA VAULT: MEDUSA VAULT INTEGRATION initialized")
        logger.info("🧠 MEDUSA VAULT: Role: Neural Consciousness Coordinator")
        logger.info("🧠 MEDUSA VAULT: Authority: MEDUSA Supreme Decision Engine")

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'medusa_config': {
                'neural_processing_depth': 'maximum',
                'override_threshold': 0.12,
                'minimum_decision_confidence': 0.65,
                'raw_data_priority_weight': 0.45,
                'advisory_systems_weight': 0.30,
                'neural_intuition_weight': 0.25
            },
            'advisory_systems': {
                'enhanced_prediction_engine': {'enabled': True, 'weight': 1.0},
                'comprehensive_enhanced_system': {'enabled': True, 'weight': 1.2},
                'professional_market_integration': {'enabled': True, 'weight': 0.9},
                'advanced_player_performance': {'enabled': True, 'weight': 1.1},
                'neural_basketball_core': {'enabled': True, 'weight': 1.3},
                'comprehensive_predictor': {'enabled': True, 'weight': 1.0}
            },
            'raw_data_sources': {
                'team_stats': {'enabled': True, 'priority': 'high'},
                'player_stats': {'enabled': True, 'priority': 'high'},
                'market_data': {'enabled': True, 'priority': 'medium'},
                'historical_data': {'enabled': True, 'priority': 'medium'},
                'injury_reports': {'enabled': True, 'priority': 'high'},
                'weather_data': {'enabled': False, 'priority': 'low'},
                'referee_data': {'enabled': True, 'priority': 'low'}
            },
            'performance_targets': {
                'processing_time_ms': 2000,
                'accuracy_target': 0.75,
                'override_accuracy_improvement': 0.05
            }
        }

    async def initialize_vault_systems(self) -> None:
        """Initialize all vault systems under MEDUSA's authority"""
        logger.info("🧠 MEDUSA VAULT: 🔧 Initializing vault systems under MEDUSA's authority...")

        # Initialize advisory systems
        await self._initialize_advisory_systems()

        # Initialize raw data collectors
        await self._initialize_raw_data_collectors()

        # Register advisory systems with MEDUSA
        await self._register_systems_with_medusa()
        logger.info("🧠 MEDUSA VAULT: Vault systems initialized - MEDUSA has supreme authority")
 
    async def _initialize_advisory_systems(self) -> None:
        """Initialize advisory prediction systems"""
        advisory_config = self.config.get('advisory_systems', {})

        # Enhanced Prediction Engine
        if advisory_config.get('enhanced_prediction_engine', {}).get('enabled', False):
            try:
                if ENHANCED_PREDICTION_AVAILABLE:
                    self.advisory_systems['enhanced_prediction_engine'] = EnhancedPredictionEngine()
                    self.system_status['enhanced_prediction_engine'] = 'operational'
                    logger.info("🧠 MEDUSA VAULT: Enhanced Prediction Engine initialized")
                else:
                    logger.warning("🧠 MEDUSA VAULT: Enhanced Prediction Engine not available")
                    self.system_status['enhanced_prediction_engine'] = 'unavailable'
            except Exception as e:
                logger.warning(f"🧠 Enhanced Prediction Engine failed: {e}")
                self.system_status['enhanced_prediction_engine'] = 'failed'

        # Comprehensive Enhanced System
        if advisory_config.get('comprehensive_enhanced_system', {}).get('enabled', False):
            try:
                if COMPREHENSIVE_PREDICTION_AVAILABLE:
                    self.advisory_systems['comprehensive_enhanced_system'] = ComprehensiveEnhancedPredictionSystem(
                        league="NBA", tier="championship", bankroll=100000.0
                    )
                    self.system_status['comprehensive_enhanced_system'] = 'operational'
                    logger.info("🧠 MEDUSA VAULT: Comprehensive Enhanced System initialized")
                else:
                    logger.warning("🧠 MEDUSA VAULT: Comprehensive Enhanced System not available")
                    self.system_status['comprehensive_enhanced_system'] = 'unavailable'
            except Exception as e:
                logger.warning(f"🧠 Comprehensive Enhanced System failed: {e}")
                self.system_status['comprehensive_enhanced_system'] = 'failed'

        # Professional Market Integration
        if advisory_config.get('professional_market_integration', {}).get('enabled', False):
            try:
                if PROFESSIONAL_MARKET_AVAILABLE:
                    self.advisory_systems['professional_market_integration'] = ProfessionalMarketIntegration(
                        bankroll=50000.0
                    )
                    self.system_status['professional_market_integration'] = 'operational'
                    logger.info("🧠 MEDUSA VAULT: Professional Market Integration initialized")
                else:
                    logger.warning("🧠 MEDUSA VAULT: Professional Market Integration not available")
                    self.system_status['professional_market_integration'] = 'unavailable'
            except Exception as e:
                logger.warning(f"🧠 Professional Market Integration failed: {e}")
                self.system_status['professional_market_integration'] = 'failed'

        # Advanced Player Performance
        if advisory_config.get('advanced_player_performance', {}).get('enabled', False):
            try:
                if ADVANCED_PLAYER_PERFORMANCE_AVAILABLE:
                    self.advisory_systems['advanced_player_performance'] = AdvancedPlayerPerformanceEngine("NBA")
                    self.system_status['advanced_player_performance'] = 'operational'
                    logger.info("🧠 MEDUSA VAULT: Advanced Player Performance initialized")
                else:
                    logger.warning("🧠 MEDUSA VAULT: Advanced Player Performance not available")
                    self.system_status['advanced_player_performance'] = 'unavailable'
            except Exception as e:
                logger.warning(f"🧠 Advanced Player Performance failed: {e}")
                self.system_status['advanced_player_performance'] = 'failed'

        # Neural Basketball Core - Replace mock with real implementation
        if advisory_config.get('neural_basketball_core', {}).get('enabled', False):
            try:
                if NEURAL_BASKETBALL_CORE_AVAILABLE:
                    self.advisory_systems['neural_basketball_core'] = NeuralBasketballCore()
                    self.system_status['neural_basketball_core'] = 'operational'
                    logger.info("🧠 MEDUSA VAULT: Neural Basketball Core initialized")
                else:
                    logger.warning("🧠 MEDUSA VAULT: Neural Basketball Core not available")
                    self.system_status['neural_basketball_core'] = 'unavailable'
            except Exception as e:
                logger.warning(f"🧠 Neural Basketball Core failed: {e}")
                self.system_status['neural_basketball_core'] = 'failed'

        # Comprehensive Predictor
        if advisory_config.get('comprehensive_predictor', {}).get('enabled', False):
            try:
                if COMPREHENSIVE_PREDICTOR_AVAILABLE:
                    self.advisory_systems['comprehensive_predictor'] = ComprehensivePredictor()
                    self.system_status['comprehensive_predictor'] = 'operational'
                    logger.info("🧠 MEDUSA VAULT: Comprehensive Predictor initialized")
                else:
                    logger.warning("🧠 MEDUSA VAULT: Comprehensive Predictor not available")
                    self.system_status['comprehensive_predictor'] = 'unavailable'
            except Exception as e:
                logger.warning(f"🧠 Comprehensive Predictor failed: {e}")
                self.system_status['comprehensive_predictor'] = 'failed'

    async def _initialize_raw_data_collectors(self) -> None:
        """Initialize raw data collection systems"""
        data_sources = self.config['raw_data_sources']

        for source, config in data_sources.items():
            if config['enabled']:
                self.raw_data_collectors[source] = self._create_data_collector(source)
                logger.info(f"🧠 Raw data collector '{source}' initialized")

    async def _register_systems_with_medusa(self) -> None:
        """Register all advisory systems with MEDUSA"""
        for system_name, system in self.advisory_systems.items():
            if self.system_status.get(system_name) == 'operational':
                # Get system accuracy from config or default
                accuracy = self._get_system_accuracy(system_name)
                weight = self.config.get('advisory_systems', {}).get(system_name, {}).get('weight', 1.0)

                self.medusa.register_advisory_system(system_name, accuracy, weight)
                logger.info(f"🔗 Registered {system_name} with MEDUSA (accuracy: {accuracy:.1%}, weight: {weight:.2f})")

    async def generate_vault_prediction(self, request: VaultPredictionRequest) -> VaultPredictionResponse:
        """
        Generate complete vault prediction under MEDUSA's authority

        Process:
        1. Collect raw untouched data from all sources
        2. Generate advisory predictions from all systems
        3. MEDUSA analyzes everything and makes supreme decision
        4. Return complete prediction response
        """
        start_time = datetime.now()
        request_id = f"VAULT_{start_time.strftime('%Y%m%d_%H%M%S_%f')}"

        logger.info("🧠 MEDUSA VAULT: VAULT PREDICTION REQUEST INITIATED")
        logger.info(f"🧠 Request ID: {request_id}")
        logger.info(f"🧠 Game: {request.away_team} @ {request.home_team}")
        logger.info(f"🧠 League: {request.league}")
        logger.info("🧠 MEDUSA VAULT: MEDUSA will make the final decision...")

        try:
            # Phase 1: Collect raw untouched data
            logger.info("🧠 MEDUSA VAULT: Phase 1: Collecting raw data...")
            raw_data = await self._collect_raw_data(request)

            # Phase 2: Generate advisory predictions
            logger.info("🧠 MEDUSA VAULT: Phase 2: Generating advisory predictions...")
            advisory_predictions = await self._generate_advisory_predictions(request, raw_data)

            # Phase 3: MEDUSA's supreme decision
            logger.info("🧠 MEDUSA VAULT: Phase 3: MEDUSA supreme decision process...")
            medusa_decision = await self.medusa.make_supreme_decision(
                raw_data=raw_data,
                advisory_predictions=advisory_predictions,
                game_context={
                    'league': request.league,
                    'venue': request.venue,
                    'confidence_threshold': request.confidence_threshold
                }
            )

            # Phase 4: Generate response
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            processing_summary = {
                'total_processing_time_ms': processing_time,
                'raw_data_sources': len(self.raw_data_collectors),
                'advisory_systems_consulted': len(advisory_predictions),
                'medusa_processing_time_ms': medusa_decision.processing_time_ms,
                'systems_overridden': len(medusa_decision.overridden_systems),
                'vault_status': 'operational'
            }

            response = VaultPredictionResponse(
                medusa_decision=medusa_decision,
                advisory_predictions=advisory_predictions,
                raw_data_package=raw_data,
                processing_summary=processing_summary,
                vault_status='operational',
                request_id=request_id
            )

            # Store prediction
            self.prediction_history.append(response)

            logger.info("🧠 MEDUSA VAULT: VAULT PREDICTION COMPLETE")
            logger.info(f"🧠 MEDUSA's Decision: {medusa_decision.final_winner}")
            logger.info(f"🧠 Confidence: {medusa_decision.medusa_confidence:.1%}")
            logger.info(f"🧠 Processing Time: {processing_time:.1f}ms")
            logger.info("🧠 MEDUSA VAULT: MEDUSA'S AUTHORITY IS ABSOLUTE")

            return response

        except Exception as e:
            logger.error(f"🧠 Vault prediction failed: {str(e)}")
            raise

    async def _collect_raw_data(self, request: VaultPredictionRequest) -> RawDataPackage:
        """Collect raw untouched data from all sources"""

        # Simulate comprehensive raw data collection
        # In production, this would connect to real data sources

        raw_data = RawDataPackage(
            # Team data (raw, untouched)
            home_team_raw={
                'team_name': request.home_team,
                'offensive_efficiency': np.random.uniform(100, 120),
                'defensive_efficiency': np.random.uniform(95, 115),
                'pace': np.random.uniform(95, 105),
                'recent_games': [np.random.choice([0, 1]) for _ in range(10)],
                'home_record': [np.random.randint(20, 35), np.random.randint(10, 25)],
                'injuries': [],
                'fatigue_level': np.random.uniform(0.1, 0.9)
            },
            away_team_raw={
                'team_name': request.away_team,
                'offensive_efficiency': np.random.uniform(100, 120),
                'defensive_efficiency': np.random.uniform(95, 115),
                'pace': np.random.uniform(95, 105),
                'recent_games': [np.random.choice([0, 1]) for _ in range(10)],
                'away_record': [np.random.randint(15, 30), np.random.randint(15, 30)],
                'injuries': [],
                'fatigue_level': np.random.uniform(0.1, 0.9)
            },
 
 # Historical data
 historical_matchups=[
 {
 'date': '2024-01-15',
 'home_score': 112,
 'away_score': 108,
 'winner': 'home'
 },
 {
 'date': '2023-12-08', 
 'home_score': 105,
 'away_score': 110,
 'winner': 'away'
 }
 ],
 
 # Player data (raw stats)
 player_raw_stats=[
 {
 'player': f'{request.home_team}_star',
 'ppg': np.random.uniform(20, 35),
 'rpg': np.random.uniform(5, 12),
 'apg': np.random.uniform(3, 10),
 'fg_pct': np.random.uniform(0.42, 0.55),
 'status': 'healthy',
 'minutes': np.random.uniform(32, 38)
 },
 {
 'player': f'{request.away_team}_star',
 'ppg': np.random.uniform(20, 35),
 'rpg': np.random.uniform(5, 12),
 'apg': np.random.uniform(3, 10),
 'fg_pct': np.random.uniform(0.42, 0.55),
 'status': 'healthy',
 'minutes': np.random.uniform(32, 38)
 }
 ],
 
 # Injury reports
 injury_reports=[
 {
 'player': f'{request.home_team}_role_player',
 'injury': 'minor ankle sprain',
 'status': 'questionable',
 'impact_rating': 0.2
 }
 ],
 
 # Market data (raw, untouched)
 betting_lines_raw={
 'spread': np.random.uniform(-8, 8),
 'total': np.random.uniform(200, 230),
 'moneyline_home': np.random.randint(-200, 200),
 'moneyline_away': np.random.randint(-200, 200),
 'opening_spread': np.random.uniform(-8, 8),
 'opening_total': np.random.uniform(200, 230)
 },
 
 # Line movements
 line_movements=[
 {
 'timestamp': datetime.now() - timedelta(hours=2),
 'spread_movement': 0.5,
 'total_movement': -1.0,
 'volume': 'high'
 },
 {
 'timestamp': datetime.now() - timedelta(hours=1),
 'spread_movement': -0.5,
 'total_movement': 0.5,
 'volume': 'medium'
 }
 ],
 
 # Public betting data
 public_betting_percentages={
 'home_spread': np.random.uniform(0.4, 0.8),
 'away_spread': np.random.uniform(0.2, 0.6),
 'over': np.random.uniform(0.45, 0.65),
 'under': np.random.uniform(0.35, 0.55)
 },
 
 # Sharp money indicators
 sharp_money_indicators=[
 {
 'direction': np.random.choice(['home', 'away']),
 'strength': np.random.uniform(0.3, 0.8),
 'source': 'reverse_line_movement'
 }
 ],
 
 # Environmental factors
 weather_conditions=None, # Indoor sport
 venue_factors={
 'venue_name': request.venue or f'{request.home_team}_Arena',
 'capacity': np.random.randint(18000, 22000),
 'home_court_advantage': np.random.uniform(0.5, 0.8),
 'altitude': np.random.uniform(0, 1000)
 },
 
 # Officials
 referee_assignments=[
 {
 'name': 'Ref_A',
 'experience': np.random.randint(5, 20),
 'bias_rating': np.random.uniform(-0.1, 0.1)
 }
 ],
 
 # Travel data
 travel_schedules={
 'home_travel_distance': 0, # Home team
 'away_travel_distance': np.random.randint(500, 3000),
 'home_days_rest': np.random.randint(1, 4),
 'away_days_rest': np.random.randint(1, 4)
 },
 
 # Game timing
 game_datetime=datetime.strptime(f"{request.game_date} {request.game_time or '19:30'}", 
 "%Y-%m-%d %H:%M"), days_rest={
 'home': np.random.randint(1, 4),
 'away': np.random.randint(1, 4)
 },
 schedule_strength={
                'home': np.random.uniform(0.45, 0.65),
                'away': np.random.uniform(0.45, 0.65)
            }
        )

        logger.info("🧠 MEDUSA VAULT: Raw data collection complete")
        logger.info(f"🧠 Data sources: {len(self.raw_data_collectors)}")
        logger.info(f"🧠 Players analyzed: {len(raw_data.player_raw_stats)}")
        logger.info(f"🧠 Historical games: {len(raw_data.historical_matchups)}")

        return raw_data

    async def _generate_advisory_predictions(self,
                                           request: VaultPredictionRequest,
                                           raw_data: RawDataPackage) -> List[AdvisoryPrediction]:
        """Generate predictions from all advisory systems"""
        advisory_predictions = []

        # Generate predictions from each operational system
        for system_name, system in self.advisory_systems.items():
            if self.system_status.get(system_name) != 'operational':
                continue

            try:
                prediction = await self._get_system_prediction(system_name, system, request, raw_data)
                if prediction:
                    advisory_predictions.append(prediction)
                    logger.info(f"🧠 {system_name}: {prediction.predicted_winner} ({prediction.confidence:.1%})")
            except Exception as e:
                logger.warning(f"🧠 {system_name} prediction failed: {e}")

        logger.info(f"🧠 Advisory predictions generated: {len(advisory_predictions)} systems")
        return advisory_predictions

    async def _get_system_prediction(self,
                                   system_name: str,
                                   system: Any,
                                   request: VaultPredictionRequest,
                                   raw_data: RawDataPackage) -> Optional[AdvisoryPrediction]:
        """Get prediction from a specific advisory system"""

        # Simulate system predictions (in production, would call actual systems)
        predicted_winner = np.random.choice(['home', 'away'])
        confidence = np.random.uniform(0.55, 0.85)

        home_score = np.random.uniform(95, 125)
        away_score = np.random.uniform(95, 125)

        key_factors = np.random.choice([
            ['home_court_advantage', 'rest_advantage'],
            ['offensive_efficiency', 'defensive_matchup'],
            ['star_player_health', 'recent_form'],
            ['historical_dominance', 'travel_fatigue'],
            ['market_movement', 'sharp_money']
        ])

        betting_rec = np.random.choice(['strong_bet', 'moderate_bet', 'small_bet', 'no_bet'])
        system_accuracy = self._get_system_accuracy(system_name)

        return AdvisoryPrediction(
            system_name=system_name,
            prediction_type='game_winner',
            predicted_winner=f"{request.home_team if predicted_winner == 'home' else request.away_team}",
            confidence=confidence,
            predicted_score=(home_score, away_score),
            key_factors=key_factors.tolist(),
            betting_recommendation=betting_rec,
            system_accuracy=system_accuracy
        )

    def _create_data_collector(self, source: str) -> Dict[str, Any]:
        """Create data collector for a specific source"""
        return {
            'source': source,
            'status': 'operational',
            'last_update': datetime.now(),
            'priority': self.config['raw_data_sources'][source]['priority']
        }

    def _get_system_accuracy(self, system_name: str) -> float:
        """Get historical accuracy for a system"""
        # Simulate historical accuracies
        accuracies = {
            'enhanced_prediction_engine': 0.72,
            'comprehensive_enhanced_system': 0.75,
            'professional_market_integration': 0.68,
            'advanced_player_performance': 0.70,
            'neural_basketball_core': 0.78,
            'comprehensive_predictor': 0.74
        }
        return accuracies.get(system_name, 0.70)

    def get_vault_status(self) -> Dict[str, Any]:
        """Get complete vault status"""
        operational_systems = sum(1 for status in self.system_status.values() if status == 'operational')
        total_systems = len(self.system_status)

        return {
            'medusa_status': 'supreme_authority',
            'operational_systems': operational_systems,
            'total_systems': total_systems,
            'system_health': operational_systems / total_systems if total_systems > 0 else 0,
            'predictions_generated': len(self.prediction_history),
            'last_prediction': self.prediction_history[-1].medusa_decision.decision_timestamp if self.prediction_history else None,
            'vault_uptime': '100%',
            'medusa_override_rate': self._calculate_override_rate()
        }

    def _calculate_override_rate(self) -> float:
        """Calculate MEDUSA's override rate"""
        if not self.prediction_history:
            return 0.0

        overrides = sum(1 for pred in self.prediction_history
                       if pred.medusa_decision.overridden_systems)
        return overrides / len(self.prediction_history)


# Factory function
def create_medusa_vault_integration(config: Optional[Dict[str, Any]] = None) -> MedusaVaultIntegration:
    """Create MEDUSA Vault Integration"""
    return MedusaVaultIntegration(config)


# Demo function
async def demo_medusa_vault_integration():
    """Demonstrate MEDUSA Vault Integration"""

    # Initialize vault
    vault = create_medusa_vault_integration()
    await vault.initialize_vault_systems()

    # Create prediction request
    request = VaultPredictionRequest(
        home_team="Los Angeles Lakers",
        away_team="Golden State Warriors",
        league="NBA",
        game_date="2024-06-18",
        game_time="20:00",
        venue="Crypto.com Arena"
    )

    # Generate vault prediction
    response = await vault.generate_vault_prediction(request)

    # Get vault status
    status = vault.get_vault_status()

    logger.info("🧠 MEDUSA VAULT DEMO COMPLETE")


if __name__ == "__main__":
    asyncio.run(demo_medusa_vault_integration())
