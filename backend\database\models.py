from typing import Dict, Any, List, Optional, TYPE_CHECKING, Union
from sqlalchemy import (
    Column, Integer, String, DateTime, Boolean, Text, Float, ForeignKey,
    Index, UniqueConstraint, CheckConstraint, func, text
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.sql import expression
from datetime import datetime, timezone
import uuid
import enum
import secrets
from pydantic import BaseModel, validator, Field
from kingdom.config.unified_config_system import UnifiedConfigSystem
import os
import logging
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Additional SQLAlchemy imports for basketball database models
try:
    from backend.config import config, ConfigManager
except ImportError:
    # Fallback configuration
    class ConfigManager:
        def __init__(self):
            self.config = None

"""
Expert-Level Database Models and Schema
=====================================

Comprehensive database schema definition for the HYPER MEDUSA NEURAL VAULT with:
- Expert-level SQLAlchemy models with async compatibility
- Advanced indexing and performance optimization
- Data validation and constraints
- Production-ready relationship management
- Integration with monitoring and audit systems
- Full authentication and expert context integration
- MEDUSA VAULT quantum operations support
"""

# Database-agnostic JSON column type
def get_json_type():
    """Get appropriate JSON column type based on database."""
    try:
        # Try to use JSONB for PostgreSQL
        return JSONB
    except:
        # Fall back to JSON for SQLite and other databases
        return JSON

# Use database-agnostic JSON type
JSONType = get_json_type()

# Authentication and dependency integration
try:
    from vault_oracle.core.auth import (
        User, ExpertContext, MedusaVaultContext,
        verify_permission, check_access_level
    )
    AUTH_INTEGRATION_AVAILABLE = True
except ImportError:
    AUTH_INTEGRATION_AVAILABLE = False

# Configuration integration
try:
    CONFIG_INTEGRATION_AVAILABLE = True
except ImportError:
    try:
        # Fallback to alternative config import paths
        CONFIG_INTEGRATION_AVAILABLE = True
    except ImportError:
        CONFIG_INTEGRATION_AVAILABLE = False

# Use the same Base as our expert CRUD implementation
Base = declarative_base()

# Pydantic Models for API responses and data validation
class BaseStatsModel(BaseModel):
 """Base statistics model with common fields"""
 games_played: int = Field(0, description="Number of games played")
 minutes: float = Field(0.0, description="Average minutes played per game")
 fg_pct: float = Field(0.0, description="Field Goal Percentage")
 three_pt_pct: float = Field(0.0, description="Three Point Percentage")
 ft_pct: float = Field(0.0, description="Free Throw Percentage")
 rebounds: float = Field(0.0, description="Average rebounds per game")
 assists: float = Field(0.0, description="Average assists per game")
 steals: float = Field(0.0, description="Average steals per game")
 blocks: float = Field(0.0, description="Average blocks per game")
 turnovers: float = Field(0.0, description="Average turnovers per game")
 points: float = Field(0.0, description="Average points per game")

class PlayerStats(BaseStatsModel):
 """Player statistics model for API responses"""
 hero_id: str = Field(..., description="Unique ID of the player")
 player_name: str = Field(..., description="Name of the player")
 mythic_roster_id: str = Field(..., description="ID of the player's team")
 position: str = Field(..., description="Player's primary position")
 usage_rate: float = Field(0.0, description="Player usage rate")
 per: float = Field(0.0, description="Player efficiency rating")
 plus_minus: float = Field(0.0, description="Plus/minus rating")

class TeamStats(BaseStatsModel):
 """Team statistics model for API responses"""
 mythic_roster_id: str = Field(..., description="Unique ID of the team")
 team_name: str = Field(..., description="Name of the team")
 wins: int = Field(0, description="Number of wins")
 losses: int = Field(0, description="Number of losses")
 win_pct: float = Field(0.0, description="Win percentage")
 offensive_rating: float = Field(0.0, description="Team's offensive rating")
 defensive_rating: float = Field(0.0, description="Team's defensive rating")
 net_rating: float = Field(0.0, description="Net rating (offensive - defensive)")
 pace: float = Field(0.0, description="Team's pace of play")
 true_shooting_pct: float = Field(0.0, description="True shooting percentage")

class TeamRankings(BaseModel):
 """Team rankings model for API responses"""
 mythic_roster_id: str = Field(..., description="Unique ID of the team")
 team_name: str = Field(..., description="Name of the team")
 overall_rank: int = Field(0, description="Overall team ranking")
 offensive_rank: int = Field(0, description="Offensive ranking")
 defensive_rank: int = Field(0, description="Defensive ranking")
 pace_rank: int = Field(0, description="Pace ranking")
 net_rating_rank: int = Field(0, description="Net rating ranking")

class GameData(BaseModel):
 """Game data model for predictions and analysis"""
 titan_clash_id: str = Field(..., description="Unique game identifier")
 home_team_id: str = Field(..., description="Home team ID")
 away_team_id: str = Field(..., description="Away team ID")
 game_date: str = Field(..., description="Game date (ISO format)")
 game_time: Optional[str] = Field(None, description="Game time")
 venue: Optional[str] = Field(None, description="Game venue")
 home_team_stats: Optional[TeamStats] = None
 away_team_stats: Optional[TeamStats] = None
 player_stats: List[PlayerStats] = Field(default=[], description="Player statistics for the game")

class PredictionResult(BaseModel):
 """Prediction result model"""
 titan_clash_id: str = Field(..., description="Game identifier")
 predicted_winner_id: str = Field(..., description="Predicted winning team ID")
 win_probability: float = Field(..., description="Win probability (0-1)")
 predicted_score_home: int = Field(..., description="Predicted home team score")
 predicted_score_away: int = Field(..., description="Predicted away team score")
 predicted_spread: float = Field(0.0, description="Predicted point spread")
 predicted_total: float = Field(0.0, description="Predicted total points")
 confidence: float = Field(0.0, description="Prediction confidence (0-1)")
 key_factors: List[str] = Field(default=[], description="Key factors in the prediction")
 timestamp: datetime = Field(default_factory=datetime.now, description="Prediction timestamp")

# ===================================================================
# ENUMERATIONS
# ===================================================================

class UserRole(enum.Enum):
 """User role enumeration"""
 USER = "user"
 PREMIUM = "premium"
 ADMIN = "admin"
 ANALYST = "analyst"


class SubscriptionTier(enum.Enum):
 """Subscription tier enumeration"""
 BASIC = "basic"
 PRO = "pro"
 ENTERPRISE = "enterprise"


class GameStatus(enum.Enum):
 """Game status enumeration"""
 SCHEDULED = "scheduled"
 LIVE = "live"
 FINAL = "final"
 POSTPONED = "postponed"
 CANCELLED = "cancelled"


class BetResult(enum.Enum):
 """Betting result enumeration"""
 WIN = "win"
 LOSS = "loss"
 PUSH = "push"
 PENDING = "pending"
 CANCELLED = "cancelled"


class AchievementType(enum.Enum):
 """Achievement type enumeration"""
 BETTING = "betting"
 ACCURACY = "accuracy"
 STREAK = "streak"
 MILESTONE = "milestone"
 SOCIAL = "social"
 SYSTEM = "system"


class AchievementRarity(enum.Enum):
 """Achievement rarity levels"""
 COMMON = "common"
 UNCOMMON = "uncommon"
 RARE = "rare"
 EPIC = "epic"
 LEGENDARY = "legendary"


# ===================================================================
# SQLALCHEMY MODELS
# ===================================================================

class UserModel(Base):
 """Expert-level user account management with enhanced security and tracking"""
 __tablename__ = "users"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 email = Column(String, unique=True, nullable=False, index=True)
 username = Column(String, unique=True, nullable=False, index=True)
 hashed_password = Column(String, nullable=False)
 api_key = Column(String, unique=True, index=True)
 role = Column(String, default=UserRole.USER.value, index=True)
 subscription_tier = Column(String, default=SubscriptionTier.BASIC.value, index=True)
 
 # Account status and security
 is_active = Column(Boolean, default=True, index=True)
 is_verified = Column(Boolean, default=False)
 failed_login_attempts = Column(Integer, default=0)
 last_login_attempt = Column(DateTime)
 password_reset_token = Column(String)
 password_reset_expires = Column(DateTime)
 
 # Subscription and billing
 subscription_start = Column(DateTime)
 subscription_end = Column(DateTime)
 billing_customer_id = Column(String) # Stripe/payment provider ID
 
 # Usage tracking
 api_calls_today = Column(Integer, default=0)
 api_calls_month = Column(Integer, default=0)
 last_api_call = Column(DateTime)
  # Preferences timezone = Column(String, default="UTC")
 notification_preferences = Column(JSONType)
 dashboard_settings = Column(JSONType)
 
 # Expert Context and MEDUSA VAULT Integration
 expert_level = Column(String, default="novice", index=True)  # novice, intermediate, expert, vault_master
 expert_since = Column(DateTime)
 vault_operations = Column(Integer, default=0)
 quantum_access_level = Column(String, default="basic", index=True)  # basic, advanced, quantum, neural
 vault_session_tokens = Column(ARRAY(String))  # Active vault session tokens neural_patterns = Column(JSONType)  # User's neural network patterns
 cognitive_preferences = Column(JSONType)  # AI interaction preferences
 
 # Advanced Security and Permissions permission_flags = Column(JSONType)  # Detailed permission system
 access_history = Column(JSONType)  # Recent access patterns
 security_clearance = Column(String, default="basic", index=True)
 two_factor_enabled = Column(Boolean, default=False)
 backup_codes = Column(ARRAY(String))
 
 # Performance Tracking
 prediction_streak = Column(Integer, default=0)
 expertise_score = Column(Float, default=0.0, index=True)
 vault_mastery_level = Column(Float, default=0.0)
 neural_synchronization = Column(Float, default=0.0)
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 last_login = Column(DateTime, index=True)
 
 # Relationships
 sessions = relationship("UserSessionModel", back_populates="user", cascade="all, delete-orphan")
 achievements = relationship("UserAchievementModel", back_populates="user", cascade="all, delete-orphan")
 stats = relationship("UserStatsModel", back_populates="user", uselist=False, cascade="all, delete-orphan")
 vault_operations_history = relationship("VaultOperationModel", back_populates="user", cascade="all, delete-orphan")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_users_active_role', 'is_active', 'role'),
 Index('idx_users_subscription', 'subscription_tier', 'subscription_end'),
 Index('idx_users_api_usage', 'api_calls_today', 'last_api_call'),
 CheckConstraint('failed_login_attempts >= 0', name='check_failed_attempts_positive'), CheckConstraint("email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'", name='check_email_format'),
 )
 
 @validates('role')
 def validate_role(self, key, role):
     if role not in [r.value for r in UserRole]:
         raise ValueError(f"Invalid role: {role}")
     return role
 
 @validates('subscription_tier')
 def validate_subscription_tier(self, key, tier):
     if tier not in [t.value for t in SubscriptionTier]:
         raise ValueError(f"Invalid subscription tier: {tier}")
     return tier

 # ===================================================================
 # AUTHENTICATION AND EXPERT CONTEXT INTEGRATION METHODS
 # ===================================================================

 def to_auth_user(self) -> Optional["User"]:
     """Convert SQLAlchemy model to authentication User object"""
     if not AUTH_INTEGRATION_AVAILABLE:
         return None
     
     try:
         # Create the authentication User object
         auth_user = User(
             user_id=self.id,
             username=self.username,
             email=self.email,
             role=self.role,
             subscription_tier=self.subscription_tier,
             is_active=self.is_active,
             api_calls_today=self.api_calls_today,
             api_calls_month=self.api_calls_month,
             permissions=self.permission_flags or {},
             expert_level=self.expert_level,
             vault_operations=self.vault_operations
         )
         return auth_user
     except Exception:
         return None

 def create_expert_context(self) -> Optional['ExpertContext']:
     """Create expert context for advanced operations"""
     if not AUTH_INTEGRATION_AVAILABLE:
         return None
     
     auth_user = self.to_auth_user()
     if not auth_user:
         return None
     
     try:
         expert_context = ExpertContext(
             user=auth_user,
             expert_level=self.expert_level,
             expert_since=self.expert_since,
             vault_operations=self.vault_operations,
             expertise_score=self.expertise_score,
             neural_synchronization=self.neural_synchronization
         )
         return expert_context
     except Exception:
         return None

 def create_vault_context(self, operation: str) -> Optional['MedusaVaultContext']:
     """Create MEDUSA VAULT context for quantum operations"""
     if not AUTH_INTEGRATION_AVAILABLE:
         return None
     
     auth_user = self.to_auth_user()
     if not auth_user:
         return None
     
     try:
         vault_context = MedusaVaultContext(
             user=auth_user,
             operation=operation,
             vault_session_id=secrets.token_hex(32),
             quantum_signature=secrets.token_hex(16)
         )
         return vault_context
     except Exception:
         return None

 def has_permission(self, permission: str) -> bool:
     """Check if user has specific permission"""
     if not AUTH_INTEGRATION_AVAILABLE:
         return True  # Fallback to allow access
     
     auth_user = self.to_auth_user()
     if not auth_user:
         return False
     
     return verify_permission(auth_user, permission)

 def get_access_level(self) -> str:
     """Get user's access level"""
     if not AUTH_INTEGRATION_AVAILABLE:
         return "basic"
     
     auth_user = self.to_auth_user()
     if not auth_user:
         return "basic"
     
     return check_access_level(auth_user)

 def is_expert_user(self) -> bool:
     """Check if user has expert-level access"""
     return self.expert_level in ["expert", "vault_master"] and self.has_permission("expert")

 def is_vault_user(self) -> bool:
     """Check if user has MEDUSA VAULT access"""
     return self.quantum_access_level in ["quantum", "neural"] and self.has_permission("vault_access")

 def update_vault_activity(self, operation_type: str):
     """Update vault activity metrics"""
     self.vault_operations += 1
     self.last_prophecy_update = datetime.now(timezone.utc)
     
     # Update neural patterns
     if not self.neural_patterns:
         self.neural_patterns = {}
     
     if operation_type not in self.neural_patterns:
         self.neural_patterns[operation_type] = 0
     self.neural_patterns[operation_type] += 1

 def calculate_expertise_score(self) -> float:
     """Calculate user's expertise score based on activity and performance"""
     base_score = 0.0
     
     # Factor in vault operations
     base_score += min(self.vault_operations * 0.1, 50.0)
     
     # Factor in prediction streak
     base_score += min(self.prediction_streak * 0.5, 25.0)
     
     # Factor in expert level
     level_multipliers = {
         "novice": 1.0,
         "intermediate": 1.5,
         "expert": 2.0,
         "vault_master": 3.0
     }
     base_score *= level_multipliers.get(self.expert_level, 1.0)
     
     # Factor in subscription tier
     tier_bonuses = {
         "basic": 0,
         "pro": 10,
         "enterprise": 25
     }
     base_score += tier_bonuses.get(self.subscription_tier, 0)
     
     self.expertise_score = min(base_score, 100.0)
     return self.expertise_score


class UserSessionModel(Base):
 """User session tracking for security and analytics"""
 __tablename__ = "user_sessions"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 vault_user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
 session_token = Column(String, unique=True, nullable=False, index=True)
 
 # Session metadata
 ip_address = Column(String)
 user_agent = Column(Text)
 device_type = Column(String) # web, mobile, api
 location = Column(JSONType) # Geo location data
 
 # Session timing
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_activity = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 expires_at = Column(DateTime, nullable=False, index=True)
 is_active = Column(Boolean, default=True, index=True)
 
 # Relationships
 user = relationship("UserModel", back_populates="sessions")
 
 __table_args__ = (
 Index('idx_sessions_user_active', 'vault_user_id', 'is_active'),
 Index('idx_sessions_expiry', 'expires_at', 'is_active'),
 )


class GameModel(Base):
 """Expert-level NBA game records with comprehensive tracking and analytics"""
 __tablename__ = "games"
 
 id = Column(String, primary_key=True) # NBA API game ID
 season = Column(String, nullable=False, index=True)
 season_type = Column(String, default="Regular Season", index=True) # Regular Season, Playoffs, Preseason
 date = Column(DateTime, nullable=False, index=True)
 
 # Team information
 home_team_id = Column(String, ForeignKey("teams.id"), nullable=False, index=True)
 away_team_id = Column(String, ForeignKey("teams.id"), nullable=False, index=True)
 venue = Column(String)
 
 # Game state
 status = Column(String, default=GameStatus.SCHEDULED.value, index=True)
 quarter = Column(Integer)
 time_remaining = Column(String)
 officials = Column(JSONType) # List of game officials
 
 # Scores
 home_score = Column(Integer)
 away_score = Column(Integer)
 home_score_q1 = Column(Integer)
 home_score_q2 = Column(Integer)
 home_score_q3 = Column(Integer)
 home_score_q4 = Column(Integer)
 away_score_q1 = Column(Integer)
 away_score_q2 = Column(Integer)
 away_score_q3 = Column(Integer)
 away_score_q4 = Column(Integer)
 
 # Betting lines (tracked over time)
 spread_home = Column(Float)
 spread_away = Column(Float)
 total_points = Column(Float)
 moneyline_home = Column(Integer)
 moneyline_away = Column(Integer)
 
 # Opening lines (for line movement analysis)
 opening_spread_home = Column(Float)
 opening_total = Column(Float)
 opening_moneyline_home = Column(Integer)
 opening_moneyline_away = Column(Integer)
 
 # Advanced game metrics
 pace = Column(Float)
 home_efficiency = Column(Float)
 away_efficiency = Column(Float)
 home_defensive_rating = Column(Float)
 away_defensive_rating = Column(Float)
 
 # Situational factors
 back_to_back_home = Column(Boolean, default=False)
 back_to_back_away = Column(Boolean, default=False)
 rest_days_home = Column(Integer)
 rest_days_away = Column(Integer)
 travel_distance_away = Column(Float)
 
 # Weather and external factors (for outdoor games/travel)
 weather_conditions = Column(JSONType)
 injury_report = Column(JSONType)
 
 # Data quality and freshness
 last_updated = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 data_source = Column(String, default="HOOPS_PANTHEON_API")
 data_quality_score = Column(Float)
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 
 # Relationships
 home_team = relationship("TeamModel", foreign_keys=[home_team_id], back_populates="home_games")
 away_team = relationship("TeamModel", foreign_keys=[away_team_id], back_populates="away_games")
 predictions = relationship("GamePredictionModel", back_populates="game", cascade="all, delete-orphan")
 player_stats = relationship("PlayerGameStatModel", back_populates="game", cascade="all, delete-orphan")
 line_movements = relationship("LineMovementModel", back_populates="game", cascade="all, delete-orphan")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_games_date_status', 'date', 'status'),
 Index('idx_games_teams', 'home_team_id', 'away_team_id'),
 Index('idx_games_season_type', 'season', 'season_type'),
 Index('idx_games_betting', 'spread_home', 'total_points'),
 UniqueConstraint('home_team_id', 'away_team_id', 'date', name='uq_game_teams_date'), CheckConstraint('quarter >= 1 AND quarter <= 4', name='check_quarter_valid'),
 CheckConstraint('home_score >= 0', name='check_home_score_positive'), CheckConstraint('away_score >= 0', name='check_away_score_positive'),
 )
 
 @validates('status')
 def validate_status(self, key, status):
     if status not in [s.value for s in GameStatus]:
         raise ValueError(f"Invalid game status: {status}")
     return status

 # ===================================================================
 # AUTHENTICATION AND ACCESS CONTROL METHODS
 # ===================================================================

 def is_accessible_to_user(self, user_access_level: str) -> bool:
     """Check if game data is accessible based on user access level"""
     if user_access_level in ["admin", "expert", "vault"]:
         return True
     elif user_access_level == "premium":
         # Premium users can access most games
         return True
     elif user_access_level == "basic":
         # Basic users have limited access
         return self.status != GameStatus.SCHEDULED.value
     else:
         return False

 def get_filtered_data(self, user_access_level: str) -> Dict[str, Any]:
     """Get filtered game data based on user access level"""
     base_data = {
         "id": self.id,
         "date": self.date.isoformat() if self.date else None,
         "home_team_id": self.home_team_id,
         "away_team_id": self.away_team_id,
         "status": self.status,
         "venue": self.venue
     }
     
     if user_access_level in ["admin", "expert", "vault"]:
         # Full data access for expert users
         base_data.update({
             "spread_home": self.spread_home,
             "total_points": self.total_points,
             "moneyline_home": self.moneyline_home,
             "moneyline_away": self.moneyline_away,
             "opening_spread_home": self.opening_spread_home,
             "opening_total": self.opening_total,
             "pace": self.pace,
             "home_efficiency": self.home_efficiency,
             "away_efficiency": self.away_efficiency,
             "injury_report": self.injury_report,
             "weather_conditions": self.weather_conditions
         })
     elif user_access_level == "premium":
         # Premium users get betting lines and basic analytics
         base_data.update({
             "spread_home": self.spread_home,
             "total_points": self.total_points,
             "moneyline_home": self.moneyline_home,
             "moneyline_away": self.moneyline_away
         })
     
     # Add scores if game is not scheduled
     if self.status != GameStatus.SCHEDULED.value:
         base_data.update({
             "home_score": self.home_score,
             "away_score": self.away_score
         })
     
     return base_data

 def requires_expert_access(self) -> bool:
     """Check if game requires expert-level access for full data"""
     return bool(self.injury_report or self.weather_conditions or self.pace)


class LineMovementModel(Base):
 """Track betting line movements over time for analysis"""
 __tablename__ = "line_movements"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 titan_clash_id = Column(String, ForeignKey("games.id", ondelete="CASCADE"), nullable=False, index=True)
 sportsbook = Column(String, nullable=False, index=True)
 
 # Line data
 spread_home = Column(Float)
 total_points = Column(Float)
 moneyline_home = Column(Integer)
 moneyline_away = Column(Integer)
 
 # Movement context
 timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 volume_indicator = Column(Float) # Betting volume at this line
 sharp_money_indicator = Column(Boolean) # Professional bettor activity
 
 # Relationships
 game = relationship("GameModel", back_populates="line_movements")
 
 __table_args__ = (
 Index('idx_line_movements_game_time', 'titan_clash_id', 'timestamp'),
 Index('idx_line_movements_book', 'sportsbook', 'timestamp'),
 )


class TeamModel(Base):
 """Expert-level NBA team information with comprehensive analytics"""
 __tablename__ = "teams"
 
 id = Column(String, primary_key=True) # NBA team ID
 name = Column(String, nullable=False, index=True)
 full_name = Column(String, nullable=False)
 abbreviation = Column(String, nullable=False, unique=True, index=True)
 city = Column(String, nullable=False)
 conference = Column(String, nullable=False, index=True)
 division = Column(String, nullable=False, index=True)
 
 # Team branding and info
 primary_color = Column(String)
 secondary_color = Column(String)
 logo_url = Column(String)
 founded_year = Column(Integer)
 arena_name = Column(String)
 arena_capacity = Column(Integer)
 
 # Current season record
 season = Column(String, nullable=False, index=True)
 wins = Column(Integer, default=0)
 losses = Column(Integer, default=0)
 win_percentage = Column(Float, index=True)
 
 # Advanced team statistics
 offensive_rating = Column(Float, index=True)
 defensive_rating = Column(Float, index=True)
 net_rating = Column(Float, index=True)
 pace = Column(Float)
 true_shooting_pct = Column(Float)
 effective_fg_pct = Column(Float)
 turnover_pct = Column(Float)
 offensive_rebound_pct = Column(Float)
 defensive_rebound_pct = Column(Float)
 
 # Situational performance
 home_record = Column(String) # "W-L" format
 away_record = Column(String)
 last_10_record = Column(String)
 vs_conference_record = Column(String)
 vs_division_record = Column(String)
 
 # Trends and momentum
 current_streak = Column(String) # "W5" or "L3"
 power_ranking = Column(Integer)
 strength_of_schedule = Column(Float)
 remaining_strength_of_schedule = Column(Float)
 
 # Injury and roster health
 injury_count = Column(Integer, default=0)
 roster_stability_score = Column(Float)
 
 # Performance metrics
 avg_margin_of_victory = Column(Float)
 avg_margin_of_defeat = Column(Float)
 games_within_5pts = Column(Integer)
 overtime_record = Column(String)
 
 # Audit and data quality
 last_stats_update = Column(DateTime, index=True)
 data_completeness_score = Column(Float)
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 
 # Relationships
 players = relationship("PlayerModel", back_populates="team")
 home_games = relationship("GameModel", foreign_keys="[GameModel.home_team_id]", back_populates="home_team")
 away_games = relationship("GameModel", foreign_keys="[GameModel.away_team_id]", back_populates="away_team")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_teams_conference_division', 'conference', 'division'),
 Index('idx_teams_performance', 'win_percentage', 'net_rating'),
 Index('idx_teams_season_stats', 'season', 'offensive_rating', 'defensive_rating'),
 CheckConstraint('wins >= 0', name='check_wins_positive'),
 CheckConstraint('losses >= 0', name='check_losses_positive'),
 CheckConstraint('win_percentage >= 0 AND win_percentage <= 1', name='check_win_pct_valid'),
 CheckConstraint('arena_capacity > 0', name='check_arena_capacity_positive'),
 )

 # ===================================================================
 # AUTHENTICATION AND ACCESS CONTROL METHODS
 # ===================================================================

 def is_accessible_to_user(self, user_access_level: str) -> bool:
     """Check if team data is accessible based on user access level"""
     if user_access_level in ["admin", "expert", "vault"]:
         return True
     elif user_access_level in ["premium", "basic"]:
         # All users can access basic team information
         return True
     else:
         return False

 def get_filtered_data(self, user_access_level: str) -> Dict[str, Any]:
     """Get filtered team data based on user access level"""
     base_data = {
         "id": self.id,
         "name": self.name,
         "full_name": self.full_name,
         "abbreviation": self.abbreviation,
         "city": self.city,
         "conference": self.conference,
         "division": self.division,
         "wins": self.wins,
         "losses": self.losses,
         "win_percentage": self.win_percentage
     }
     
     if user_access_level in ["admin", "expert", "vault"]:
         # Full data access for expert users
         base_data.update({
             "offensive_rating": self.offensive_rating,
             "defensive_rating": self.defensive_rating,
             "net_rating": self.net_rating,
             "pace": self.pace,
             "strength_of_schedule": self.strength_of_schedule,
             "remaining_strength_of_schedule": self.remaining_strength_of_schedule,
             "injury_count": self.injury_count,
             "roster_stability_score": self.roster_stability_score,
             "avg_margin_of_victory": self.avg_margin_of_victory,
             "avg_margin_of_defeat": self.avg_margin_of_defeat,
             "power_ranking": self.power_ranking
         })
     elif user_access_level == "premium":
         # Premium users get advanced analytics
         base_data.update({
             "offensive_rating": self.offensive_rating,
             "defensive_rating": self.defensive_rating,
             "net_rating": self.net_rating,
             "pace": self.pace,
             "home_record": self.home_record,
             "away_record": self.away_record,
             "last_10_record": self.last_10_record,
             "current_streak": self.current_streak
         })
     elif user_access_level == "basic":
         # Basic users get essential team stats
         base_data.update({
             "home_record": self.home_record,
             "away_record": self.away_record,
             "current_streak": self.current_streak
         })
     
     return base_data

 def requires_expert_access(self) -> bool:
     """Check if team requires expert-level access for full data"""
     return bool(
         self.strength_of_schedule or 
         self.roster_stability_score or 
         self.power_ranking
     )


class PlayerModel(Base):
 """Expert-level NBA player information with comprehensive analytics and tracking"""
 __tablename__ = "players"
 
 id = Column(String, primary_key=True) # NBA player ID
 name = Column(String, nullable=False, index=True)
 mythic_roster_id = Column(String, ForeignKey("teams.id"), index=True)
 jersey_number = Column(Integer)
 
 # Physical attributes
 position = Column(String, index=True)
 primary_position = Column(String)
 secondary_position = Column(String)
 height_inches = Column(Integer)
 height_display = Column(String) # "6'8\""
 weight_lbs = Column(Integer)
 wingspan_inches = Column(Integer)
 
 # Personal information
 age = Column(Integer, index=True)
 birthdate = Column(DateTime)
 country = Column(String)
 college = Column(String)
 draft_year = Column(Integer)
 draft_round = Column(Integer)
 draft_pick = Column(Integer)
 experience_years = Column(Integer, index=True)
 
 # Contract and status
 is_active = Column(Boolean, default=True, index=True)
 is_starter = Column(Boolean, default=False, index=True)
 injury_status = Column(String) # healthy, day_to_day, out, doubtful, questionable
 contract_years_remaining = Column(Integer)
 salary_current = Column(Float)
 
 # Current season averages
 season = Column(String, nullable=False, index=True)
 games_played = Column(Integer, default=0)
 games_started = Column(Integer, default=0)
 minutes_per_game = Column(Float, index=True)
 
 # Scoring stats
 points_per_game = Column(Float, index=True)
 field_goals_made_per_game = Column(Float)
 field_goals_attempted_per_game = Column(Float)
 field_goal_percentage = Column(Float, index=True)
 three_pointers_made_per_game = Column(Float)
 three_pointers_attempted_per_game = Column(Float)
 three_point_percentage = Column(Float, index=True)
 free_throws_made_per_game = Column(Float)
 free_throws_attempted_per_game = Column(Float)
 free_throw_percentage = Column(Float)
 
 # Other stats
 rebounds_per_game = Column(Float, index=True)
 offensive_rebounds_per_game = Column(Float)
 defensive_rebounds_per_game = Column(Float)
 assists_per_game = Column(Float, index=True)
 steals_per_game = Column(Float)
 blocks_per_game = Column(Float)
 turnovers_per_game = Column(Float)
 fouls_per_game = Column(Float)
 
 # Advanced analytics
 player_efficiency_rating = Column(Float, index=True)
 true_shooting_percentage = Column(Float)
 effective_field_goal_percentage = Column(Float)
 usage_rate = Column(Float)
 assist_percentage = Column(Float)
 rebound_percentage = Column(Float)
 steal_percentage = Column(Float)
 block_percentage = Column(Float)
 turnover_percentage = Column(Float)
 offensive_rating = Column(Float)
 defensive_rating = Column(Float)
 net_rating = Column(Float)
 plus_minus_per_game = Column(Float)
 
 # Shooting analytics
 shots_at_rim_percentage = Column(Float)
 shots_at_rim_fg_percentage = Column(Float)
 mid_range_percentage = Column(Float)
 mid_range_fg_percentage = Column(Float)
 corner_three_percentage = Column(Float)
 corner_three_fg_percentage = Column(Float)
 
 # Performance trends
 last_5_games_avg_points = Column(Float)
 last_10_games_avg_points = Column(Float)
 home_vs_away_differential = Column(Float)
 vs_winning_teams_performance = Column(Float)
 back_to_back_performance = Column(Float)
 
 # Props and betting relevance
 prop_hit_rate_points = Column(Float) # Historical over/under hit rate
 prop_hit_rate_rebounds = Column(Float)
 prop_hit_rate_assists = Column(Float)
 betting_value_score = Column(Float) # Overall betting value indicator
 
 # Data quality and freshness
 last_game_date = Column(DateTime, index=True)
 stats_last_updated = Column(DateTime, index=True)
 data_quality_score = Column(Float)
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc))
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 
 # Relationships
 team = relationship("TeamModel", back_populates="players")
 game_stats = relationship("PlayerGameStatModel", back_populates="player", cascade="all, delete-orphan")
 props = relationship("PlayerPropModel", back_populates="player", cascade="all, delete-orphan")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_players_team_position', 'mythic_roster_id', 'position'),
 Index('idx_players_performance', 'points_per_game', 'player_efficiency_rating'),
 Index('idx_players_status', 'is_active', 'injury_status'),
 Index('idx_players_season_team', 'season', 'mythic_roster_id'),
 Index('idx_players_usage', 'minutes_per_game', 'usage_rate'),
 UniqueConstraint('mythic_roster_id', 'jersey_number', 'season', name='uq_player_team_jersey'),
 CheckConstraint('games_played >= 0', name='check_games_played_positive'),
 CheckConstraint('minutes_per_game >= 0', name='check_minutes_positive'),
 CheckConstraint('field_goal_percentage >= 0 AND field_goal_percentage <= 1', name='check_fg_pct_valid'), CheckConstraint('age > 0 AND age < 60', name='check_age_realistic'),
 )
 
 @validates('injury_status')
 def validate_injury_status(self, key, status):
     valid_statuses = ['healthy', 'day_to_day', 'out', 'doubtful', 'questionable']
     if status and status not in valid_statuses:
         raise ValueError(f"Invalid injury status: {status}")
     return status

 # ===================================================================
 # AUTHENTICATION AND ACCESS CONTROL METHODS
 # ===================================================================

 def is_accessible_to_user(self, user_access_level: str) -> bool:
     """Check if player data is accessible based on user access level"""
     if user_access_level in ["admin", "expert", "vault"]:
         return True
     elif user_access_level == "premium":
         # Premium users can access all active players
         return self.is_active
     elif user_access_level == "basic":
         # Basic users have limited access to main roster players
         return self.is_active and self.is_starter
     else:
         return False

 def get_filtered_data(self, user_access_level: str) -> Dict[str, Any]:
     """Get filtered player data based on user access level"""
     base_data = {
         "id": self.id,
         "name": self.name,
         "position": self.position,
         "mythic_roster_id": self.mythic_roster_id,
         "jersey_number": self.jersey_number,
         "is_active": self.is_active
     }
     
     if user_access_level in ["admin", "expert", "vault"]:
         # Full data access for expert users
         base_data.update({
             "injury_status": self.injury_status,
             "contract_years_remaining": self.contract_years_remaining,
             "salary_current": self.salary_current,
             "player_efficiency_rating": self.player_efficiency_rating,
             "usage_rate": self.usage_rate,
             "prop_hit_rate_points": self.prop_hit_rate_points,
             "prop_hit_rate_rebounds": self.prop_hit_rate_rebounds,
             "prop_hit_rate_assists": self.prop_hit_rate_assists,
             "betting_value_score": self.betting_value_score,
             "last_5_games_avg_points": self.last_5_games_avg_points,
             "vs_winning_teams_performance": self.vs_winning_teams_performance
         })
     elif user_access_level == "premium":
         # Premium users get advanced stats and some betting insights
         base_data.update({
             "injury_status": self.injury_status,
             "player_efficiency_rating": self.player_efficiency_rating,
             "usage_rate": self.usage_rate,
             "points_per_game": self.points_per_game,
             "rebounds_per_game": self.rebounds_per_game,
             "assists_per_game": self.assists_per_game,
             "last_5_games_avg_points": self.last_5_games_avg_points
         })
     elif user_access_level == "basic":
         # Basic users get essential stats only
         base_data.update({
             "points_per_game": self.points_per_game,
             "rebounds_per_game": self.rebounds_per_game,
             "assists_per_game": self.assists_per_game
         })
     
     return base_data

 def requires_expert_access(self) -> bool:
     """Check if player requires expert-level access for full data"""
     return bool(
         self.prop_hit_rate_points or 
         self.betting_value_score or 
         self.salary_current or
         self.contract_years_remaining
     )



class PlayerGameStatModel(Base):
 """Expert-level player statistics for individual games with comprehensive tracking"""
 __tablename__ = "player_game_stats"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 hero_id = Column(String, ForeignKey("players.id", ondelete="CASCADE"), nullable=False, index=True)
 titan_clash_id = Column(String, ForeignKey("games.id", ondelete="CASCADE"), nullable=False, index=True)
 
 # Basic stats
 minutes_played = Column(Float)
 seconds_played = Column(Integer) # For precise tracking
 points = Column(Integer, index=True)
 field_goals_made = Column(Integer)
 field_goals_attempted = Column(Integer)
 three_pointers_made = Column(Integer)
 three_pointers_attempted = Column(Integer)
 free_throws_made = Column(Integer)
 free_throws_attempted = Column(Integer)
 
 # Rebounds and assists
 offensive_rebounds = Column(Integer)
 defensive_rebounds = Column(Integer)
 total_rebounds = Column(Integer, index=True)
 assists = Column(Integer, index=True)
 
 # Defensive stats
 steals = Column(Integer)
 blocks = Column(Integer)
 
 # Other stats
 turnovers = Column(Integer)
 personal_fouls = Column(Integer)
 technical_fouls = Column(Integer)
 flagrant_fouls = Column(Integer)
 plus_minus = Column(Integer, index=True)
 
 # Advanced tracking
 starter = Column(Boolean, default=False)
 bench_points = Column(Integer) # Points scored while coming off bench
 lead_changes_responsible = Column(Integer)
 
 # Shot tracking
 shots_blocked = Column(Integer)
 shots_contested = Column(Integer)
 shots_created_for_others = Column(Integer)
 
 # Game context
 home_game = Column(Boolean, index=True)
 back_to_back = Column(Boolean)
 rest_days = Column(Integer)
 
 # Performance indicators
 game_score = Column(Float) # John Hollinger's Game Score
 usage_rate_game = Column(Float)
 efficiency_rating = Column(Float)
 
 # Data quality
 stat_completeness = Column(Float) # Percentage of stats available
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 
 # Relationships
 player = relationship("PlayerModel", back_populates="game_stats")
 game = relationship("GameModel", back_populates="player_stats")
 
 __table_args__ = (
 Index('idx_player_stats_player_date', 'hero_id', 'titan_clash_id'),
 Index('idx_player_stats_performance', 'points', 'total_rebounds', 'assists'),
 Index('idx_player_stats_game_context', 'home_game', 'back_to_back'),
 UniqueConstraint('hero_id', 'titan_clash_id', name='uq_player_game_stat'),
 CheckConstraint('minutes_played >= 0', name='check_minutes_positive'),
 CheckConstraint('points >= 0', name='check_points_positive'),
 CheckConstraint('total_rebounds >= 0', name='check_rebounds_positive'),
 )


class PlayerPropModel(Base):
 """Expert-level player prop betting lines with comprehensive tracking"""
 __tablename__ = "player_props"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 hero_id = Column(String, ForeignKey("players.id", ondelete="CASCADE"), nullable=False, index=True)
 titan_clash_id = Column(String, ForeignKey("games.id", ondelete="CASCADE"), nullable=False, index=True)
 
 # Prop details
 prop_type = Column(String, nullable=False, index=True) # points, rebounds, assists, threes, etc.
 line = Column(Float, nullable=False, index=True)
 over_odds = Column(Integer, index=True)
 under_odds = Column(Integer, index=True)
 sportsbook = Column(String, nullable=False, index=True)
 
 # Line movement tracking
 opening_line = Column(Float)
 closing_line = Column(Float)
 line_movement = Column(Float) # Current line - opening line
 sharp_money_indicator = Column(Boolean, default=False)
 public_money_percentage = Column(Float)
 
 # Market context
 juice_over = Column(Float) # Implied probability over
 juice_under = Column(Float) # Implied probability under
 fair_value = Column(Float) # Calculated fair line
 edge_over = Column(Float) # Expected value betting over
 edge_under = Column(Float) # Expected value betting under
 
 # Historical performance
 player_hit_rate_l10 = Column(Float) # Last 10 games hit rate
 player_hit_rate_season = Column(Float) # Season hit rate
 vs_opponent_performance = Column(Float) # Performance vs this opponent
 home_away_split = Column(Float) # Home vs away differential
 
 # Situational factors
 player_rest_days = Column(Integer)
 back_to_back = Column(Boolean, default=False)
 minutes_projection = Column(Float)
 usage_projection = Column(Float)
 
 # Data quality and audit
 confidence_score = Column(Float)
 data_freshness_score = Column(Float)
 last_updated = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 
 # Relationships
 player = relationship("PlayerModel", back_populates="props")
 game = relationship("GameModel")
 predictions = relationship("PropPredictionModel", back_populates="prop", cascade="all, delete-orphan")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_props_player_game', 'hero_id', 'titan_clash_id'),
 Index('idx_props_type_line', 'prop_type', 'line'),
 Index('idx_props_book_odds', 'sportsbook', 'over_odds', 'under_odds'),
 Index('idx_props_value', 'edge_over', 'edge_under'),
 UniqueConstraint('hero_id', 'titan_clash_id', 'prop_type', 'sportsbook', name='uq_player_prop_book'),
 CheckConstraint('line > 0', name='check_line_positive'),
 CheckConstraint('over_odds != 0 AND under_odds != 0', name='check_odds_nonzero'),
 )


class GamePredictionModel(Base):
 """Expert-level game outcome predictions with comprehensive analysis"""
 __tablename__ = "game_predictions"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 titan_clash_id = Column(String, ForeignKey("games.id", ondelete="CASCADE"), nullable=False, index=True)
 model_name = Column(String, nullable=False, index=True)
 model_version = Column(String, nullable=False, index=True)
 
 # Score predictions
 predicted_home_score = Column(Float, index=True)
 predicted_away_score = Column(Float, index=True)
 predicted_total = Column(Float, index=True)
 predicted_spread = Column(Float, index=True) # Home team perspective
 predicted_margin = Column(Float)
 
 # Win probabilities
 win_probability_home = Column(Float, nullable=False, index=True)
 win_probability_away = Column(Float, nullable=False, index=True)
 
 # Betting market predictions
 predicted_moneyline_home = Column(Integer)
 predicted_moneyline_away = Column(Integer)
 over_probability = Column(Float)
 under_probability = Column(Float)
 spread_cover_probability = Column(Float)
 
 # Confidence and quality metrics
 confidence_score = Column(Float, nullable=False, index=True)
 model_agreement = Column(Float) # Consensus with other models
 data_quality_score = Column(Float)
 prediction_variance = Column(Float)
 # Expected value calculations
 ev_home_ml = Column(Float) # Expected value betting home moneyline
 ev_away_ml = Column(Float)
 ev_over = Column(Float)
 ev_under = Column(Float)
 ev_spread_home = Column(Float)
 ev_spread_away = Column(Float)
 expected_value_over = Column(Float, index=True) # For compatibility with test
 
 # Feature importance and analysis
 key_factors = Column(JSONType) # Most important features for this prediction
 situational_factors = Column(JSONType) # Game context factors
 injury_impact = Column(Float) # Impact of injuries on prediction
 rest_impact = Column(Float) # Impact of rest days
 travel_impact = Column(Float) # Travel fatigue impact
 matchup_rating = Column(Float) # Overall matchup favorability
 
 # Historical context
 head_to_head_factor = Column(Float)
 recent_form_factor = Column(Float)
 home_court_advantage = Column(Float)
 
 # Model performance tracking
 prediction_type = Column(String, default="pregame", index=True) # pregame, live, halftime
 processing_time_ms = Column(Integer)
 feature_count = Column(Integer)
 
 # Results tracking (filled after game completion)
 actual_home_score = Column(Integer)
 actual_away_score = Column(Integer)
 actual_total = Column(Integer)
 actual_margin = Column(Integer)
 
 # Accuracy metrics (calculated post-game)
 score_accuracy = Column(Float)
 spread_accuracy = Column(Float)
 total_accuracy = Column(Float)
 win_prediction_correct = Column(Boolean)
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 prediction_locked_at = Column(DateTime) # When prediction was finalized
 
 # Relationships
 game = relationship("GameModel", back_populates="predictions")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_game_pred_model', 'titan_clash_id', 'model_name', 'model_version'),
 Index('idx_game_pred_confidence', 'confidence_score', 'model_agreement'),
 Index('idx_game_pred_value', 'ev_home_ml', 'ev_over', 'ev_spread_home'),
 Index('idx_game_pred_accuracy', 'win_prediction_correct', 'score_accuracy'),
 UniqueConstraint('titan_clash_id', 'model_name', 'model_version', 'prediction_type', name='uq_game_prediction'),
 CheckConstraint('win_probability_home + win_probability_away = 1.0', name='check_win_prob_sum'),
 CheckConstraint('confidence_score >= 0 AND confidence_score <= 1', name='check_confidence_range'),
 CheckConstraint('over_probability + under_probability = 1.0', name='check_total_prob_sum'),
 )


class PropPredictionModel(Base):
 """Expert-level player prop predictions with comprehensive analysis"""
 __tablename__ = "prop_predictions"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 prop_id = Column(String, ForeignKey("player_props.id", ondelete="CASCADE"), nullable=False, index=True)
 model_name = Column(String, nullable=False, index=True)
 model_version = Column(String, nullable=False, index=True)
 
 # Core predictions
 predicted_value = Column(Float, nullable=False, index=True)
 over_probability = Column(Float, nullable=False, index=True)
 under_probability = Column(Float, nullable=False, index=True)
 confidence_score = Column(Float, nullable=False, index=True)
 
 # Expected value calculations
 expected_value_over = Column(Float, index=True)
 expected_value_under = Column(Float)
 kelly_criterion_over = Column(Float)
 kelly_criterion_under = Column(Float)
 optimal_bet_size_over = Column(Float)
 optimal_bet_size_under = Column(Float)
 
 # Prediction distribution
 prediction_variance = Column(Float)
 prediction_std_dev = Column(Float)
 percentile_5 = Column(Float)
 percentile_25 = Column(Float)
 percentile_75 = Column(Float)
 percentile_95 = Column(Float)
 
 # Feature analysis
 key_factors = Column(JSONType) # Most important features
 matchup_impact = Column(Float, index=True) # Specific matchup impact
 recent_form_impact = Column(Float) # Recent performance weight
 usage_impact = Column(Float) # Usage rate considerations
 pace_impact = Column(Float) # Game pace impact
 rest_impact = Column(Float) # Rest days impact
 
 # Historical context
 player_trend_factor = Column(Float) # Player's recent trend
 vs_opponent_history = Column(Float) # Historical vs this opponent
 situational_performance = Column(Float) # Similar game situations
 regression_factor = Column(Float) # Mean reversion consideration
 
 # Game context factors
 projected_minutes = Column(Float, index=True)
 projected_usage_rate = Column(Float)
 blowout_risk_factor = Column(Float) # Risk of garbage time
 injury_risk_factor = Column(Float) # Player injury concerns
 
 # Model performance indicators
 feature_importance_score = Column(Float)
 data_completeness = Column(Float)
 model_stability = Column(Float)
 backtesting_accuracy = Column(Float)
 
 # Results tracking (filled after game)
 actual_value = Column(Float)
 actual_result = Column(String) # "over", "under", "push"
 prediction_error = Column(Float)
 bet_outcome = Column(String) # "win", "loss", "push"
 
 # Performance metrics (calculated post-game)
 absolute_error = Column(Float)
 squared_error = Column(Float)
 probability_score = Column(Float) # Brier score
 calibration_error = Column(Float)
 
 # Audit and tracking
 processing_time_ms = Column(Integer)
 feature_count = Column(Integer)
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 prediction_locked_at = Column(DateTime)
 
 # Relationships
 prop = relationship("PlayerPropModel", back_populates="predictions")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_prop_pred_model', 'prop_id', 'model_name', 'model_version'),
 Index('idx_prop_pred_value', 'predicted_value', 'confidence_score'),
 Index('idx_prop_pred_ev', 'expected_value_over', 'expected_value_under'),
 Index('idx_prop_pred_accuracy', 'absolute_error', 'probability_score'),
 UniqueConstraint('prop_id', 'model_name', 'model_version', name='uq_prop_prediction'),
 CheckConstraint('over_probability + under_probability = 1.0', name='check_prop_prob_sum'),
 CheckConstraint('confidence_score >= 0 AND confidence_score <= 1', name='check_prop_confidence_range'),
 CheckConstraint('predicted_value >= 0', name='check_predicted_value_positive'),
 )


class AchievementModel(Base):
 """Expert-level achievement definition system"""
 __tablename__ = "achievements"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 name = Column(String, nullable=False, index=True)
 description = Column(Text, nullable=False)
 achievement_type = Column(String, nullable=False, index=True)
 rarity = Column(String, default=AchievementRarity.COMMON.value, index=True)
 
 # Achievement criteria
 criteria_json = Column(JSONType, nullable=False) # Flexible criteria definition
 target_value = Column(Float) # Numeric target if applicable
 category = Column(String, index=True)
 
 # Rewards and display
 points_awarded = Column(Integer, default=0)
 badge_icon = Column(String) # Icon identifier
 badge_color = Column(String, default="#4F46E5")
 tier = Column(String, default="bronze") # bronze, silver, gold, platinum
 
 # Metadata
 is_active = Column(Boolean, default=True, index=True)
 is_repeatable = Column(Boolean, default=False)
 unlock_order = Column(Integer) # For sequential achievements
 prerequisites = Column(ARRAY(String)) # Required achievement IDs
 
 # Statistics
 total_unlocked = Column(Integer, default=0)
 unlock_rate = Column(Float, default=0.0) # Percentage of users who unlocked
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 created_by = Column(String, ForeignKey("users.id"))
 
 # Relationships
 user_achievements = relationship("UserAchievementModel", back_populates="achievement", cascade="all, delete-orphan")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_achievements_type_rarity', 'achievement_type', 'rarity'),
 Index('idx_achievements_active_category', 'is_active', 'category'),
 Index('idx_achievements_unlock_stats', 'total_unlocked', 'unlock_rate'),
 CheckConstraint('points_awarded >= 0', name='check_points_positive'),
 CheckConstraint('target_value >= 0', name='check_target_positive'),
 CheckConstraint('total_unlocked >= 0', name='check_unlocked_positive'), CheckConstraint('unlock_rate >= 0 AND unlock_rate <= 1', name='check_unlock_rate_valid'),
 )
 
 @validates('achievement_type')
 def validate_achievement_type(self, key, achievement_type):
     if achievement_type not in [t.value for t in AchievementType]:
         raise ValueError(f"Invalid achievement type: {achievement_type}")
     return achievement_type
 
 @validates('rarity')
 def validate_rarity(self, key, rarity):
     if rarity not in [r.value for r in AchievementRarity]:
         raise ValueError(f"Invalid rarity: {rarity}")
     return rarity


class UserAchievementModel(Base):
 """Expert-level user achievement tracking with progress and metrics"""
 __tablename__ = "user_achievements"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 vault_user_id = Column(String, ForeignKey("users.id"), nullable=False, index=True)
 achievement_id = Column(String, ForeignKey("achievements.id"), nullable=False, index=True)
 
 # Progress tracking
 current_progress = Column(Float, default=0.0)
 target_progress = Column(Float, nullable=False)
 progress_percentage = Column(Float, default=0.0)
 is_completed = Column(Boolean, default=False, index=True)
 
 # Achievement unlock details
 unlocked_at = Column(DateTime, index=True)
 unlock_method = Column(String) # How achievement was unlocked
 unlock_context = Column(JSONType) # Additional context data
 
 # Progression metadata
 first_progress_at = Column(DateTime, index=True)
 last_progress_at = Column(DateTime, index=True)
 completion_time_seconds = Column(Integer) # Time to complete from first progress
 
 # Rewards claimed
 points_claimed = Column(Integer, default=0)
 rewards_claimed_at = Column(DateTime)
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 
 # Relationships
 user = relationship("UserModel", back_populates="achievements")
 achievement = relationship("AchievementModel", back_populates="user_achievements")
 
 # Constraints and indexes
 __table_args__ = (
 UniqueConstraint('vault_user_id', 'achievement_id', name='uq_user_achievement'),
 Index('idx_user_achievements_progress', 'vault_user_id', 'is_completed', 'progress_percentage'),
 Index('idx_user_achievements_unlocked', 'vault_user_id', 'unlocked_at'),
 Index('idx_user_achievements_recent', 'achievement_id', 'unlocked_at'),
 CheckConstraint('current_progress >= 0', name='check_current_progress_positive'),
 CheckConstraint('target_progress > 0', name='check_target_progress_positive'),
 CheckConstraint('progress_percentage >= 0 AND progress_percentage <= 100', name='check_progress_percentage_valid'),
 CheckConstraint('completion_time_seconds >= 0', name='check_completion_time_positive'),
 )


class UserStatsModel(Base):
 """Expert-level user statistics and KPI tracking for achievements"""
 __tablename__ = "user_stats"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 vault_user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, unique=True, index=True)
 
 # Achievement statistics
 total_achievements = Column(Integer, default=0)
 achievements_unlocked = Column(Integer, default=0)
 achievement_points = Column(Integer, default=0)
 achievement_completion_rate = Column(Float, default=0.0)
 
 # Betting statistics
 total_bets_placed = Column(Integer, default=0)
 total_bet_amount = Column(Float, default=0.0)
 total_winnings = Column(Float, default=0.0)
 win_rate = Column(Float, default=0.0)
 current_streak = Column(Integer, default=0)
 longest_streak = Column(Integer, default=0)
 
 # Accuracy and prediction statistics
 prediction_accuracy = Column(Float, default=0.0)
 total_predictions = Column(Integer, default=0)
 correct_predictions = Column(Integer, default=0)
 
 # Engagement statistics
 days_active = Column(Integer, default=0)
 last_activity_date = Column(DateTime, index=True)
 total_sessions = Column(Integer, default=0)
 average_session_duration = Column(Float, default=0.0)
 
 # Ranking and tier information
 user_tier = Column(String, default="bronze")
 tier_progress = Column(Float, default=0.0)
 next_tier = Column(String)
 ranking_position = Column(Integer)
 percentile_rank = Column(Float)
 
 # Monthly and weekly statistics
 monthly_bets = Column(Integer, default=0)
 monthly_winnings = Column(Float, default=0.0)
 weekly_accuracy = Column(Float, default=0.0)
 
 # Milestone tracking
 milestones_completed = Column(ARRAY(String))
 next_milestone = Column(String)
 milestone_progress = Column(Float, default=0.0)
 
 # Social and community stats
 referrals_made = Column(Integer, default=0)
 community_contributions = Column(Integer, default=0)
 forum_posts = Column(Integer, default=0)
 
 # Audit fields
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 stats_last_calculated = Column(DateTime, index=True)
 
 # Relationships
 user = relationship("UserModel", back_populates="stats", uselist=False)
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_user_stats_tier_rank', 'user_tier', 'ranking_position'),
 Index('idx_user_stats_activity', 'last_activity_date', 'days_active'),
 Index('idx_user_stats_performance', 'win_rate', 'prediction_accuracy'),
 Index('idx_user_stats_achievements', 'achievement_points', 'achievements_unlocked'),
 CheckConstraint('total_achievements >= 0', name='check_total_achievements_positive'),
 CheckConstraint('achievements_unlocked >= 0', name='check_unlocked_positive'),
 CheckConstraint('achievement_points >= 0', name='check_points_positive'),
 CheckConstraint('achievement_completion_rate >= 0 AND achievement_completion_rate <= 1', name='check_completion_rate_valid'),
 CheckConstraint('win_rate >= 0 AND win_rate <= 1', name='check_win_rate_valid'),
 CheckConstraint('prediction_accuracy >= 0 AND prediction_accuracy <= 1', name='check_accuracy_valid'),
 CheckConstraint('tier_progress >= 0 AND tier_progress <= 1', name='check_tier_progress_valid'),
 )


class VaultOperationModel(Base):
 """MEDUSA VAULT operations tracking for expert users"""
 __tablename__ = "vault_operations"
 
 id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
 vault_user_id = Column(String, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
 
 # Operation details
 operation_type = Column(String, nullable=False, index=True)  # prediction, analysis, neural_sync, quantum_query
 operation_name = Column(String, nullable=False)
 operation_data = Column(JSONType)  # Operation parameters and context
 
 # Vault context
 vault_session_id = Column(String, nullable=False, index=True)
 quantum_signature = Column(String, unique=True, index=True)
 neural_pattern_id = Column(String, index=True)
 
 # Execution details
 status = Column(String, default="initiated", index=True)  # initiated, processing, completed, failed, cancelled
 processing_time_ms = Column(Integer)
 complexity_score = Column(Float)  # Operation complexity rating
 
 # Results and outcomes
 result_data = Column(JSONType)
 accuracy_score = Column(Float)  # If applicable
 confidence_level = Column(Float)
 vault_energy_consumed = Column(Float)  # Resource usage metric
 
 # Integration with external systems
 prediction_ids = Column(ARRAY(String))  # Related predictions
 game_ids = Column(ARRAY(String))  # Related games
 player_ids = Column(ARRAY(String))  # Related players
 
 # Error handling and diagnostics
 error_details = Column(JSONType)
 retry_count = Column(Integer, default=0)
 debug_information = Column(JSONType)
 
 # Performance metrics
 neural_sync_quality = Column(Float)
 quantum_coherence = Column(Float)
 vault_resonance = Column(Float)
 
 # Audit and compliance
 chronicle_timestamp = Column(DateTime, default=lambda: datetime.now(timezone.utc), index=True)
 last_prophecy_update = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))
 completed_at = Column(DateTime, index=True)
 expires_at = Column(DateTime)
 
 # Relationships
 user = relationship("UserModel", back_populates="vault_operations_history")
 
 # Constraints and indexes
 __table_args__ = (
 Index('idx_vault_ops_user_type', 'vault_user_id', 'operation_type'),
 Index('idx_vault_ops_session', 'vault_session_id', 'status'),
 Index('idx_vault_ops_performance', 'accuracy_score', 'confidence_level'),
 Index('idx_vault_ops_timeline', 'chronicle_timestamp', 'completed_at'),
 CheckConstraint('processing_time_ms >= 0', name='check_processing_time_positive'),
 CheckConstraint('complexity_score >= 0', name='check_complexity_positive'),
 CheckConstraint('retry_count >= 0', name='check_retry_count_positive'),
 CheckConstraint('accuracy_score >= 0 AND accuracy_score <= 1', name='check_accuracy_valid'),
 CheckConstraint('confidence_level >= 0 AND confidence_level <= 1', name='check_confidence_valid'),
 )
 
 def to_vault_context_dict(self) -> Dict[str, Any]:
     """Convert operation to vault context dictionary"""
     return {
         "operation_id": self.id,         "operation_type": self.operation_type,
         "vault_session_id": self.vault_session_id,
         "quantum_signature": self.quantum_signature,
         "status": self.status,
         "complexity_score": self.complexity_score,
         "neural_sync_quality": self.neural_sync_quality,
         "timestamp": self.chronicle_timestamp.isoformat() if self.chronicle_timestamp else None
     }


# ===================================================================
# UTILITY FUNCTIONS
# ===================================================================

def get_model_schema_info():
    """Get schema information for all models"""
    return {
        'models': {
            'UserModel': {
                'table_name': 'users',
                'description': 'Expert-level user account management with enhanced security and tracking'
            },
            'GameModel': {
                'table_name': 'games',
                'description': 'Expert-level NBA game records with comprehensive tracking and analytics'
            },
            'TeamModel': {
                'table_name': 'teams',
                'description': 'Expert-level NBA team information with comprehensive analytics'
            },
            'PlayerModel': {
                'table_name': 'players',
                'description': 'Expert-level NBA player information with comprehensive analytics and tracking'
            },
            'AchievementModel': {
                'table_name': 'achievements',
                'description': 'Expert-level achievement definition system'
            },
            'VaultOperationModel': {
                'table_name': 'vault_operations',
                'description': 'MEDUSA VAULT operations tracking for expert users'
            }
        },
        'total_models': 13,
        'base_class': 'declarative_base',
        'auth_integration': AUTH_INTEGRATION_AVAILABLE,
        'config_integration': CONFIG_INTEGRATION_AVAILABLE
    }

# ===================================================================
# MODEL ALIASES AND EXPORTS FOR BACKWARD COMPATIBILITY
# ===================================================================

# Create aliases for common import patterns used by routers
User = UserModel
Achievement = AchievementModel 
UserAchievement = UserAchievementModel
UserStats = UserStatsModel
UserSession = UserSessionModel
Game = GameModel
Team = TeamModel
Player = PlayerModel
GamePrediction = GamePredictionModel
PropPrediction = PropPredictionModel
PlayerGameStat = PlayerGameStatModel
PlayerProp = PlayerPropModel
LineMovement = LineMovementModel
VaultOperation = VaultOperationModel

# Export list for explicit imports
__all__ = [
 # Base and enums
 'Base',
 'UserRole', 'SubscriptionTier', 'GameStatus', 'BetResult',
 'AchievementType', 'AchievementRarity',
  # Main models (with Model suffix)
 'UserModel', 'UserSessionModel', 'GameModel', 'TeamModel', 'PlayerModel',
 'AchievementModel', 'UserAchievementModel', 'UserStatsModel',
 'GamePredictionModel', 'PropPredictionModel', 'PlayerGameStatModel',
 'PlayerPropModel', 'LineMovementModel', 'VaultOperationModel',
 
 # Aliases for convenience (without Model suffix)
 'User', 'Achievement', 'UserAchievement', 'UserStats',
 'UserSession', 'Game', 'Team', 'Player', 'GamePrediction',
 'PropPrediction', 'PlayerGameStat', 'PlayerProp', 'LineMovement',
 'VaultOperation',
  # Pydantic models for API responses
 'BaseStatsModel', 'PlayerStats', 'TeamStats', 'TeamRankings',
 'GameData', 'PredictionResult',
 
 # Integration status and utilities
 'AUTH_INTEGRATION_AVAILABLE', 'CONFIG_INTEGRATION_AVAILABLE', 'INTEGRATION_STATUS', 'get_model_schema_info',
 'init_db'
]

def init_db():
    """Initialize the database with proper production setup."""

    logger = logging.getLogger(__name__)

    try:
        # Get database URL from environment
        database_url = os.getenv('DATABASE_URL', 'sqlite:///./hyper_medusa_vault.db')

        # Create engine with proper configuration
        engine = create_engine(
            database_url,
            echo=False,  # Set to True for SQL debugging
            pool_pre_ping=True,  # Verify connections before use
            pool_recycle=3600,   # Recycle connections every hour
        )

        # Create all tables
        Base.metadata.create_all(bind=engine)

        # Create session factory
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        # Test database connection
        with engine.connect() as connection:
            result = connection.execute(text("SELECT 1"))
            logger.info("✅ Database connection successful")

        logger.info("✅ Database initialization complete")
        return engine, SessionLocal

    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        raise

__all__.append('init_db')
