#!/usr/bin/env python3
"""
🎯 REAL-TIME ACCURACY TRACKING SYSTEM
====================================

Monitors prediction accuracy in real-time and triggers dynamic model adjustments.
Feeds performance metrics into ensemble weight optimization and automated fallback strategies.

Key Features:
- Real-time accuracy monitoring for all prediction models
- Automated alerts for accuracy drops below thresholds
- Performance-based model weight adjustment
- Integration with props-to-game dynamic weighting system
- Automated fallback strategies for poor-performing models
"""

import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
import json
from collections import deque
import threading
import time

logger = logging.getLogger(__name__)

@dataclass
class PredictionRecord:
    """Individual prediction record for tracking"""
    prediction_id: str
    model_name: str
    prediction: Dict[str, Any]
    actual_outcome: Optional[Dict[str, Any]] = None
    accuracy: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.now)
    game_id: str = ""
    league: str = "NBA"

@dataclass
class ModelPerformanceMetrics:
    """Performance metrics for individual models"""
    model_name: str
    recent_accuracy: float = 0.0
    total_predictions: int = 0
    correct_predictions: int = 0
    accuracy_trend: List[float] = field(default_factory=list)
    last_update: datetime = field(default_factory=datetime.now)
    performance_alerts: List[str] = field(default_factory=list)

class RealTimeAccuracyTracker:
    """Real-time accuracy tracking and performance monitoring system"""
    
    def __init__(self, accuracy_window: int = 50, alert_threshold: float = 0.60):
        self.accuracy_window = accuracy_window
        self.alert_threshold = alert_threshold
        
        # Prediction storage
        self.prediction_records = deque(maxlen=1000)  # Keep last 1000 predictions
        self.model_metrics = {}  # Model name -> ModelPerformanceMetrics
        
        # Real-time tracking
        self.recent_predictions = deque(maxlen=accuracy_window)
        self.ensemble_accuracy_history = deque(maxlen=100)
        
        # Performance thresholds
        self.performance_config = {
            'excellent_threshold': 0.75,
            'good_threshold': 0.65,
            'warning_threshold': 0.60,
            'critical_threshold': 0.55,
            'min_predictions_for_adjustment': 10
        }
        
        # Alert system
        self.active_alerts = []
        self.alert_callbacks = []  # Functions to call when alerts are triggered
        
        # Integration with props-to-game system
        self.props_integration = None
        
        logger.info("🎯 Real-Time Accuracy Tracker initialized")
    
    def register_prediction(self, model_name: str, prediction: Dict[str, Any], 
                          game_id: str = "", league: str = "NBA") -> str:
        """Register a new prediction for tracking"""
        prediction_id = f"{model_name}_{game_id}_{datetime.now().timestamp()}"
        
        record = PredictionRecord(
            prediction_id=prediction_id,
            model_name=model_name,
            prediction=prediction,
            game_id=game_id,
            league=league
        )
        
        self.prediction_records.append(record)
        self.recent_predictions.append(record)
        
        # Initialize model metrics if not exists
        if model_name not in self.model_metrics:
            self.model_metrics[model_name] = ModelPerformanceMetrics(model_name=model_name)
        
        logger.info(f"📝 Registered prediction {prediction_id} for {model_name}")
        return prediction_id
    
    def update_prediction_outcome(self, prediction_id: str, actual_outcome: Dict[str, Any]):
        """Update prediction with actual outcome and calculate accuracy"""
        # Find the prediction record
        record = None
        for pred_record in self.prediction_records:
            if pred_record.prediction_id == prediction_id:
                record = pred_record
                break
        
        if not record:
            logger.warning(f"⚠️ Prediction record {prediction_id} not found")
            return
        
        # Update with actual outcome
        record.actual_outcome = actual_outcome
        record.accuracy = self._calculate_accuracy(record.prediction, actual_outcome)
        
        # Update model metrics
        self._update_model_metrics(record)
        
        # Check for performance alerts
        self._check_performance_alerts(record.model_name)
        
        # Trigger dynamic weight adjustment if needed
        self._trigger_weight_adjustment()
        
        logger.info(f"✅ Updated prediction {prediction_id} with accuracy: {record.accuracy:.3f}")
    
    def _calculate_accuracy(self, prediction: Dict[str, Any], actual: Dict[str, Any]) -> float:
        """Calculate accuracy based on prediction type"""
        try:
            # Game outcome accuracy
            if 'home_win_probability' in prediction and 'home_team_won' in actual:
                predicted_winner = prediction['home_win_probability'] > 0.5
                actual_winner = actual['home_team_won']
                return 1.0 if predicted_winner == actual_winner else 0.0
            
            # Point spread accuracy (within 3 points)
            if 'point_spread' in prediction and 'actual_margin' in actual:
                predicted_margin = prediction['point_spread']
                actual_margin = actual['actual_margin']
                return 1.0 if abs(predicted_margin - actual_margin) <= 3.0 else 0.0
            
            # Total points accuracy (within 5 points)
            if 'total_points' in prediction and 'actual_total' in actual:
                predicted_total = prediction['total_points']
                actual_total = actual['actual_total']
                return 1.0 if abs(predicted_total - actual_total) <= 5.0 else 0.0
            
            # Default binary accuracy
            return 1.0 if prediction.get('prediction') == actual.get('outcome') else 0.0
            
        except Exception as e:
            logger.error(f"❌ Error calculating accuracy: {e}")
            return 0.0
    
    def _update_model_metrics(self, record: PredictionRecord):
        """Update performance metrics for a model"""
        metrics = self.model_metrics[record.model_name]
        
        # Update counters
        metrics.total_predictions += 1
        if record.accuracy and record.accuracy > 0.5:
            metrics.correct_predictions += 1
        
        # Calculate recent accuracy
        recent_records = [r for r in self.recent_predictions 
                         if r.model_name == record.model_name and r.accuracy is not None]
        
        if recent_records:
            metrics.recent_accuracy = np.mean([r.accuracy for r in recent_records])
            metrics.accuracy_trend.append(metrics.recent_accuracy)
            
            # Keep only recent trend data
            if len(metrics.accuracy_trend) > 20:
                metrics.accuracy_trend = metrics.accuracy_trend[-20:]
        
        metrics.last_update = datetime.now()
    
    def _check_performance_alerts(self, model_name: str):
        """Check if model performance requires alerts"""
        metrics = self.model_metrics[model_name]
        
        if metrics.total_predictions < self.performance_config['min_predictions_for_adjustment']:
            return  # Not enough data yet
        
        accuracy = metrics.recent_accuracy
        
        # Generate alerts based on performance thresholds
        if accuracy < self.performance_config['critical_threshold']:
            alert = f"🚨 CRITICAL: {model_name} accuracy dropped to {accuracy:.3f}"
            self._trigger_alert(alert, 'critical', model_name)
        elif accuracy < self.performance_config['warning_threshold']:
            alert = f"⚠️ WARNING: {model_name} accuracy at {accuracy:.3f}"
            self._trigger_alert(alert, 'warning', model_name)
        elif accuracy > self.performance_config['excellent_threshold']:
            alert = f"🌟 EXCELLENT: {model_name} performing at {accuracy:.3f}"
            self._trigger_alert(alert, 'positive', model_name)
    
    def _trigger_alert(self, message: str, severity: str, model_name: str):
        """Trigger performance alert"""
        alert = {
            'message': message,
            'severity': severity,
            'model_name': model_name,
            'timestamp': datetime.now(),
            'accuracy': self.model_metrics[model_name].recent_accuracy
        }
        
        self.active_alerts.append(alert)
        logger.warning(message)
        
        # Call registered alert callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                logger.error(f"❌ Alert callback failed: {e}")
    
    def _trigger_weight_adjustment(self):
        """Trigger dynamic weight adjustment in props-to-game integration"""
        if not self.props_integration:
            return
        
        try:
            # Get current model accuracies
            props_accuracy = self._get_model_accuracy('props_system')
            base_accuracy = self._get_model_accuracy('base_models')
            ensemble_accuracy = self._get_ensemble_accuracy()
            
            # Update props-to-game integration with performance metrics
            self.props_integration.update_performance_metrics(
                props_accuracy, base_accuracy, ensemble_accuracy
            )
            
            logger.info("🔄 Triggered dynamic weight adjustment")
            
        except Exception as e:
            logger.error(f"❌ Weight adjustment failed: {e}")
    
    def _get_model_accuracy(self, model_pattern: str) -> float:
        """Get accuracy for models matching pattern"""
        matching_models = [name for name in self.model_metrics.keys() 
                          if model_pattern.lower() in name.lower()]
        
        if not matching_models:
            return 0.65  # Default fallback
        
        accuracies = [self.model_metrics[name].recent_accuracy 
                     for name in matching_models]
        return np.mean(accuracies) if accuracies else 0.65
    
    def _get_ensemble_accuracy(self) -> float:
        """Get overall ensemble accuracy"""
        if not self.recent_predictions:
            return 0.65
        
        recent_with_accuracy = [r for r in self.recent_predictions if r.accuracy is not None]
        if not recent_with_accuracy:
            return 0.65
        
        return np.mean([r.accuracy for r in recent_with_accuracy])
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        summary = {
            'overall_accuracy': self._get_ensemble_accuracy(),
            'total_predictions': len(self.prediction_records),
            'recent_predictions': len(self.recent_predictions),
            'active_alerts': len(self.active_alerts),
            'model_performance': {},
            'performance_trends': {},
            'last_update': datetime.now()
        }
        
        # Add individual model performance
        for name, metrics in self.model_metrics.items():
            summary['model_performance'][name] = {
                'recent_accuracy': metrics.recent_accuracy,
                'total_predictions': metrics.total_predictions,
                'correct_predictions': metrics.correct_predictions,
                'overall_accuracy': metrics.correct_predictions / max(1, metrics.total_predictions)
            }
            
            summary['performance_trends'][name] = metrics.accuracy_trend[-10:]  # Last 10 data points
        
        return summary
    
    def register_props_integration(self, props_integration):
        """Register props-to-game integration for dynamic weight adjustment"""
        self.props_integration = props_integration
        logger.info("🔗 Props-to-game integration registered for dynamic weight adjustment")
    
    def add_alert_callback(self, callback):
        """Add callback function for performance alerts"""
        self.alert_callbacks.append(callback)
        logger.info("📢 Alert callback registered")

# Global instance
accuracy_tracker = RealTimeAccuracyTracker()

def get_accuracy_tracker() -> RealTimeAccuracyTracker:
    """Get the global accuracy tracker instance"""
    return accuracy_tracker
