import os
import sys
import asyncio
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tu<PERSON>, Union
from dataclasses import dataclass, field
from pathlib import Path
import json
from src.data_integration.real_data_connector import RealDataConnector, DataConnectionConfig, create_real_data_connector

#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - REAL DATA PIPELINE
==============================================

Production-ready data pipeline that connects the RealDataConnector to MEDUSA's
prediction systems. Provides seamless integration between the 795+ CSV files
and the prediction engines.

Features:
- Real-time data feeding to prediction systems
- Intelligent data preprocessing and feature engineering
- NBA/WNBA parity support
- Performance optimization with caching
- Comprehensive error handling
"""


# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))


logger = logging.getLogger(__name__)

@dataclass
class PipelineConfig:
    """Configuration for the real data pipeline"""
    enable_preprocessing: bool = True
    enable_feature_engineering: bool = True
    enable_validation: bool = True
    cache_processed_data: bool = True
    max_records_per_request: int = 500000  # Increased to handle full dataset processing
    feature_columns: List[str] = field(default_factory=lambda: [
        'FG_PCT', 'FG3_PCT', 'FT_PCT', 'REB', 'AST', 'STL', 'BLK', 'TOV', 'PTS',
        'PLUS_MINUS', 'MIN', 'FGM', 'FGA'
    ])

class RealDataPipeline:
    """
    Production-ready data pipeline for MEDUSA predictions
    
    Connects real data sources to prediction systems with intelligent
    preprocessing and feature engineering.
    """
    
    def __init__(self, pipeline_config: Optional[PipelineConfig] = None,
                 connector_config: Optional[DataConnectionConfig] = None):
        self.pipeline_config = pipeline_config or PipelineConfig()
        self.connector = create_real_data_connector(connector_config)
        self.processed_cache: Dict[str, Tuple[pd.DataFrame, datetime]] = {}
        
        logger.info("🔄 RealDataPipeline initialized")
    
    async def initialize(self) -> bool:
        """Initialize the data pipeline"""
        try:
            success = await self.connector.initialize()
            if success:
                logger.info("✅ RealDataPipeline ready for predictions")
            return success
        except Exception as e:
            logger.error(f"❌ Failed to initialize RealDataPipeline: {e}")
            return False
    
    async def get_prediction_data(self, league: str, data_type: str = "recent_games",
                                 **kwargs) -> pd.DataFrame:
        """
        Get processed data ready for predictions
        
        Args:
            league: 'NBA' or 'WNBA'
            data_type: 'recent_games', 'player_stats', 'team_stats', 'training_data'
            **kwargs: Additional parameters for data retrieval
        
        Returns:
            Processed DataFrame ready for prediction models
        """
        try:
            cache_key = f"{league}_{data_type}_{hash(str(kwargs))}"
            
            # Check cache first
            if (self.pipeline_config.cache_processed_data and 
                cache_key in self.processed_cache):
                cached_data, cached_time = self.processed_cache[cache_key]
                if datetime.now() - cached_time < timedelta(minutes=30):
                    logger.info(f"📋 Using cached data for {league} {data_type}")
                    return cached_data
            
            # Get raw data based on type
            if data_type == "recent_games":
                raw_data = await self.connector.get_recent_games(
                    league, kwargs.get('days', 30)
                )
            elif data_type == "player_stats":
                raw_data = await self.connector.get_player_stats(
                    league, kwargs.get('player_id'), kwargs.get('season')
                )
            elif data_type == "team_stats":
                raw_data = await self.connector.get_team_stats(
                    league, kwargs.get('team_id'), kwargs.get('season')
                )
            elif data_type == "training_data":
                raw_data = await self.connector.get_training_data(
                    league, kwargs.get('features')
                )
            else:
                logger.error(f"Unknown data type: {data_type}")
                return pd.DataFrame()
            
            if raw_data.empty:
                logger.warning(f"No raw data found for {league} {data_type}")
                return pd.DataFrame()
            
            # Process the data
            processed_data = await self._process_data(raw_data, league, data_type)
            
            # Cache the processed data
            if self.pipeline_config.cache_processed_data:
                self.processed_cache[cache_key] = (processed_data, datetime.now())
            
            logger.info(f"✅ Processed {len(processed_data)} records for {league} {data_type}")
            return processed_data
            
        except Exception as e:
            logger.error(f"Error getting prediction data for {league} {data_type}: {e}")
            return pd.DataFrame()
    
    async def _process_data(self, raw_data: pd.DataFrame, league: str, 
                           data_type: str) -> pd.DataFrame:
        """Process raw data for predictions"""
        try:
            processed_data = raw_data.copy()
            
            # Basic preprocessing
            if self.pipeline_config.enable_preprocessing:
                processed_data = self._preprocess_data(processed_data)
            
            # Feature engineering
            if self.pipeline_config.enable_feature_engineering:
                processed_data = self._engineer_features(processed_data, league)
            
            # Validation
            if self.pipeline_config.enable_validation:
                processed_data = self._validate_data(processed_data)
            
            # Limit records if needed
            if len(processed_data) > self.pipeline_config.max_records_per_request:
                processed_data = processed_data.head(self.pipeline_config.max_records_per_request)
                logger.info(f"📊 Limited to {self.pipeline_config.max_records_per_request} records")
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Error processing data: {e}")
            return raw_data  # Return raw data if processing fails
    
    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Enhanced data preprocessing with intelligent missing value handling"""
        try:
            # Remove duplicates
            data = data.drop_duplicates()

            # Enhanced missing value handling with basketball intelligence
            numeric_columns = data.select_dtypes(include=[np.number]).columns

            # Handle different types of numeric columns intelligently
            for col in numeric_columns:
                if col in data.columns:
                    missing_count = data[col].isnull().sum()
                    if missing_count > 0:
                        logger.info(f"Handling {missing_count} missing values in {col}")

                        # Basketball-specific intelligent imputation
                        if 'PCT' in col.upper() or 'PERCENTAGE' in col.upper():
                            # For percentages, use league average or median
                            median_val = data[col].median()
                            fill_val = median_val if not pd.isna(median_val) else 0.5
                            data[col] = data[col].fillna(fill_val)
                        elif 'RATING' in col.upper():
                            # For ratings, use mean or league average
                            mean_val = data[col].mean()
                            fill_val = mean_val if not pd.isna(mean_val) else 100.0
                            data[col] = data[col].fillna(fill_val)
                        elif any(stat in col.upper() for stat in ['PTS', 'POINTS', 'SCORE']):
                            # For scoring stats, use median
                            median_val = data[col].median()
                            fill_val = median_val if not pd.isna(median_val) else 0.0
                            data[col] = data[col].fillna(fill_val)
                        elif any(stat in col.upper() for stat in ['REB', 'AST', 'STL', 'BLK']):
                            # For counting stats, use median
                            median_val = data[col].median()
                            fill_val = median_val if not pd.isna(median_val) else 0.0
                            data[col] = data[col].fillna(fill_val)
                        elif 'WIN' in col.upper() or 'LOSS' in col.upper():
                            # For win/loss records, use 0
                            data[col] = data[col].fillna(0)
                        else:
                            # For other numeric columns, use median or 0
                            median_val = data[col].median()
                            fill_val = median_val if not pd.isna(median_val) else 0.0
                            data[col] = data[col].fillna(fill_val)

            # Handle categorical missing values with basketball intelligence
            categorical_columns = data.select_dtypes(include=['object']).columns
            for col in categorical_columns:
                if col in data.columns:
                    missing_count = data[col].isnull().sum()
                    if missing_count > 0:
                        if 'TEAM' in col.upper():
                            data[col] = data[col].fillna('Unknown_Team')
                        elif 'PLAYER' in col.upper():
                            data[col] = data[col].fillna('Unknown_Player')
                        elif 'POSITION' in col.upper():
                            data[col] = data[col].fillna('Unknown_Position')
                        else:
                            data[col] = data[col].fillna('Unknown')

            # Enhanced percentage column handling
            pct_columns = [col for col in data.columns if 'PCT' in col.upper()]
            for col in pct_columns:
                if col in data.columns:
                    # Ensure percentages are in decimal format (0-1)
                    max_val = data[col].max()
                    if not pd.isna(max_val) and max_val > 1:
                        data[col] = data[col] / 100

                    # Ensure percentages are within valid range
                    data[col] = data[col].clip(0, 1)

            # Validate data quality after preprocessing
            total_missing = data.isnull().sum().sum()
            if total_missing > 0:
                logger.warning(f"Still have {total_missing} missing values after preprocessing")
            else:
                logger.info("All missing values successfully handled")

            return data

        except Exception as e:
            logger.error(f"Preprocessing error: {e}")
            return data
    
    def _engineer_features(self, data: pd.DataFrame, league: str) -> pd.DataFrame:
        """Engineer features for better predictions"""
        try:
            # Make a copy to avoid SettingWithCopyWarning
            data = data.copy()

            # Create efficiency metrics
            if 'FGM' in data.columns and 'FGA' in data.columns:
                data.loc[:, 'FG_EFFICIENCY'] = data['FGM'] / (data['FGA'] + 1e-6)

            if 'PTS' in data.columns and 'MIN' in data.columns:
                data.loc[:, 'PTS_PER_MIN'] = data['PTS'] / (data['MIN'] + 1e-6)

            # Create composite scores
            if all(col in data.columns for col in ['REB', 'AST', 'STL', 'BLK']):
                data.loc[:, 'IMPACT_SCORE'] = (
                    data['REB'] * 0.3 + data['AST'] * 0.4 +
                    data['STL'] * 0.2 + data['BLK'] * 0.1
                )
            
            # League-specific adjustments
            if league.upper() == 'WNBA':
                # WNBA-specific feature engineering
                if 'PTS' in data.columns:
                    data.loc[:, 'WNBA_SCORING_IMPACT'] = data['PTS'] * 1.1  # Adjust for WNBA scoring
                    data.loc[:, 'LEAGUE_FACTOR'] = 1.1  # WNBA league adjustment
            else:
                # NBA-specific adjustments
                if 'PTS' in data.columns:
                    data.loc[:, 'NBA_PACE_FACTOR'] = data['PTS'] * 1.05  # NBA pace adjustment
                    data.loc[:, 'LEAGUE_FACTOR'] = 1.0  # NBA baseline
            
            # Create rolling averages if game sequence data is available
            if 'GAME_DATE' in data.columns or 'DATE' in data.columns:
                date_col = 'GAME_DATE' if 'GAME_DATE' in data.columns else 'DATE'
                try:
                    data[date_col] = pd.to_datetime(data[date_col])
                    data = data.sort_values(date_col)
                    
                    # Rolling averages for key stats
                    for col in ['PTS', 'REB', 'AST']:
                        if col in data.columns:
                            data[f'{col}_ROLLING_AVG'] = data[col].rolling(window=5, min_periods=1).mean()
                except Exception as e:
                    logger.warning(f"Rolling average calculation failed: {e}")
            
            return data
            
        except Exception as e:
            logger.error(f"Feature engineering error: {e}")
            return data
    
    def _validate_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Validate processed data"""
        try:
            # Remove rows with all NaN values
            data = data.dropna(how='all')
            
            # Validate numeric ranges
            numeric_columns = data.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if 'PCT' in col.upper():
                    # Percentages should be 0-1
                    data = data[(data[col] >= 0) & (data[col] <= 1)]
                elif col in ['PTS', 'REB', 'AST', 'STL', 'BLK']:
                    # Stats should be non-negative
                    data = data[data[col] >= 0]
            
            # Remove outliers (basic approach)
            for col in numeric_columns:
                if col in data.columns and len(data) > 10:
                    Q1 = data[col].quantile(0.25)
                    Q3 = data[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 3 * IQR
                    upper_bound = Q3 + 3 * IQR
                    data = data[(data[col] >= lower_bound) & (data[col] <= upper_bound)]
            
            return data
            
        except Exception as e:
            logger.error(f"Data validation error: {e}")
            return data
    
    async def get_live_prediction_features(self, league: str, team_ids: List[str]) -> Dict[str, Any]:
        """Get live features for real-time predictions"""
        try:
            # Get recent team performance
            features = {}
            
            for team_id in team_ids:
                team_data = await self.get_prediction_data(
                    league, "team_stats", team_id=team_id
                )
                
                if not team_data.empty:
                    # Calculate key features
                    features[team_id] = {
                        'avg_points': team_data['PTS'].mean() if 'PTS' in team_data.columns else 0,
                        'avg_rebounds': team_data['REB'].mean() if 'REB' in team_data.columns else 0,
                        'avg_assists': team_data['AST'].mean() if 'AST' in team_data.columns else 0,
                        'fg_percentage': team_data['FG_PCT'].mean() if 'FG_PCT' in team_data.columns else 0,
                        'recent_form': len(team_data),  # Number of recent games
                    }
                else:
                    features[team_id] = {
                        'avg_points': 0, 'avg_rebounds': 0, 'avg_assists': 0,
                        'fg_percentage': 0, 'recent_form': 0
                    }
            
            return features
            
        except Exception as e:
            logger.error(f"Error getting live prediction features: {e}")
            return {}
    
    def get_pipeline_status(self) -> Dict[str, Any]:
        """Get pipeline status and statistics"""
        connector_summary = self.connector.get_data_summary()
        
        return {
            "pipeline_ready": True,
            "cache_size": len(self.processed_cache),
            "connector_status": connector_summary,
            "config": {
                "preprocessing_enabled": self.pipeline_config.enable_preprocessing,
                "feature_engineering_enabled": self.pipeline_config.enable_feature_engineering,
                "validation_enabled": self.pipeline_config.enable_validation,
                "max_records": self.pipeline_config.max_records_per_request
            }
        }
    
    async def close(self):
        """Clean up pipeline resources"""
        await self.connector.close()
        self.processed_cache.clear()
        logger.info("🔄 RealDataPipeline closed")


# Factory function
def create_real_data_pipeline(pipeline_config: Optional[PipelineConfig] = None,
                             connector_config: Optional[DataConnectionConfig] = None) -> RealDataPipeline:
    """Create and initialize a RealDataPipeline instance"""
    return RealDataPipeline(pipeline_config, connector_config)
