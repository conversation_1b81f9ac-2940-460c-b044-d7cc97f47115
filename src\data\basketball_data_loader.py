import os
import sys
import logging
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, date
from typing import Dict, List, Tuple, Optional, Any, Union, Literal, Type
import json
import sqlite3
from pathlib import Path
import re # For regex in season parsing
from dataclasses import dataclass, field
from vault_oracle.wells.nba_api_connector import BasketballDataConnector, APIConnectorConfig
from vault_oracle.wells.nba_api_connector import PlayerProfileV2
import argparse

#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=ef01b2c3-4567-89ab-cdef-0123456789ab | DATE=2025-06-27
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary data loading algorithms and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

"""
HYPER MEDUSA NEURAL VAULT - Basketball Data Loader Business Value Documentation
===============================================================================

basketball_data_loader.py
-------------------------
Provides advanced NBA/WNBA data loading and preprocessing for neural training and analytics.

Business Value:
- Enables robust, scalable, and efficient data ingestion for analytics and model training.
- Supports rapid integration of new data sources and feature pipelines.
- Accelerates development of new analytics and machine learning features.

Extension Points for Plugins & Custom Data Loaders:
---------------------------------------------------
- Subclass `BasketballDataLoader` to add new data loading or preprocessing logic.
- Register data loader plugins via a plugin registry or callback system.
- Add new endpoints or data sources by extending the loader class.
- Use the config system to enable/disable features per environment.

For further details, see module-level docstrings and architecture documentation.
"""

"""
 HYPER MEDUSA NEURAL VAULT - Basketball Data Loader 
═══════════════════════════════════════════════════════════════════════════════════

Advanced Basketball Data Loader with NBA API integration for neural training.
Provides comprehensive real basketball data for training the Neural Basketball Core.

Features:
- NBA API integration via existing BasketballDataConnector
- Historical game data retrieval and processing
- Feature engineering for machine learning
- Data validation and quality checks
- Caching for performance optimization
- Support for both NBA and WNBA data

 Elite Basketball Intelligence Data Pipeline 
"""

# Add project root to path BEFORE any vault_oracle imports
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


# --- NBA API endpoint imports (add these at the top of your file, after other imports) ---
from nba_api.stats.endpoints import (
    ScheduleLeagueV2, boxscoretraditionalv2, boxscoreadvancedv2, shotchartdetail,
    playerdashboardbygeneralsplits, teamdashboardbygeneralsplits, commonallplayers,
    leaguedashteamstats, playergamelog, leaguegamefinder, leagueleaders, teamdetails,
    playerprofilev2, leaguedashplayerclutch, leaguedashteamclutch,
    leaguedashplayerptshot, leaguedashplayershotlocations, leaguedashoppptshot,
    leaguedashptdefend, leaguedashptteamdefend, boxscorescoringv2, boxscoremiscv2,
    boxscorefourfactorsv2, boxscoreusagev2, commonplayerinfo, commonteamyears,
    leaguestandingsv3, teamgamelog, playbyplayv2, gamerotation, leaguedashlineups,
    leagueplayerondetails, teamplayeronoffdetails, teamplayeronoffsummary,
    playervsplayer, teamvsplayer, teamandplayersvsplayers, drafthistory,
    leaguehustlestatsplayer, leaguehustlestatsteam
)
# --- END NBA API endpoint imports ---


# Configure logger
logger = logging.getLogger("basketball_data_loader")
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

@dataclass
class DataLoaderConfig:
    """Configuration for the BasketballDataLoader."""
    historical_data_years: int = 5
    cache_enabled: bool = True
    cache_dir: str = "data_cache"
    data_source_priority: List[str] = field(default_factory=lambda: ["nba_api", "local_db"])
    # You might want to define other loader-specific configurations here

class BasketballDataLoader:
    """
    Manages loading and initial processing of basketball data from various sources,
    primarily through the BasketballDataConnector.
    """

    def __init__(self, config: DataLoaderConfig = DataLoaderConfig()):
        self.config = config
        self.connector_config = APIConnectorConfig(output_dir=self.config.cache_dir)
        self.connector = BasketballDataConnector(self.connector_config)
        logger.info("BasketballDataLoader initialized with NBA API connector.")
        os.makedirs(self.config.cache_dir, exist_ok=True) # Ensure cache directory exists

    async def get_players(self, season: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches all players for a given season."""
        return await self.connector.get_all_players(season=season, league_id=league_id)

    async def get_player_game_logs(self, player_id: int, season: str, season_type: Optional[str] = None, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches game logs for a specific player."""
        return await self.connector.get_player_game_log(player_id=player_id, season=season, season_type=season_type, league_id=league_id)

    async def get_team_statistics(self, season: str, season_type: Optional[str] = None, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team statistics."""
        return await self.connector.get_team_stats(season=season, season_type=season_type, league_id=league_id)

    async def get_schedule(self, season: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches the league schedule."""
        return await self.connector.get_league_schedule(season=season, league_id=league_id)

    def load_training_data(self, league: str = "NBA", data_source: str = "auto") -> Optional[pd.DataFrame]:
        """
        Load training data for neural network training

        Args:
            league: "NBA" or "WNBA"
            data_source: "auto", "csv", "database"

        Returns:
            DataFrame with training data or None if failed
        """
        logger.info(f"🏀 Loading training data for {league}...")

        try:
            # Try different data sources
            if data_source == "auto" or data_source == "csv":
                # First try to load from exported CSV files
                csv_data = self._load_from_csv(league)
                if csv_data is not None and not csv_data.empty:
                    logger.info(f"✅ Loaded {len(csv_data)} records from CSV for {league}")
                    return self._preprocess_training_data(csv_data, league)

            if data_source == "auto" or data_source == "database":
                # Fallback to database
                db_data = self._load_from_database(league)
                if db_data is not None and not db_data.empty:
                    logger.info(f"✅ Loaded {len(db_data)} records from database for {league}")
                    return self._preprocess_training_data(db_data, league)

            logger.warning(f"⚠️ No training data found for {league}")
            return None

        except Exception as e:
            logger.error(f"❌ Error loading training data for {league}: {e}")
            return None

    def _load_from_csv(self, league: str) -> Optional[pd.DataFrame]:
        """Load training data from CSV files"""
        try:
            # Special handling for WNBA - use existing clean data
            if league.upper() == "WNBA":
                wnba_paths = [
                    "data/clean_wnba_training_data.csv",
                    "data/ml_training/wnba_training_data.csv"
                ]
                for path in wnba_paths:
                    if os.path.exists(path):
                        logger.info(f"📄 Loading WNBA data from {path}")
                        return pd.read_csv(path)

            csv_path = f"data/ml_training/{league.lower()}_training_data.csv"
            if os.path.exists(csv_path):
                df = pd.read_csv(csv_path)
                logger.info(f"📁 Loaded {len(df)} records from {csv_path}")
                return df
            else:
                logger.info(f"📁 CSV file not found: {csv_path}")
                return None
        except Exception as e:
            logger.error(f"❌ Error loading CSV for {league}: {e}")
            return None

    def _load_from_database(self, league: str) -> Optional[pd.DataFrame]:
        """Load comprehensive training data from database"""
        try:

            # Try different database paths (updated to correct database files)
            db_paths = [
                "hyper_medusa_consolidated.db",
                "data/unified_nba_wnba_data.db",
                "data/hyper_medusa_consolidated.db",
                "medusa_master.db",
                "data/medusa_master.db",
                "../medusa_master.db"
            ]

            for db_path in db_paths:
                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)

                    # Enhanced query to get comprehensive training data
                    league_filter = f"WHERE league_name = '{league}'" if league in ['NBA', 'WNBA'] else ""
                    query = f"""
                    SELECT
                        player_id, player_name, team_abbreviation, season, season_type,
                        league_name, league_id, data_category, data_type, source_table,
                        stat_value, rank_position, game_id, team_id,
                        CASE WHEN stat_value > 20 THEN 1 ELSE 0 END as high_performer,
                        CASE WHEN rank_position <= 10 THEN 1 ELSE 0 END as top_10_rank,
                        CASE WHEN stat_value > (SELECT AVG(stat_value) FROM unified_nba_wnba_data WHERE league_name = '{league}')
                             THEN 1 ELSE 0 END as above_average_performer
                    FROM unified_nba_wnba_data
                    {league_filter}
                    AND stat_value IS NOT NULL
                    AND stat_value != ''
                    ORDER BY season DESC, player_name
                    """

                    df = pd.read_sql_query(query, conn)

                    # Get additional contextual data if available
                    contextual_query = f"""
                    SELECT DISTINCT
                        source_table, data_category, COUNT(*) as record_count
                    FROM unified_nba_wnba_data
                    {league_filter}
                    GROUP BY source_table, data_category
                    ORDER BY record_count DESC
                    """

                    contextual_df = pd.read_sql_query(contextual_query, conn)
                    logger.info(f"📊 Available data categories for {league}:")
                    for _, row in contextual_df.head(10).iterrows():
                        logger.info(f"   {row['source_table']} ({row['data_category']}): {row['record_count']:,} records")

                    conn.close()

                    if not df.empty:
                        logger.info(f"🗄️ Loaded {len(df):,} comprehensive records from database for {league}")
                        logger.info(f"📅 Seasons covered: {df['season'].nunique()} unique seasons")
                        logger.info(f"🏀 Players covered: {df['player_id'].nunique()} unique players")
                        logger.info(f"🏟️ Teams covered: {df['team_abbreviation'].nunique()} unique teams")
                        return df

            logger.warning(f"🗄️ No database found or no data for {league}")
            return None

        except Exception as e:
            logger.error(f"❌ Error loading from database for {league}: {e}")
            return None

    def _add_league_specific_features(self, df: pd.DataFrame, league: str) -> pd.DataFrame:
        """Add league-specific features to the dataset"""
        try:
            logger.info(f"🏀 Adding {league}-specific features...")

            # Add league identifier
            df['league'] = league

            # League-specific feature engineering
            if league == 'NBA':
                # NBA-specific features (82 game season)
                df['season_progress'] = df.get('games_played', 0) / 82.0
                df['is_nba'] = 1
                df['is_wnba'] = 0

                # NBA-specific position mappings
                df['position_numeric'] = df.get('position', 'G').map({
                    'PG': 1, 'SG': 2, 'SF': 3, 'PF': 4, 'C': 5, 'G': 1.5, 'F': 3.5
                }).fillna(3)

            elif league == 'WNBA':
                # WNBA-specific features (40 game season)
                df['season_progress'] = df.get('games_played', 0) / 40.0
                df['is_nba'] = 0
                df['is_wnba'] = 1

                # WNBA-specific position mappings
                df['position_numeric'] = df.get('position', 'G').map({
                    'PG': 1, 'SG': 2, 'SF': 3, 'PF': 4, 'C': 5, 'G': 1.5, 'F': 3.5
                }).fillna(3)

            # Common league-agnostic features
            df['efficiency_rating'] = (
                df.get('points', 0) + df.get('rebounds', 0) + df.get('assists', 0) +
                df.get('steals', 0) + df.get('blocks', 0) -
                df.get('turnovers', 0) - df.get('missed_fg', 0) - df.get('missed_ft', 0)
            )

            logger.info(f"✅ Added {league}-specific features successfully")
            return df

        except Exception as e:
            logger.error(f"❌ Error adding {league}-specific features: {e}")
            return df

    def _preprocess_training_data(self, df: pd.DataFrame, league: str) -> pd.DataFrame:
        """Preprocess comprehensive training data for neural network"""
        try:
            logger.info(f"🔧 Preprocessing comprehensive training data for {league}...")

            # Add league-specific features first
            df = self._add_league_specific_features(df, league)

            # Handle missing values - convert to numeric first
            for col in df.columns:
                if df[col].dtype == 'object':
                    # Try to convert to numeric, keep as string if fails
                    df[col] = pd.to_numeric(df[col], errors='coerce')  # Use 'coerce' to convert invalid to NaN

            # Now fill missing values
            df = df.fillna(0)

            # Ensure required columns exist
            required_columns = ['stat_value', 'rank_position']
            for col in required_columns:
                if col not in df.columns:
                    df[col] = 0

            # Create comprehensive features for neural network
            features = []

            # Core numerical features
            numerical_features = ['stat_value', 'rank_position']
            for feat in numerical_features:
                if feat in df.columns:
                    features.append(feat)

            # Categorical features (encoded)
            categorical_mappings = {}

            if 'team_abbreviation' in df.columns:
                df['team_encoded'] = pd.Categorical(df['team_abbreviation']).codes
                features.append('team_encoded')
                categorical_mappings['team_abbreviation'] = df['team_abbreviation'].unique()

            if 'season' in df.columns:
                df['season_encoded'] = pd.Categorical(df['season']).codes
                features.append('season_encoded')
                categorical_mappings['season'] = df['season'].unique()

            if 'data_category' in df.columns:
                df['data_category_encoded'] = pd.Categorical(df['data_category']).codes
                features.append('data_category_encoded')

            if 'source_table' in df.columns:
                df['source_table_encoded'] = pd.Categorical(df['source_table']).codes
                features.append('source_table_encoded')

            # Binary features
            binary_features = ['high_performer', 'top_10_rank', 'above_average_performer']
            for feat in binary_features:
                if feat in df.columns:
                    features.append(feat)
                elif feat == 'high_performer':
                    stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                    df['high_performer'] = (stat_values > 15).astype(int)
                    features.append('high_performer')

            # Advanced engineered features
            if 'stat_value' in df.columns:
                try:
                    # Ensure stat_value is numeric
                    df['stat_value'] = pd.to_numeric(df['stat_value'], errors='coerce').fillna(0)

                    # Statistical features
                    df['stat_value_normalized'] = (df['stat_value'] - df['stat_value'].mean()) / (df['stat_value'].std() + 1e-8)
                    df['stat_value_log'] = np.log1p(df['stat_value'].abs())
                    df['stat_value_squared'] = df['stat_value'] ** 2
                    features.extend(['stat_value_normalized', 'stat_value_log', 'stat_value_squared'])

                    # Percentile features
                    df['stat_value_percentile'] = df['stat_value'].rank(pct=True)
                    features.append('stat_value_percentile')
                except Exception as e:
                    logger.warning(f"⚠️ Could not create stat_value features: {e}")

            # League-specific features
            if league == 'NBA':
                df['is_nba'] = 1
                df['is_wnba'] = 0
            else:
                df['is_nba'] = 0
                df['is_wnba'] = 1
            features.extend(['is_nba', 'is_wnba'])

            # Create multiple target variables for different prediction tasks
            targets = {}

            # Primary target: High performance prediction
            if 'win_prediction' not in df.columns:
                try:
                    stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                    targets['win_prediction'] = (stat_values > stat_values.median()).astype(int)
                except Exception as e:
                    logger.warning(f"⚠️ Could not create win_prediction target: {e}")
                    targets['win_prediction'] = np.random.randint(0, 2, len(df))
            else:
                targets['win_prediction'] = pd.to_numeric(df['win_prediction'], errors='coerce').fillna(0).astype(int)

            # Secondary targets
            try:
                stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                targets['elite_performer'] = (stat_values > stat_values.quantile(0.8)).astype(int)
            except Exception:
                targets['elite_performer'] = np.random.randint(0, 2, len(df))

            try:
                rank_values = pd.to_numeric(df.get('rank_position', 100), errors='coerce').fillna(100)
                targets['top_tier'] = (rank_values <= 5).astype(int)
            except Exception:
                targets['top_tier'] = np.random.randint(0, 2, len(df))

            # Add targets to dataframe
            for target_name, target_values in targets.items():
                df[target_name] = target_values

            # Ensure we have sufficient features (pad if necessary)
            while len(features) < 20:  # Increased minimum features
                feature_name = f'engineered_feature_{len(features)}'
                # Create more meaningful synthetic features
                if len(features) % 3 == 0:
                    stat_values = pd.to_numeric(df.get('stat_value', 0), errors='coerce').fillna(0)
                    df[feature_name] = stat_values * np.random.normal(1, 0.1, len(df))
                elif len(features) % 3 == 1:
                    rank_values = pd.to_numeric(df.get('rank_position', 50), errors='coerce').fillna(50)
                    df[feature_name] = rank_values + np.random.normal(0, 5, len(df))
                else:
                    df[feature_name] = np.random.normal(0, 1, len(df))
                features.append(feature_name)

            # Select feature columns and primary target
            target_columns = list(targets.keys())
            df_processed = df[features + target_columns].copy()

            # Log feature engineering results
            logger.info(f"✅ Comprehensive preprocessing completed:")
            logger.info(f"   📊 Records: {len(df_processed):,}")
            logger.info(f"   🔧 Features: {len(features)} (numerical: {len(numerical_features)}, categorical: {len(categorical_mappings)}, binary: {len(binary_features)})")
            logger.info(f"   🎯 Targets: {len(target_columns)} ({', '.join(target_columns)})")
            logger.info(f"   📈 Feature density: {df_processed[features].notna().sum().sum() / (len(features) * len(df_processed)):.2%}")

            return df_processed

        except Exception as e:
            logger.error(f"❌ Error preprocessing comprehensive data for {league}: {e}")
            return df

    async def get_game_box_score_traditional(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches traditional box score for a game."""
        return await self.connector.get_box_score_traditional(game_id=game_id, league_id=league_id)

    async def get_game_box_score_advanced(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches advanced box score for a game."""
        return await self.connector.get_box_score_advanced(game_id=game_id, league_id=league_id)

    async def get_player_shot_chart(self, player_id: int, game_id: str, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches shot chart details for a player in a specific game."""
        return await self.connector.get_shot_chart_detail(player_id=player_id, game_id=game_id, season=season, league_id=league_id, season_type=season_type)

    async def get_player_general_splits(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player's general splits dashboard data."""
        # FIX: Use correct connector method and parameter names
        return await self.connector.get_player_dashboard_by_year_over_year(
            player_id=player_id,
            league_id=league_id,
            season_type=season_type,
            per_mode=per_mode
        )

    async def get_team_general_splits(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team's general splits dashboard data."""
        # FIX: Use correct connector method and parameter names
        return await self.connector.get_team_dashboard_by_year_over_year(
            team_id=team_id,
            league_id=league_id,
            season_type=season_type,
            per_mode=per_mode
        )

    async def get_player_clutch_stats(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player clutch statistics."""
        params = {
            'PlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'PerMode': per_mode or self.connector_config.default_per_mode
        }
        return await self.connector.get_data_from_endpoint(leaguedashplayerclutch, params, file_suffix=f"_player_clutch_{player_id}_{season}")

    async def get_team_clutch_stats(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team clutch statistics."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'PerMode': per_mode or self.connector_config.default_per_mode
        }
        return await self.connector.get_data_from_endpoint(leaguedashteamclutch, params, file_suffix=f"_team_clutch_{team_id}_{season}")

    async def get_player_shooting_locations(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, measure_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player shooting location data."""
        params = {
            'PlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'MeasureType': measure_type or self.connector_config.default_measure_type
        }
        return await self.connector.get_data_from_endpoint(leaguedashplayershotlocations, params, file_suffix=f"_player_shot_loc_{player_id}_{season}") # Changed to leaguedashplayershotlocations

    async def get_opponent_shooting(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, measure_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches opponent shooting statistics against a specific team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'MeasureType': measure_type or self.connector_config.default_measure_type
        }
        return await self.connector.get_data_from_endpoint(leaguedashoppptshot, params, file_suffix=f"_opp_shooting_{team_id}_{season}")

    async def get_player_defensive_stats(self, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player defensive statistics."""
        params = {
            'PlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'PerMode': per_mode or self.connector_config.default_per_mode
        }
        return await self.connector.get_data_from_endpoint(leaguedashptdefend, params, file_suffix=f"_player_defense_{player_id}_{season}")

    async def get_team_defensive_stats(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None, per_mode: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches team defensive statistics (using leaguedashteamstats, which includes defensive metrics)."""
        params = {
            'season': season,
            'season_type_all_star': season_type or self.connector_config.default_season_type,
            'league_id': league_id or self.connector_config.default_league_id,
            'per_mode_detailed': per_mode or self.connector_config.default_per_mode
        }
        df = await self.connector.get_data_from_endpoint(leaguedashteamstats, params, file_suffix=f"_team_defense_{team_id}_{season}")
        # Filter for the specific team_id and only defensive columns if needed
        if df is not None and not df.empty:
            df = df[df['TEAM_ID'] == team_id]
            # Optionally, select only defensive columns here
        return df

    async def get_box_score_scoring(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches scoring breakdown from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscorescoringv2, params, file_suffix=f"_boxscore_scoring_{game_id}")

    async def get_box_score_misc(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches miscellaneous stats from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscoremiscv2, params, file_suffix=f"_boxscore_misc_{game_id}")

    async def get_box_score_four_factors(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches four factors stats from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscorefourfactorsv2, params, file_suffix=f"_boxscore_four_factors_{game_id}")

    async def get_box_score_usage(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches usage stats from box score."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(boxscoreusagev2, params, file_suffix=f"_boxscore_usage_{game_id}")

    # --- END NEW ENDPOINT METHODS ---

    # --- START EXPANDED ENDPOINT METHODS ---
    async def get_common_player_info(self, player_id: int, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches common player info (biographical and career data)."""
        params = {
            'PlayerID': player_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(commonplayerinfo, params, file_suffix=f"_commonplayerinfo_{player_id}")

    async def get_player_profilev2(self, player_id: int, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player profile v2 data."""
        params = {
            'PlayerID': player_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        # Use PlayerProfileV2 class directly
        return await self.connector.get_data_from_endpoint(PlayerProfileV2, params, file_suffix=f"_playerprofilev2_{player_id}")

    async def get_common_team_years(self, team_id: int, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches years a team has existed in the league."""
        params = {
            'TeamID': team_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(commonteamyears, params, file_suffix=f"_commonteamyears_{team_id}")

    async def get_league_standings_v3(self, season: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches league standings (modern version)."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(leaguestandingsv3, params, file_suffix=f"_leaguestandingsv3_{season}")

    async def get_team_game_log(self, team_id: int, season: str, season_type: Optional[str] = None, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches game logs for a specific team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'SeasonType': season_type or self.connector_config.default_season_type,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(teamgamelog, params, file_suffix=f"_teamgamelog_{team_id}_{season}")

    async def get_play_by_play_v2(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches play-by-play data (v2) for a given game ID."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(playbyplayv2, params, file_suffix=f"_playbyplayv2_{game_id}")

    async def get_game_rotation(self, game_id: str, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player rotation data for a game."""
        params = {
            'GameID': game_id,
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(gamerotation, params, file_suffix=f"_gamerotation_{game_id}")

    async def get_league_dash_lineups(self, season: str, group_quantity: int = 5, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches advanced lineup data for the league."""
        params = {
            'Season': season,
            'GroupQuantity': group_quantity,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leaguedashlineups, params, file_suffix=f"_leaguedashlineups_{season}_{group_quantity}")

    async def get_league_player_on_details(self, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches player on-court details for the league."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leagueplayerondetails, params, file_suffix=f"_leagueplayerondetails_{season}")

    async def get_team_player_on_off_details(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches on/off details for all players on a team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamplayeronoffdetails, params, file_suffix=f"_teamplayeronoffdetails_{team_id}_{season}")

    async def get_team_player_on_off_summary(self, team_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches on/off summary for all players on a team."""
        params = {
            'TeamID': team_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamplayeronoffsummary, params, file_suffix=f"_teamplayeronoffsummary_{team_id}_{season}")

    async def get_player_vs_player(self, player_id1: int, player_id2: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches head-to-head stats between two players."""
        params = {
            'PlayerID': player_id1,
            'VsPlayerID': player_id2,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(playervsplayer, params, file_suffix=f"_playervsplayer_{player_id1}_vs_{player_id2}_{season}")

    async def get_team_vs_player(self, team_id: int, player_id: int, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches stats for a team vs a player."""
        params = {
            'TeamID': team_id,
            'VsPlayerID': player_id,
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamvsplayer, params, file_suffix=f"_teamvsplayer_{team_id}_vs_{player_id}_{season}")

    async def get_team_and_players_vs_players(self, team_id: int, player_ids: List[int], season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches stats for a team and its players vs a list of players."""
        params = {
            'TeamID': team_id,
            'VsPlayerIDs': ','.join(map(str, player_ids)),
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(teamandplayersvsplayers, params, file_suffix=f"_teamandplayersvsplayers_{team_id}_vs_{'_'.join(map(str, player_ids))}_{season}")

    async def get_draft_history(self, league_id: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches NBA draft history."""
        params = {
            'LeagueID': league_id or self.connector_config.default_league_id
        }
        return await self.connector.get_data_from_endpoint(drafthistory, params, file_suffix=f"_drafthistory_{league_id or self.connector_config.default_league_id}")

    async def get_league_hustle_stats_player(self, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches hustle stats for all players in a season."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leaguehustlestatsplayer, params, file_suffix=f"_leaguehustlestatsplayer_{season}")

    async def get_league_hustle_stats_team(self, season: str, league_id: Optional[str] = None, season_type: Optional[str] = None) -> Optional[pd.DataFrame]:
        """Fetches hustle stats for all teams in a season."""
        params = {
            'Season': season,
            'LeagueID': league_id or self.connector_config.default_league_id,
            'SeasonType': season_type or self.connector_config.default_season_type
        }
        return await self.connector.get_data_from_endpoint(leaguehustlestatsteam, params, file_suffix=f"_leaguehustlestatsteam_{season}")
    # --- END EXPANDED ENDPOINT METHODS ---

    def _generate_season_list(self, end_year: int, num_years: int) -> List[str]:
        """Generates a list of NBA season strings (e.g., '2022-23')."""
        seasons = []
        for i in range(num_years):
            season_end_year = end_year - i
            season_start_year = season_end_year - 1
            seasons.append(f"{season_start_year}-{str(season_end_year)[-2:]}")
        return seasons[::-1] # Return in ascending order

    def _get_season_id(self, season: str) -> str:
        """Converts a season string (e.g., '2023-24') to an NBA API season ID (e.g., '22023')."""
        try:
            start_year = int(season.split('-')[0])
            return f"2{start_year}"
        except (ValueError, IndexError) as e:
            logger.error(f"Invalid season format: {season}. Expected 'YYYY-YY'. Error: {e}")
            raise ValueError(f"Invalid season format: {season}. Expected 'YYYY-YY'.")

    def _convert_game_id_to_date(self, game_id: str) -> Optional[datetime]:
        """
        Extracts the date from an NBA game ID and returns a datetime object.
        Game ID format: '002YYGGGGG' where YY is the season start year, GGGGG is game sequence.
        Example: '0022300001' -> 2023-XX-XX (date is approximate as it's not directly in ID)
        For mock data, we are relying on GAME_DATE column if available.
        """
        # This function is usually for parsing, not generating.
        # If actual date is needed, it typically comes from the game JSON or schedule endpoint.
        # For mock purposes, we'll assume the GAME_DATE column is reliable.
        logger.warning("Converting game_id to date is an approximation. Rely on 'GAME_DATE' column if available in data.")
        return None # Placeholder as actual date extraction from game_id is complex and not always reliable.

    async def load_historical_data_for_training(self, start_season: str, end_season: str, league_id: str = "00") -> Dict[str, pd.DataFrame]:
        """
        Loads a large dataset of historical game data for training purposes.
        This is a high-level function that orchestrates fetching schedules, then iterating through games.
        """
        all_game_data: List[pd.DataFrame] = []
        all_player_game_logs: List[pd.DataFrame] = []
        all_team_stats: List[pd.DataFrame] = []

        start_year = int(start_season.split('-')[0])
        end_year = int(end_season.split('-')[0]) + 1 # Include the end season fully

        for year in range(start_year, end_year):
            season_str = f"{year}-{str(year+1)[-2:]}"
            logger.info(f"Processing historical data for season: {season_str}")

            schedule_df = await self.get_schedule(season_str, league_id=league_id)
            if schedule_df is None or schedule_df.empty:
                logger.warning(f"No schedule found for season {season_str}, skipping.")
                continue

            game_ids = schedule_df['GAME_ID'].tolist()
            
            for game_id in game_ids:
                game_data_dict = await self.load_game_data(game_id, league_id=league_id, season=season_str)
                
                if 'traditional_box_score' in game_data_dict and not game_data_dict['traditional_box_score'].empty:
                    all_game_data.append(game_data_dict['traditional_box_score'])
                    # Extract player game logs from traditional box score if available
                    all_player_game_logs.append(game_data_dict['traditional_box_score'][['GAME_ID', 'PLAYER_ID', 'TEAM_ID', 'PTS', 'REB', 'AST', 'STL', 'BLK', 'TOV', 'FGM', 'FGA', 'FG_PCT', 'FG3M', 'FG3A', 'FG3_PCT', 'FTM', 'FTA', 'FT_PCT', 'PLUS_MINUS']]) # Example columns
                
                if 'advanced_box_score' in game_data_dict and not game_data_dict['advanced_box_score'].empty:
                     # Merge advanced box score with traditional if both exist
                    if all_game_data and not all_game_data[-1].empty and 'GAME_ID' in all_game_data[-1].columns:
                        last_game_df = all_game_data.pop() # Get the last added game data
                        merged_df = pd.merge(last_game_df, game_data_dict['advanced_box_score'], on=['GAME_ID', 'TEAM_ID'], how='left', suffixes=('_trad', '_adv'))
                        all_game_data.append(merged_df)
                    else:
                        all_game_data.append(game_data_dict['advanced_box_score'])

                # You can add more logic here to process other types of game data (e.g., play_by_play)
                # and append to respective lists or store in a structured way.

            team_stats_df = await self.get_team_statistics(season_str, league_id=league_id)
            if team_stats_df is not None and not team_stats_df.empty:
                all_team_stats.append(team_stats_df)
            else:
                logger.warning(f"No team stats found for season {season_str}.")

        final_data = {
            'all_games_data': pd.concat(all_game_data, ignore_index=True) if all_game_data else pd.DataFrame(),
            'all_player_game_logs': pd.concat(all_player_game_logs, ignore_index=True) if all_player_game_logs else pd.DataFrame(),
            'all_team_stats': pd.concat(all_team_stats, ignore_index=True) if all_team_stats else pd.DataFrame(),
        }
        logger.info(f"Finished loading historical data for seasons {start_season} to {end_season}.")
        return final_data

    async def collect_bulk_data(self, start_year: int, end_year: int, leagues: list = ["00", "10"], out_dir: str = None):
        """
        Collects players, teams, and schedule data for each league and season in the given range.
        Skips files that already exist. Saves as CSV in the specified output directory (default: cache_dir).
        """
        out_dir = out_dir or self.config.cache_dir
        os.makedirs(out_dir, exist_ok=True)
        seasons = [f"{year}-{str(year+1)[-2:]}" for year in range(start_year, end_year)]
        for league_id in leagues:
            logger.info(f"Collecting data for league {league_id}...")
            for season in seasons:
                logger.info(f"  Season: {season}")
                # Players
                players_path = os.path.join(out_dir, f"players_{league_id}_{season}.csv")
                if not os.path.exists(players_path):
                    players_df = await self.get_players(season, league_id=league_id)
                    if players_df is not None and not players_df.empty:
                        players_df.to_csv(players_path, index=False)
                        logger.info(f"    Saved: {players_path}")
                    else:
                        logger.warning(f"    No player data for {league_id} {season}")
                else:
                    logger.info(f"    Skipped (exists): {players_path}")
                # Teams
                teams_path = os.path.join(out_dir, f"teams_{league_id}_{season}.csv")
                if not os.path.exists(teams_path):
                    teams_df = await self.get_team_statistics(season, league_id=league_id)
                    if teams_df is not None and not teams_df.empty:
                        teams_df.to_csv(teams_path, index=False)
                        logger.info(f"    Saved: {teams_path}")
                    else:
                        logger.warning(f"    No team data for {league_id} {season}")
                else:
                    logger.info(f"    Skipped (exists): {teams_path}")
                # Schedule
                schedule_path = os.path.join(out_dir, f"schedule_{league_id}_{season}.csv")
                if not os.path.exists(schedule_path):
                    schedule_df = await self.get_schedule(season, league_id=league_id)
                    if schedule_df is not None and not schedule_df.empty:
                        schedule_df.to_csv(schedule_path, index=False)
                        logger.info(f"    Saved: {schedule_path}")
                    else:
                        logger.warning(f"    No schedule data for {league_id} {season}")
                else:
                    logger.info(f"    Skipped (exists): {schedule_path}")

    async def collect_all_endpoints_bulk(self, start_year: int, end_year: int, leagues: list = ["00", "10"], out_dir: str = None):
        """
        Collects all player-level, team-level, and game-level endpoint data for every player, team, and game,
        for every league and season in the given range. Skips files that already exist. Saves as CSV in the output directory.
        """
        out_dir = out_dir or self.config.cache_dir
        os.makedirs(out_dir, exist_ok=True)
        seasons = [f"{year}-{str(year+1)[-2:]}" for year in range(start_year, end_year)]
        for league_id in leagues:
            logger.info(f"[BULK] Collecting ALL endpoint data for league {league_id}...")
            for season in seasons:
                logger.info(f"  [BULK] Season: {season}")
                # --- Load players, teams, schedule ---
                players_df = None
                teams_df = None
                schedule_df = None

                players_path = os.path.join(out_dir, f"players_{league_id}_{season}.csv")
                if os.path.exists(players_path):
                    players_df = pd.read_csv(players_path)
                else:
                    players_df = await self.get_players(season, league_id=league_id)
                    if players_df is not None and not players_df.empty:
                        players_df.to_csv(players_path, index=False)
                
                teams_path = os.path.join(out_dir, f"teams_{league_id}_{season}.csv")
                if os.path.exists(teams_path):
                    teams_df = pd.read_csv(teams_path)
                else:
                    teams_df = await self.get_team_statistics(season, league_id=league_id)
                    if teams_df is not None and not teams_df.empty:
                        teams_df.to_csv(teams_path, index=False)
                
                schedule_path = os.path.join(out_dir, f"schedule_{league_id}_{season}.csv")
                if os.path.exists(schedule_path):
                    schedule_df = pd.read_csv(schedule_path)
                else:
                    schedule_df = await self.get_schedule(season, league_id=league_id)
                    if schedule_df is not None and not schedule_df.empty:
                        schedule_df.to_csv(schedule_path, index=False)

                # --- Player-level endpoints ---
                if players_df is not None and not players_df.empty:
                    for _, player in players_df.iterrows():
                        player_id = int(player['PERSON_ID']) if 'PERSON_ID' in player else int(player['PLAYER_ID'])
                        player_methods = [
                            (self.get_player_game_logs, "game_logs"),
                            (self.get_player_general_splits, "general_splits"),
                            (self.get_player_clutch_stats, "clutch_stats"),
                            (self.get_player_shooting_locations, "shot_locations"),
                            (self.get_player_defensive_stats, "defensive_stats"),
                            (self.get_player_profilev2, "profile"),
                            (self.get_common_player_info, "commonplayerinfo"),
                            (self.get_league_hustle_stats_player, "hustle_stats_player"),
                        ]
                        for method, suffix in player_methods:
                            out_path = os.path.join(out_dir, f"player_{player_id}_{suffix}_{league_id}_{season}.csv")
                            if os.path.exists(out_path):
                                logger.info(f"    [SKIP] {out_path}")
                                continue
                            try:
                                if suffix == "profile" or suffix == "commonplayerinfo":
                                    df = await method(player_id, league_id=league_id)
                                elif suffix == "hustle_stats_player":
                                    df = await method(season, league_id=league_id)
                                else:
                                    df = await method(player_id, season, league_id=league_id)

                                if df is not None and not df.empty:
                                    df.to_csv(out_path, index=False)
                                    logger.info(f"    [SAVE] {out_path}")
                                else:
                                    logger.warning(f"    [NO DATA] {out_path}")
                            except Exception as e:
                                logger.warning(f"    [ERROR] {out_path}: {e}")
                # --- Team-level endpoints ---
                if teams_df is not None and not teams_df.empty:
                    for _, team in teams_df.iterrows():
                        team_id = int(team['TEAM_ID'])
                        team_methods = [
                            (self.get_team_statistics, "stats"),
                            (self.get_team_general_splits, "general_splits"),
                            (self.get_team_clutch_stats, "clutch_stats"),
                            (self.get_opponent_shooting, "opponent_shooting"),
                            (self.get_team_defensive_stats, "defensive_stats"),
                            (self.get_team_details, "details"),
                            (self.get_common_team_years, "commonteamyears"),
                            (self.get_team_game_log, "teamgamelog"),
                            (self.get_team_player_on_off_details, "teamplayeronoffdetails"),
                            (self.get_team_player_on_off_summary, "teamplayeronoffsummary"),
                            (self.get_league_hustle_stats_team, "hustle_stats_team"),
                        ]
                        for method, suffix in team_methods:
                            out_path = os.path.join(out_dir, f"team_{team_id}_{suffix}_{league_id}_{season}.csv")
                            if os.path.exists(out_path):
                                logger.info(f"    [SKIP] {out_path}")
                                continue
                            try:
                                if suffix == "details":
                                    df = await method(team_id, season, league_id=league_id)
                                elif suffix == "commonteamyears":
                                    df = await method(team_id, league_id=league_id)
                                elif suffix == "hustle_stats_team":
                                    df = await method(season, league_id=league_id)
                                else:
                                    df = await method(team_id, season, league_id=league_id)
                                if df is not None and not df.empty:
                                    df.to_csv(out_path, index=False)
                                    logger.info(f"    [SAVE] {out_path}")
                                else:
                                    logger.warning(f"    [NO DATA] {out_path}")
                            except Exception as e:
                                logger.warning(f"    [ERROR] {out_path}: {e}")
                # --- Game-level endpoints ---
                if schedule_df is not None and not schedule_df.empty:
                    for _, game in schedule_df.iterrows():
                        game_id = str(game['GAME_ID'])
                        game_methods = [
                            (self.get_game_box_score_traditional, "boxscore_traditional"),
                            (self.get_game_box_score_advanced, "boxscore_advanced"),
                            (self.get_box_score_scoring, "boxscore_scoring"),
                            (self.get_box_score_misc, "boxscore_misc"),
                            (self.get_box_score_four_factors, "boxscore_fourfactors"),
                            (self.get_box_score_usage, "boxscore_usage"),
                            (self.get_play_by_play_v2, "playbyplayv2"),
                            (self.get_game_rotation, "gamerotation"),
                        ]
                        for method, suffix in game_methods:
                            out_path = os.path.join(out_dir, f"game_{game_id}_{suffix}_{league_id}.csv")
                            if os.path.exists(out_path):
                                logger.info(f"    [SKIP] {out_path}")
                                continue
                            try:
                                df = await method(game_id, league_id=league_id)
                                if df is not None and not df.empty:
                                    df.to_csv(out_path, index=False)
                                    logger.info(f"    [SAVE] {out_path}")
                                else:
                                    logger.warning(f"    [NO DATA] {out_path}")
                            except Exception as e:
                                logger.warning(f"    [ERROR] {out_path}: {e}")
                # --- League-level endpoints (once per season) ---
                league_methods = [
                    (self.get_league_standings_v3, "leaguestandingsv3"),
                    (self.get_league_leaders, "leagueleaders"),
                    (self.get_league_dash_lineups, "leaguedashlineups"),
                    (self.get_league_player_on_details, "leagueplayerondetails"),
                    (self.get_draft_history, "drafthistory_overall"),
                ]
                for method, suffix in league_methods:
                    out_path = os.path.join(out_dir, f"league_{suffix}_{league_id}_{season}.csv")
                    if os.path.exists(out_path):
                        logger.info(f"    [SKIP] {out_path}")
                        continue
                    try:
                        if suffix == "leagueleaders":
                            df = await method(season, league_id=league_id, stat_category='PTS')
                        elif suffix == "drafthistory_overall":
                            df = await method(league_id=league_id)
                            out_path = os.path.join(out_dir, f"league_{suffix}_{league_id}.csv")
                        else:
                            df = await method(season, league_id=league_id)
                        
                        if df is not None and not df.empty:
                            df.to_csv(out_path, index=False)
                            logger.info(f"    [SAVE] {out_path}")
                        else:
                            logger.warning(f"    [NO DATA] {out_path}")
                    except Exception as e:
                        logger.warning(f"    [ERROR] {out_path}: {e}")
        logger.info("[BULK] Finished collecting all endpoint data.")

if __name__ == "__main__":

    parser = argparse.ArgumentParser(description="Run BasketballDataLoader bulk data collection.")
    parser.add_argument('--start_year', type=int, default=datetime.now().year-2, help='Start year (e.g. 2023)')
    parser.add_argument('--end_year', type=int, default=datetime.now().year, help='End year (exclusive, e.g. 2025)')
    parser.add_argument('--leagues', nargs='+', default=["00", "10"], help='List of league IDs (default: NBA=00, WNBA=10)')
    parser.add_argument('--out_dir', type=str, default=None, help='Output directory for CSVs')
    parser.add_argument('--all_endpoints', action='store_true', help='Collect all endpoints (player/team/game)')
    args = parser.parse_args()

    loader = BasketballDataLoader()
    if args.all_endpoints:
        asyncio.run(loader.collect_all_endpoints_bulk(
            start_year=args.start_year,
            end_year=args.end_year,
            leagues=args.leagues,
            out_dir=args.out_dir
        ))
    else:
        asyncio.run(loader.collect_bulk_data(
            start_year=args.start_year,
            end_year=args.end_year,
            leagues=args.leagues,
            out_dir=args.out_dir
        ))

