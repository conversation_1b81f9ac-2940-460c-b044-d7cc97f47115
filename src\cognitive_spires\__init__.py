#!/usr/bin/env python3
"""
__init__.py for src.cognitive_spires
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
This package contains the expert-level cognitive and prophetic spires of the Oracle.
All spires are now at production/expert level.
"""

import logging

# Configure logger for this package
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Import Expert-Level Cognitive Spires (Production Ready)
logger.info("🏀 MEDUSA VAULT: Initializing Expert-Level Cognitive Spires...")

# Initialize module availability tracking
EXPERT_MODULES_AVAILABLE = True
expert_spires = []

# Core Expert Spires (The Original Five) - Basketball Intelligence Oracles
try:
    from .ChronosFatigueOracle_Expert import ChronosFatigueOracle_Expert as ChronosOracle
    logger.info("🏀 MEDUSA VAULT: ChronosOracle (Expert) imported successfully")
    expert_spires.append('ChronosOracle')
except ImportError as e:
    logger.error(f"❌ ChronosOracle Expert import failed: {e}")
    ChronosOracle = None

try:
    from .NikeVictoryOracle_Expert import NikeVictoryOracle_Expert as NikeOracle
    logger.info("🏀 MEDUSA VAULT: NikeOracle (Expert) imported successfully")
    expert_spires.append('NikeOracle')
except ImportError as e:
    logger.error(f"❌ NikeOracle Expert import failed: {e}")
    NikeOracle = None

try:
    from .AthenaStrategyEngine_Expert import AthenaStrategyEngine_Expert as AthenaOracle
    logger.info("🏀 MEDUSA VAULT: AthenaOracle (Expert) imported successfully")
    expert_spires.append('AthenaOracle')
except ImportError as e:
    logger.error(f"❌ AthenaOracle Expert import failed: {e}")
    AthenaOracle = None

try:
    from .MetisOracle_Expert import MetisOracle_Expert as MetisOracle
    logger.info("🏀 MEDUSA VAULT: MetisOracle (Expert) imported successfully")
    expert_spires.append('MetisOracle')
except ImportError as e:
    logger.error(f"❌ MetisOracle Expert import failed: {e}")
    MetisOracle = None

try:
    from .AresDefenseOracle_Expert import AresDefenseOracle_Expert as AresOracle
    logger.info("🏀 MEDUSA VAULT: AresOracle (Expert) imported successfully")
    expert_spires.append('AresOracle')
except ImportError as e:
    logger.error(f"❌ AresOracle Expert import failed: {e}")
    AresOracle = None

# Advanced Expert Spires - Basketball Intelligence Weavers
try:
    from .FateForge_Expert import FateForge_Expert as FateForge
    logger.info("🏀 MEDUSA VAULT: FateForge (Expert) imported successfully")
    expert_spires.append('FateForge')
    logger.warning("⚠️ FateForge is deprecated: Use UnifiedModelForge with FateArchetypeStrategy")
except ImportError as e:
    logger.error(f"❌ FateForge Expert import failed: {e}")
    FateForge = None

try:
    from src.weavers.FateWeaver_Expert import FateWeaver_Expert as FateWeaver
    logger.info("🏀 MEDUSA VAULT: FateWeaver (Expert) imported successfully")
    expert_spires.append('FateWeaver')
except ImportError as e:
    logger.error(f"❌ FateWeaver Expert import failed: {e}")
    FateWeaver = None

try:
    from src.weavers.GorgonWeave_Expert import GorgonWeave_Expert as GorgonWeave
    logger.info("🏀 MEDUSA VAULT: GorgonWeave (Expert) imported successfully")
    expert_spires.append('GorgonWeave')
except ImportError as e:
    logger.error(f"❌ GorgonWeave Expert import failed: {e}")
    GorgonWeave = None

try:
    from src.weavers.HeroicDeedWeaver_Expert import HeroicDeedWeaver_Expert as HeroicDeedWeaver
    logger.info("🏀 MEDUSA VAULT: HeroicDeedWeaver (Expert) imported successfully")
    expert_spires.append('HeroicDeedWeaver')
except ImportError as e:
    logger.error(f"❌ HeroicDeedWeaver Expert import failed: {e}")
    HeroicDeedWeaver = None

try:
    from .OlympianCouncil_Expert import OlympianCouncil_Expert as OlympianCouncil
    logger.info("🏀 MEDUSA VAULT: OlympianCouncil (Expert) imported successfully")
    expert_spires.append('OlympianCouncil')
except ImportError as e:
    logger.error(f"❌ OlympianCouncil Expert import failed: {e}")
    OlympianCouncil = None

try:
    from .PrometheusRealm_Expert import PrometheusRealm_Expert as PrometheusRealm
    logger.info("🏀 MEDUSA VAULT: PrometheusRealm (Expert) imported successfully")
    expert_spires.append('PrometheusRealm')
except ImportError as e:
    logger.error(f"❌ PrometheusRealm Expert import failed: {e}")
    PrometheusRealm = None

try:
    from .ProphecyOrchestrator_Expert import ProphecyOrchestrator_Expert as ProphecyOrchestrator
    logger.info("🏀 MEDUSA VAULT: ProphecyOrchestrator (Expert) imported successfully")
    expert_spires.append('ProphecyOrchestrator')
except ImportError as e:
    logger.error(f"❌ ProphecyOrchestrator Expert import failed: {e}")
    ProphecyOrchestrator = None

try:
    from src.weavers.SerpentWeave_Expert import SerpentWeave_Expert as SerpentWeave
    logger.info("🏀 MEDUSA VAULT: SerpentWeave (Expert) imported successfully")
    expert_spires.append('SerpentWeave')
except ImportError as e:
    logger.error(f"❌ SerpentWeave Expert import failed: {e}")
    SerpentWeave = None

# Import the CognitiveSpiresFactory_Expert - Basketball Intelligence Factory
try:
    from .CognitiveSpiresFactory_Expert import CognitiveSpiresFactory_Expert
    logger.info("🏀 MEDUSA VAULT: CognitiveSpiresFactory_Expert imported successfully")
    expert_spires.append('CognitiveSpiresFactory_Expert')
except ImportError as e:
    logger.error(f"❌ CognitiveSpiresFactory_Expert import failed: {e}")
    CognitiveSpiresFactory_Expert = None

# Legacy components for backward compatibility (only ChronosMonitor retained)
try:
    from .ChronosMonitor import ChronosMonitor
    logger.info("🏀 MEDUSA VAULT: ChronosMonitor (Legacy) imported for backward compatibility")
    expert_spires.append('ChronosMonitor')
except ImportError as e:
    logger.warning(f"⚠️ ChronosMonitor import failed: {e}")
    ChronosMonitor = None

# Compatibility aliases for basketball intelligence systems
try:
    AthenaAlertSystem = AthenaOracle  # Legacy alias for basketball strategy alerts
except NameError:
    AthenaAlertSystem = None  # Fallback if AthenaOracle not available

# Basketball Intelligence Spires Initialization Complete
successful_imports = len(expert_spires)
total_spires = 13  # Total expected expert spires
logger.info(f"🏀 MEDUSA VAULT: Expert-Level Cognitive Spires initialization complete!")
logger.info(f"🏀 Successfully imported {successful_imports}/{total_spires} expert basketball intelligence spires")

# Update module availability status
if successful_imports >= 8:  # At least 8 core spires needed for full functionality
    EXPERT_MODULES_AVAILABLE = True
    logger.info("✅ Basketball Intelligence System: FULLY OPERATIONAL")
else:
    EXPERT_MODULES_AVAILABLE = False
    logger.warning(f"⚠️ Basketball Intelligence System: LIMITED FUNCTIONALITY ({successful_imports} spires available)")


# Define Basketball Intelligence Exception Classes
class CosmicCollapse(Exception):
    """
    Custom exception for catastrophic failures within the Basketball Oracle system,
    indicating a complete breakdown or unrecoverable state in basketball intelligence.
    """

    def __init__(self, message="A catastrophic cosmic collapse has occurred in basketball intelligence."):
        self.message = message
        super().__init__(self.message)


class BasketballIntelligenceError(Exception):
    """
    Custom exception for basketball intelligence system failures.
    """

    def __init__(self, message="Basketball intelligence system error occurred."):
        self.message = message
        super().__init__(self.message)


# Export all basketball intelligence spires and utilities
__all__ = [
    # Core Expert Basketball Intelligence Spires (The Original Five)
    "ChronosOracle",      # Fatigue and temporal analysis
    "NikeOracle",         # Victory prediction and game outcomes
    "AthenaOracle",       # Strategic analysis and game planning
    "MetisOracle",        # Wisdom and pattern recognition
    "AresOracle",         # Defense and threat analysis

    # Advanced Expert Basketball Intelligence Spires
    "FateForge",          # Deprecated: Use UnifiedModelForge with FateArchetypeStrategy
    "FateWeaver",         # Destiny and outcome weaving
    "GorgonWeave",        # Complex pattern weaving
    "HeroicDeedWeaver",   # Achievement and milestone tracking
    "OlympianCouncil",    # Multi-oracle coordination
    "PrometheusRealm",    # Knowledge and learning systems
    "ProphecyOrchestrator", # Prediction orchestration
    "SerpentWeave",       # Advanced pattern analysis

    # Factory and Management Classes
    "CognitiveSpiresFactory_Expert",  # Basketball intelligence factory

    # Legacy Compatibility (Minimal)
    "ChronosMonitor",     # Legacy temporal monitoring
    "AthenaAlertSystem",  # Legacy strategy alerts

    # Exception Classes
    "CosmicCollapse",
    "BasketballIntelligenceError",

    # Module Status
    "EXPERT_MODULES_AVAILABLE",
]

logger.info("🏀 MEDUSA VAULT: Basketball Intelligence Cognitive Spires package initialization complete!")
logger.info(f"🏀 Available Expert Spires: {expert_spires}")
logger.info(f"🏀 Basketball Intelligence Status: {'OPERATIONAL' if EXPERT_MODULES_AVAILABLE else 'LIMITED'}")
