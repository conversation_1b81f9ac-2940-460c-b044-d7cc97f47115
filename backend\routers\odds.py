import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from enum import Enum
from fastapi import APIRouter, HTTPException, Query, Request, Depends, BackgroundTasks
from pydantic import BaseModel, Field, validator
import redis.asyncio as redis
from backend.auth.dependencies import get_expert_context
from backend.middleware.tier_enforcement import require_tier, enforce_feature_access
from backend.middleware.feature_flags import UserTier
from src.cognitive_spires.schemas.prophecy_lines import LiveOdds, LineHistory, ModelEdge, OddsVelocity
from vault_oracle.wells.expert_odds_integration import create_expert_odds_integrator
from backend.database.models import UserModel as User


"""
HYPER MEDUSA NEURAL VAULT™ - Expert Odds Router
================================================
Enterprise-grade odds analysis with real-time market intelligence
Version: 1.0.0 | Classification: EXPERT
"""




# Configure expert logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("HYPER_MEDUSA_ODDS")

# Expert router with versioned API
router = APIRouter(
    prefix="/api/v1/odds",
    tags=["HYPER MEDUSA - Odds Intelligence"],
    responses={
        404: {"description": "Odds data not found"},
        500: {"description": "Neural vault processing error"}
    }
)

class OddsType(str, Enum):
    """Expert odds type classifications"""
    MONEYLINE = "moneyline"
    SPREAD = "spread"
    TOTAL = "total"
    PLAYER_PROPS = "player_props"
    TEAM_PROPS = "team_props"
    FUTURES = "futures"
    LIVE = "live"

class MarketStatus(str, Enum):
    """Market status classifications"""
    ACTIVE = "active"
    SUSPENDED = "suspended"
    SETTLED = "settled"
    CANCELLED = "cancelled"

class EdgeType(str, Enum):
    """Edge opportunity classifications"""
    NEURAL_EDGE = "neural_edge"  # AI-detected edge
    VALUE_BET = "value_bet"  # Mathematical value
    ARBITRAGE = "arbitrage"  # Cross-book arbitrage
    STEAM_MOVE = "steam_move"  # Line movement edge
    REVERSE_LINE = "reverse_line"  # Reverse line movement

class ExpertOddsRequest(BaseModel):
    """Expert odds request model"""
    titan_clash_id: Optional[str] = Field(None, description="Specific game ID")
    sport: Optional[str] = Field("NBA", description="Sport filter")
    odds_type: Optional[OddsType] = Field(None, description="Odds type filter")
    min_edge: float = Field(0.0, ge=0.0, le=100.0, description="Minimum edge percentage")
    sportsbooks: Optional[List[str]] = Field(None, description="Specific sportsbooks")
    live_only: bool = Field(False, description="Live odds only")
    limit: int = Field(50, ge=1, le=200, description="Results limit")

class ExpertOddsData(BaseModel):
    """Expert odds data model"""
    odds_id: str = Field(..., description="Unique odds identifier")
    titan_clash_id: str = Field(..., description="Game identifier")
    sport: str = Field(..., description="Sport")
    home_team: str = Field(..., description="Home team")
    away_team: str = Field(..., description="Away team")
    odds_type: OddsType = Field(..., description="Type of odds")
    market_status: MarketStatus = Field(..., description="Market status")

    # Core odds data
    sportsbook: str = Field(..., description="Sportsbook name")
    home_odds: int = Field(..., description="Home team odds")
    away_odds: int = Field(..., description="Away team odds")
    line: Optional[float] = Field(None, description="Point spread or total line")

    # Neural analysis
    neural_prediction: float = Field(..., description="Neural network prediction")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Prediction confidence")
    edge_percentage: float = Field(..., description="Calculated edge percentage")
    edge_type: EdgeType = Field(..., description="Type of edge detected")

    # Market intelligence
    opening_odds: Dict[str, int] = Field(default_factory=dict, description="Opening odds")
    current_odds: Dict[str, int] = Field(default_factory=dict, description="Current odds")
    line_movement: float = Field(0.0, description="Line movement since open")
    movement_velocity: float = Field(0.0, description="Rate of line movement")
    steam_percentage: float = Field(0.0, description="Steam move indicator")
    public_betting: float = Field(0.0, description="Public betting percentage")
    sharp_money: float = Field(0.0, description="Sharp money indicator")

    # Advanced metrics
    implied_probability: float = Field(..., description="Implied probability")
    true_probability: float = Field(..., description="Neural true probability")
    fair_odds: int = Field(..., description="Calculated fair odds")
    kelly_percentage: float = Field(0.0, description="Kelly criterion percentage")
    roi_projection: float = Field(0.0, description="ROI projection")

    # Market data
    volume: int = Field(0, description="Betting volume")
    liquidity: float = Field(0.0, description="Market liquidity")
    market_efficiency: float = Field(0.0, description="Efficiency score")

    # Timestamps
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update")
    game_time: Optional[datetime] = Field(None, description="Game start time")

    # Neural insights
    neural_insights: List[str] = Field(default_factory=list, description="AI insights")
    risk_factors: List[str] = Field(default_factory=list, description="Risk factors")

class ExpertOddsAnalytics(BaseModel):
    """Expert odds analytics model"""
    total_markets: int = Field(..., description="Total markets analyzed")
    active_edges: int = Field(..., description="Active edge opportunities")
    neural_locks: int = Field(..., description="Neural lock count")
    arbitrage_opportunities: int = Field(..., description="Arbitrage count")
    average_edge: float = Field(..., description="Average edge percentage")
    total_volume: int = Field(..., description="Total betting volume")
    market_efficiency: float = Field(..., description="Overall efficiency")
    steam_moves: int = Field(..., description="Steam move count")
    top_edges: List[str] = Field(..., description="Top edge categories")

@router.get(
    "/live-odds",
    response_model=List[ExpertOddsData],
    summary="🔴 Get live odds with neural edge detection",
    description="Real-time odds with HYPER MEDUSA neural edge detection and market intelligence"
)
@require_tier(UserTier.PRO, feature="live_odds")
async def get_live_odds(
    background_tasks: BackgroundTasks,
    titan_clash_id: Optional[str] = Query(None, description="Specific game"),
    sport: str = Query("NBA", description="Sport filter"),
    odds_type: Optional[OddsType] = Query(None, description="Odds type"),
    min_edge: float = Query(2.0, ge=0.0, le=100.0, description="Minimum edge %"),
    sportsbooks: Optional[str] = Query(None, description="Comma-separated sportsbooks"),
    limit: int = Query(50, ge=1, le=200, description="Results limit"),
    ctx=Depends(get_expert_context)
):
    """Get live odds with neural edge detection"""
    try:
        # Initialize expert odds integrator
        integrator = create_expert_odds_integrator()

        # Parse sportsbooks
        sportsbook_list = sportsbooks.split(",") if sportsbooks else None

        # Fetch live odds
        request_params = {
            "titan_clash_id": titan_clash_id,
            "sport": sport,
            "odds_type": odds_type.value if odds_type else None,
            "min_edge": min_edge,
            "sportsbooks": sportsbook_list,
            "limit": limit
        }

        odds_data = await integrator.fetch_live_odds(request_params)

        # Enhance with neural analysis
        enhanced_odds = []
        for odds in odds_data[:limit]:
            # Calculate edge metrics
            neural_prediction = odds.get("neural_prediction", 0.5)
            # Ensure home_odds is not zero for implied_prob calculation
            home_odds_val = odds.get("home_odds", -110)
            implied_prob = 0.0
            if home_odds_val != 0:
                implied_prob = 1 / ((abs(home_odds_val) / 100) + 1)

            edge_percentage = 0.0
            if implied_prob != 0:
                edge_percentage = abs((neural_prediction - implied_prob) / implied_prob * 100)

            # Determine edge type
            edge_type = EdgeType.NEURAL_EDGE
            if edge_percentage > 10:
                edge_type = EdgeType.VALUE_BET
            elif odds.get("steam_percentage", 0) > 70:
                edge_type = EdgeType.STEAM_MOVE

            # Calculate fair_odds
            fair_odds_val = 0
            if neural_prediction > 0 and neural_prediction <= 1:
                if neural_prediction > 0.5:
                    fair_odds_val = int(-100 / neural_prediction)
                else:
                    fair_odds_val = int(100 * (1 - neural_prediction) / neural_prediction)


            enhanced_odds_data = ExpertOddsData(
                odds_id=f"medusa_{odds.get('titan_clash_id', '')}_{odds.get('sportsbook', '')}_{datetime.utcnow().strftime('%Y%m%d%H%M')}",
                titan_clash_id=odds.get("titan_clash_id", ""),
                sport=sport,
                home_team=odds.get("home_team", ""),
                away_team=odds.get("away_team", ""),
                odds_type=OddsType(odds.get("odds_type", "moneyline")),
                market_status=MarketStatus(odds.get("market_status", "active")),
                sportsbook=odds.get("sportsbook", ""),
                home_odds=odds.get("home_odds", -110),
                away_odds=odds.get("away_odds", -110),
                line=odds.get("line"),
                neural_prediction=neural_prediction,
                confidence=odds.get("confidence", 0.75),
                edge_percentage=edge_percentage,
                edge_type=edge_type,
                opening_odds=odds.get("opening_odds", {}),
                current_odds=odds.get("current_odds", {}),
                line_movement=odds.get("line_movement", 0.0),
                movement_velocity=odds.get("movement_velocity", 0.0),
                steam_percentage=odds.get("steam_percentage", 0.0),
                public_betting=odds.get("public_betting", 0.0),
                sharp_money=odds.get("sharp_money", 0.0),
                implied_probability=implied_prob,
                true_probability=neural_prediction,
                fair_odds=fair_odds_val,
                kelly_percentage=odds.get("kelly_percentage", 0.0),
                roi_projection=odds.get("roi_projection", 0.0),
                volume=odds.get("volume", 0),
                liquidity=odds.get("liquidity", 0.0),
                market_efficiency=odds.get("market_efficiency", 0.0),
                game_time=odds.get("game_time"),
                neural_insights=odds.get("neural_insights", []),
                risk_factors=odds.get("risk_factors", [])
            )
            enhanced_odds.append(enhanced_odds_data)

        # Log analysis
        background_tasks.add_task(
            log_odds_analysis,
            len(enhanced_odds),
            min_edge,
            sport
        )

        logger.info(f"HYPER MEDUSA: Generated {len(enhanced_odds)} live odds with neural analysis")
        return enhanced_odds

    except Exception as e:
        logger.error(f"Live odds error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Neural odds processing error: {str(e)}")

@router.get(
    "/neural-edges",
    response_model=List[ExpertOddsData],
    summary=" Get neural edge opportunities",
    description="Top edge opportunities detected by HYPER MEDUSA neural networks"
)
@require_tier(UserTier.PRO, feature="neural_edges")
async def get_neural_edges(
    min_edge: float = Query(5.0, ge=1.0, le=50.0, description="Minimum edge %"),
    edge_type: Optional[EdgeType] = Query(None, description="Edge type filter"),
    limit: int = Query(20, ge=1, le=100, description="Results limit"),
    ctx=Depends(get_expert_context)
):
    """Get neural edge opportunities"""
    try:
        # Get edge opportunities from neural analysis
        edges_data = await ctx.prediction_service.get_edge_opportunities({
            "min_edge": min_edge,
            "edge_type": edge_type.value if edge_type else None,
            "limit": limit
        })

        neural_edges = []
        for edge in edges_data:
            # Ensure implied_probability and true_probability are not zero for calculation
            implied_prob_val = edge.get("implied_probability", 0.5)
            true_prob_val = edge.get("true_probability", 0.5)

            # Recalculate edge_percentage to handle potential division by zero
            edge_percentage_val = 0.0
            if implied_prob_val != 0:
                edge_percentage_val = abs((true_prob_val - implied_prob_val) / implied_prob_val * 100)

            # Recalculate fair_odds
            fair_odds_val = 0
            if true_prob_val > 0 and true_prob_val <= 1:
                if true_prob_val > 0.5:
                    fair_odds_val = int(-100 / true_prob_val)
                else:
                    fair_odds_val = int(100 * (1 - true_prob_val) / true_prob_val)

            enhanced_edge = ExpertOddsData(
                odds_id=f"edge_{edge.get('titan_clash_id', '')}_{edge.get('sportsbook', '')}",
                titan_clash_id=edge.get("titan_clash_id", ""),
                sport=edge.get("sport", "NBA"),
                home_team=edge.get("home_team", ""),
                away_team=edge.get("away_team", ""),
                odds_type=OddsType(edge.get("odds_type", "moneyline")),
                market_status=MarketStatus.ACTIVE,
                sportsbook=edge.get("sportsbook", ""),
                home_odds=edge.get("home_odds", -110),
                away_odds=edge.get("away_odds", -110),
                neural_prediction=edge.get("neural_prediction", 0.5),
                confidence=edge.get("confidence", 0.8),
                edge_percentage=edge_percentage_val,
                edge_type=EdgeType(edge.get("edge_type", "neural_edge")),
                implied_probability=implied_prob_val,
                true_probability=true_prob_val,
                fair_odds=fair_odds_val,
                neural_insights=edge.get("neural_insights", ["Neural edge detected"])
            )
            neural_edges.append(enhanced_edge)

        logger.info(f"HYPER MEDUSA: Found {len(neural_edges)} neural edges")
        return neural_edges

    except Exception as e:
        logger.error(f"Neural edges error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Neural edge error: {str(e)}")

@router.get(
    "/market-analytics",
    response_model=ExpertOddsAnalytics,
    summary=" Get odds market analytics",
    description="Comprehensive market analytics and efficiency metrics"
)
async def get_market_analytics(
    sport: str = Query("NBA", description="Sport filter"),
    date: Optional[str] = Query(None, description="Analysis date"),
    ctx=Depends(get_expert_context)
):
    """Get comprehensive market analytics"""
    try:
        analytics_data = await ctx.prediction_service.get_market_analytics({
            "sport": sport,
            "date": date
        })

        analytics = ExpertOddsAnalytics(
            total_markets=analytics_data.get("total_markets", 0),
            active_edges=analytics_data.get("active_edges", 0),
            neural_locks=analytics_data.get("neural_locks", 0),
            arbitrage_opportunities=analytics_data.get("arbitrage_opportunities", 0),
            average_edge=analytics_data.get("average_edge", 0.0),
            total_volume=analytics_data.get("total_volume", 0),
            market_efficiency=analytics_data.get("market_efficiency", 0.0),
            steam_moves=analytics_data.get("steam_moves", 0),
            top_edges=analytics_data.get("top_edges", [])
        )

        logger.info(f"HYPER MEDUSA: Generated market analytics for {sport}")
        return analytics

    except Exception as e:
        logger.error(f"Market analytics error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analytics error: {str(e)}")

@router.get(
    "/line-movement/{titan_clash_id}",
    response_model=List[LineHistory],
    summary=" Get line movement history",
    description="Historical line movement analysis with steam detection"
)
async def get_line_movement(
    titan_clash_id: str,
    sportsbook: Optional[str] = Query(None, description="Specific sportsbook"),
    hours: int = Query(24, ge=1, le=168, description="Hours of history"),
    ctx=Depends(get_expert_context)
):
    """Get line movement history for a game"""
    try:
        movement_data = await ctx.prediction_service.get_line_movement({
            "titan_clash_id": titan_clash_id,
            "sportsbook": sportsbook,
            "hours": hours
        })

        line_history = [LineHistory(**data) for data in movement_data]

        logger.info(f"HYPER MEDUSA: Retrieved line movement for game {titan_clash_id}")
        return line_history

    except Exception as e:
        logger.error(f"Line movement error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Line movement error: {str(e)}")

@router.get(
    "/arbitrage",
    response_model=List[ExpertOddsData],
    summary="⚖️ Get arbitrage opportunities",
    description="Cross-sportsbook arbitrage opportunities detected by neural analysis"
)
@require_tier(UserTier.ENTERPRISE, feature="arbitrage_detection")
async def get_arbitrage_opportunities(
    min_roi: float = Query(1.0, ge=0.1, le=10.0, description="Minimum ROI %"),
    sport: str = Query("NBA", description="Sport filter"),
    limit: int = Query(20, ge=1, le=100, description="Results limit"),
    ctx=Depends(get_expert_context)
):
    """Get arbitrage opportunities"""
    try:
        arb_data = await ctx.prediction_service.get_arbitrage_opportunities({
            "min_roi": min_roi,
            "sport": sport,
            "limit": limit
        })

        arbitrage_opps = []
        for arb in arb_data:
            # Ensure neural_prediction is not zero to prevent ZeroDivisionError
            neural_prediction_val = arb.get("neural_prediction", 0.5)
            implied_probability_val = arb.get("implied_probability", 0.5)

            # Recalculate edge_percentage to handle potential division by zero
            edge_percentage_val = 0.0
            if implied_probability_val != 0:
                edge_percentage_val = abs((neural_prediction_val - implied_probability_val) / implied_probability_val * 100)

            # Recalculate fair_odds
            fair_odds_val = 0
            if neural_prediction_val > 0 and neural_prediction_val <= 1:
                if neural_prediction_val > 0.5:
                    fair_odds_val = int(-100 / neural_prediction_val)
                else:
                    fair_odds_val = int(100 * (1 - neural_prediction_val) / neural_prediction_val)

            enhanced_arb = ExpertOddsData(
                odds_id=f"arb_{arb.get('titan_clash_id', '')}_{datetime.utcnow().strftime('%Y%m%d%H%M')}",
                titan_clash_id=arb.get("titan_clash_id", ""),
                sport=sport,
                home_team=arb.get("home_team", ""),
                away_team=arb.get("away_team", ""),
                odds_type=OddsType.MONEYLINE,
                market_status=MarketStatus.ACTIVE,
                sportsbook=arb.get("primary_sportsbook", ""),
                home_odds=arb.get("home_odds", -110),
                away_odds=arb.get("away_odds", -110),
                neural_prediction=neural_prediction_val,
                confidence=0.95,
                edge_percentage=edge_percentage_val,
                edge_type=EdgeType.ARBITRAGE,
                implied_probability=implied_probability_val,
                true_probability=arb.get("true_probability", 0.5), # Use true_probability from arb_data or default
                fair_odds=fair_odds_val,
                roi_projection=arb.get("roi_percentage", 0.0),
                neural_insights=arb.get("neural_insights", ["Arbitrage opportunity detected"])
            )
            arbitrage_opps.append(enhanced_arb)

        logger.info(f"HYPER MEDUSA: Found {len(arbitrage_opps)} arbitrage opportunities")
        return arbitrage_opps

    except Exception as e:
        logger.error(f"Arbitrage error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Arbitrage error: {str(e)}")

@router.get(
    "/health",
    summary="🏥 Odds service health check",
    description="Health status of HYPER MEDUSA odds neural vault"
)
async def health_check(ctx=Depends(get_expert_context)):
    """Health check for odds service"""
    try:
        # Check integrator and services
        integrator = create_expert_odds_integrator()
        integrator_health = await integrator.health_check()
        prediction_health = await ctx.prediction_service.health_check()

        return {
            "status": "HYPER MEDUSA NEURAL VAULT ONLINE",
            "service": "Odds Intelligence Expert Router",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "odds_integrator": integrator_health.get("status", "unknown"),
            "prediction_service": prediction_health.get("status", "unknown"),
            "neural_networks": "operational",
            "edge_detection": "active",
            "market_analysis": "real-time"
        }

    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        return {
            "status": "DEGRADED",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

async def log_odds_analysis(odds_count: int, min_edge: float, sport: str):
    """Background task to log odds analysis"""
    try:
        logger.info(f"HYPER MEDUSA: Analyzed {odds_count} odds, min_edge={min_edge}%, sport={sport}")
    except Exception as e:
        logger.error(f"Logging error: {str(e)}")
