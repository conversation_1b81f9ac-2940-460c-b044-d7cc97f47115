import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, HTTPException, Depends, Query, BackgroundTasks
from pydantic import BaseModel, Field
from enum import Enum
from backend.database.models import UserModel as User
    from backend.services.automated_reporting_service import (
    from backend.middleware.tier_enforcement import require_tier
    from backend.middleware.feature_flags import UserTier
    from backend.middleware.auth_middleware import get_expert_auth_context, ExpertAuthContext, SecurityLevel


# --- Expert Authentication & Service Imports with Fallback ---
try:
        reporting_service, ReportType, ReportFrequency, ReportFormat,
        ReportSchedule, GeneratedReport
    )
    SERVICES_AVAILABLE = True
except ImportError as e:
    SERVICES_AVAILABLE = False
    # Define fallback mocks if services are not available
    class MockReportingService:
        def __init__(self):
            self.is_running = True
        async def generate_report(self, report_type, parameters): return None
        def get_report_history(self, limit): return []
        def add_schedule(self, schedule):
            """Add schedule with production implementation"""
            logger.info(f"📅 Adding report schedule: {schedule}")
            return {"status": "schedule_added", "schedule": schedule}
        def get_schedules(self): return []
        def remove_schedule(self, schedule_id):
            """Remove schedule with production implementation"""
            logger.info(f"🗑️ Removing report schedule: {schedule_id}")
            return {"status": "schedule_removed", "schedule_id": schedule_id}
        def _calculate_next_run(self, schedule): return None
        async def _deliver_report(self, report, schedule):
            """Deliver report with production implementation"""
            logger.info(f"📧 Delivering report: {report} to schedule: {schedule}")
            return {"status": "report_delivered", "report": report, "schedule": schedule}
    reporting_service = MockReportingService()
    
    class ReportType(str, Enum):
        PERFORMANCE_SUMMARY = "performance_summary"
    class ReportFrequency(str, Enum):
        DAILY = "daily"
    class ReportFormat(str, Enum):
        JSON = "json"
    class UserTier(str, Enum):
        PRO = "PRO"
        ENTERPRISE = "ENTERPRISE"
    class SecurityLevel(str, Enum):
        ADMIN = "ADMIN"
    class ReportSchedule: pass
    class GeneratedReport: pass

    def require_tier(tier, feature=""):
        def decorator(func):
            return func
        return decorator
    def get_expert_auth_context():
        return None

"""
📊 HYPER MEDUSA NEURAL VAULT - Automated Reporting Router
========================================================
API endpoints for automated analytics reporting and delivery system.
"""

# Configure logging
logger = logging.getLogger("HYPER_MEDUSA_REPORTING_API")

# Create router
router = APIRouter(
    prefix="/api/v1/reporting",
    tags=["📊 Automated Reporting"],
    responses={404: {"description": "Report not found"}}
)

# Pydantic models for API
class ReportGenerationRequest(BaseModel):
    """Request model for generating reports"""
    report_type: ReportType
    timeframe: str = Field(default="24h", description="Report timeframe (1h, 6h, 24h, 7d, 30d)")
    format: ReportFormat = Field(default=ReportFormat.JSON, description="Output format")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Additional parameters")

class ScheduleCreateRequest(BaseModel):
    """Request model for creating report schedules"""
    schedule_id: str = Field(..., description="Unique schedule identifier")
    report_type: ReportType
    frequency: ReportFrequency
    format: ReportFormat
    recipients: List[str] = Field(..., description="Email addresses or user IDs")
    tier_filter: Optional[UserTier] = Field(default=None, description="Filter by user tier")
    enabled: bool = Field(default=True, description="Enable/disable schedule")
    parameters: Optional[Dict[str, Any]] = Field(default=None, description="Report parameters")

class ScheduleUpdateRequest(BaseModel):
    """Request model for updating report schedules"""
    frequency: Optional[ReportFrequency] = None
    format: Optional[ReportFormat] = None
    recipients: Optional[List[str]] = None
    enabled: Optional[bool] = None
    parameters: Optional[Dict[str, Any]] = None

class ReportResponse(BaseModel):
    """Response model for generated reports"""
    report_id: str
    report_type: str
    generated_at: str
    timeframe: str
    format: str
    data: Dict[str, Any]
    file_path: Optional[str] = None
    size_bytes: Optional[int] = None

class ScheduleResponse(BaseModel):
    """Response model for report schedules"""
    schedule_id: str
    report_type: str
    frequency: str
    format: str
    recipients: List[str]
    tier_filter: Optional[str] = None
    enabled: bool
    last_run: Optional[str] = None
    next_run: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None

@router.post(
    "/generate",
    response_model=ReportResponse,
    summary="🔄 Generate Report",
    description="Generate an analytics report on-demand"
)
@require_tier(UserTier.PRO, feature="analytics_reports")
async def generate_report(
    request: ReportGenerationRequest,
    background_tasks: BackgroundTasks,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Generate Analytics Report
    
    Generate comprehensive analytics reports including:
    - Performance summaries
    - System health reports
    - Prediction accuracy analysis
    - User analytics and tier usage
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")
        
        logger.info(f"📊 Generating report: {request.report_type.value} for user {auth_context.user_id}")
        
        # Generate report
        report = await reporting_service.generate_report(
            report_type=request.report_type,
            parameters=request.parameters or {"timeframe": request.timeframe}
        )
        
        # Convert to response format
        response = ReportResponse(
            report_id=report.report_id,
            report_type=report.report_type.value,
            generated_at=report.generated_at.isoformat(),
            timeframe=report.timeframe,
            format=report.format.value,
            data=report.data,
            file_path=report.file_path,
            size_bytes=report.size_bytes
        )
        
        logger.info(f"✅ Report generated successfully: {report.report_id}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to generate report: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate report: {str(e)}"
        )

@router.get(
    "/reports",
    response_model=List[ReportResponse],
    summary="📋 List Reports",
    description="Get list of generated reports"
)
@require_tier(UserTier.PRO, feature="analytics_reports")
async def list_reports(
    limit: int = Query(50, description="Maximum number of reports to return"),
    report_type: Optional[ReportType] = Query(None, description="Filter by report type"),
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - List Generated Reports
    
    Retrieve history of generated analytics reports with filtering options.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Get report history
        reports = reporting_service.get_report_history(limit=limit)
        
        # Filter by report type if specified
        if report_type:
            reports = [r for r in reports if r.report_type == report_type]
        
        # Convert to response format
        response = [
            ReportResponse(
                report_id=report.report_id,
                report_type=report.report_type.value,
                generated_at=report.generated_at.isoformat(),
                timeframe=report.timeframe,
                format=report.format.value,
                data=report.data,
                file_path=report.file_path,
                size_bytes=report.size_bytes
            ) for report in reports
        ]
        
        logger.info(f"📋 Retrieved {len(response)} reports for user {auth_context.user_id}")
        return response
        
    except Exception as e:
        logger.error(f"❌ Failed to list reports: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve reports: {str(e)}"
        )

@router.get(
    "/reports/{report_id}",
    response_model=ReportResponse,
    summary="📄 Get Report",
    description="Get specific report by ID"
)
@require_tier(UserTier.PRO, feature="analytics_reports")
async def get_report(
    report_id: str,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Get Specific Report
    
    Retrieve a specific analytics report by its unique identifier.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Find report in history
        reports = reporting_service.get_report_history(limit=1000)  # Search more reports
        report = next((r for r in reports if r.report_id == report_id), None)
        
        if not report:
            raise HTTPException(
                status_code=404,
                detail=f"Report not found: {report_id}"
            )
        
        # Convert to response format
        response = ReportResponse(
            report_id=report.report_id,
            report_type=report.report_type.value,
            generated_at=report.generated_at.isoformat(),
            timeframe=report.timeframe,
            format=report.format.value,
            data=report.data,
            file_path=report.file_path,
            size_bytes=report.size_bytes
        )
        
        logger.info(f"📄 Retrieved report {report_id} for user {auth_context.user_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get report {report_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve report: {str(e)}"
        )

@router.post(
    "/schedules",
    response_model=ScheduleResponse,
    summary="📅 Create Schedule",
    description="Create automated report schedule"
)
@require_tier(UserTier.ENTERPRISE, feature="automated_reporting")
async def create_schedule(
    request: ScheduleCreateRequest,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Create Report Schedule
    
    Create automated report schedules for regular delivery of analytics reports.
    Enterprise tier feature for automated business intelligence.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Verify admin permissions for schedule management
        if auth_context.security_level != SecurityLevel.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Administrator privileges required for schedule management"
            )
        
        logger.info(f"📅 Creating report schedule: {request.schedule_id}")
        
        # Create schedule object
        schedule = ReportSchedule(
            schedule_id=request.schedule_id,
            report_type=request.report_type,
            frequency=request.frequency,
            format=request.format,
            recipients=request.recipients,
            tier_filter=request.tier_filter,
            enabled=request.enabled,
            parameters=request.parameters
        )
        
        # Add to reporting service
        reporting_service.add_schedule(schedule)
        
        # Convert to response format
        response = ScheduleResponse(
            schedule_id=schedule.schedule_id,
            report_type=schedule.report_type.value,
            frequency=schedule.frequency.value,
            format=schedule.format.value,
            recipients=schedule.recipients,
            tier_filter=schedule.tier_filter.value if schedule.tier_filter else None,
            enabled=schedule.enabled,
            last_run=schedule.last_run.isoformat() if schedule.last_run else None,
            next_run=schedule.next_run.isoformat() if schedule.next_run else None,
            parameters=schedule.parameters
        )
        
        logger.info(f"✅ Report schedule created: {request.schedule_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to create schedule: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create schedule: {str(e)}"
        )

@router.get(
    "/schedules",
    response_model=List[ScheduleResponse],
    summary="📋 List Schedules",
    description="Get all report schedules"
)
@require_tier(UserTier.ENTERPRISE, feature="automated_reporting")
async def list_schedules(
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - List Report Schedules
    
    Retrieve all configured automated report schedules.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Verify admin permissions
        if auth_context.security_level != SecurityLevel.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Administrator privileges required for schedule management"
            )
        
        # Get all schedules
        schedules = reporting_service.get_schedules()
        
        # Convert to response format
        response = [
            ScheduleResponse(
                schedule_id=schedule.schedule_id,
                report_type=schedule.report_type.value,
                frequency=schedule.frequency.value,
                format=schedule.format.value,
                recipients=schedule.recipients,
                tier_filter=schedule.tier_filter.value if schedule.tier_filter else None,
                enabled=schedule.enabled,
                last_run=schedule.last_run.isoformat() if schedule.last_run else None,
                next_run=schedule.next_run.isoformat() if schedule.next_run else None,
                parameters=schedule.parameters
            ) for schedule in schedules
        ]
        
        logger.info(f"📋 Retrieved {len(response)} schedules for admin {auth_context.user_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to list schedules: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve schedules: {str(e)}"
        )

@router.put(
    "/schedules/{schedule_id}",
    response_model=ScheduleResponse,
    summary="✏️ Update Schedule",
    description="Update existing report schedule"
)
@require_tier(UserTier.ENTERPRISE, feature="automated_reporting")
async def update_schedule(
    schedule_id: str,
    request: ScheduleUpdateRequest,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Update Report Schedule
    
    Update configuration of existing automated report schedule.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Verify admin permissions
        if auth_context.security_level != SecurityLevel.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Administrator privileges required for schedule management"
            )
        
        # Get existing schedule
        schedules = reporting_service.get_schedules()
        schedule = next((s for s in schedules if s.schedule_id == schedule_id), None)
        
        if not schedule:
            raise HTTPException(
                status_code=404,
                detail=f"Schedule not found: {schedule_id}"
            )
        
        logger.info(f"✏️ Updating report schedule: {schedule_id}")
        
        # Update schedule properties
        update_data = request.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(schedule, key, value)
        
        # Recalculate next run time if frequency changed
        if 'frequency' in update_data:
            schedule.next_run = reporting_service._calculate_next_run(schedule)
        
        # Convert to response format
        response = ScheduleResponse(
            schedule_id=schedule.schedule_id,
            report_type=schedule.report_type.value,
            frequency=schedule.frequency.value,
            format=schedule.format.value,
            recipients=schedule.recipients,
            tier_filter=schedule.tier_filter.value if schedule.tier_filter else None,
            enabled=schedule.enabled,
            last_run=schedule.last_run.isoformat() if schedule.last_run else None,
            next_run=schedule.next_run.isoformat() if schedule.next_run else None,
            parameters=schedule.parameters
        )
        
        logger.info(f"✅ Report schedule updated: {schedule_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to update schedule: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to update schedule: {str(e)}"
        )

@router.delete(
    "/schedules/{schedule_id}",
    summary="🗑️ Delete Schedule",
    description="Delete report schedule"
)
@require_tier(UserTier.ENTERPRISE, feature="automated_reporting")
async def delete_schedule(
    schedule_id: str,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Delete Report Schedule
    
    Remove automated report schedule from the system.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Verify admin permissions
        if auth_context.security_level != SecurityLevel.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Administrator privileges required for schedule management"
            )
        
        logger.info(f"🗑️ Deleting report schedule: {schedule_id}")
        
        # Remove schedule
        reporting_service.remove_schedule(schedule_id)
        
        logger.info(f"✅ Report schedule deleted: {schedule_id}")
        return {"message": f"Schedule {schedule_id} deleted successfully"}
        
    except Exception as e:
        logger.error(f"❌ Failed to delete schedule: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to delete schedule: {str(e)}"
        )

@router.post(
    "/schedules/{schedule_id}/trigger",
    response_model=ReportResponse,
    summary="▶️ Trigger Schedule",
    description="Manually trigger report schedule"
)
@require_tier(UserTier.ENTERPRISE, feature="automated_reporting")
async def trigger_schedule(
    schedule_id: str,
    background_tasks: BackgroundTasks,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Trigger Report Schedule
    
    Manually trigger execution of a scheduled report for testing or immediate delivery.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        # Verify admin permissions
        if auth_context.security_level != SecurityLevel.ADMIN:
            raise HTTPException(
                status_code=403,
                detail="Administrator privileges required for schedule management"
            )
        
        # Get schedule
        schedules = reporting_service.get_schedules()
        schedule = next((s for s in schedules if s.schedule_id == schedule_id), None)
        
        if not schedule:
            raise HTTPException(
                status_code=404,
                detail=f"Schedule not found: {schedule_id}"
            )
        
        logger.info(f"▶️ Manually triggering report schedule: {schedule_id}")
        
        # Generate report
        report = await reporting_service.generate_report(
            report_type=schedule.report_type,
            parameters=schedule.parameters
        )
        
        # Deliver report in background
        background_tasks.add_task(
            reporting_service._deliver_report,
            report,
            schedule
        )
        
        # Convert to response format
        response = ReportResponse(
            report_id=report.report_id,
            report_type=report.report_type.value,
            generated_at=report.generated_at.isoformat(),
            timeframe=report.timeframe,
            format=report.format.value,
            data=report.data,
            file_path=report.file_path,
            size_bytes=report.size_bytes
        )
        
        logger.info(f"✅ Schedule triggered successfully: {schedule_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to trigger schedule: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to trigger schedule: {str(e)}"
        )

@router.get(
    "/status",
    summary="📊 Reporting Status",
    description="Get reporting service status"
)
@require_tier(UserTier.PRO, feature="analytics_reports")
async def get_reporting_status(
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """
    🧠 HYPER MEDUSA NEURAL VAULT - Reporting Service Status
    
    Get current status of the automated reporting service.
    """
    try:
        if not SERVICES_AVAILABLE:
            raise HTTPException(status_code=503, detail="Reporting service is not available.")

        schedules = reporting_service.get_schedules()
        reports = reporting_service.get_report_history(limit=10)
        
        status = {
            "service_status": "running" if reporting_service.is_running else "stopped",
            "total_schedules": len(schedules),
            "active_schedules": len([s for s in schedules if s.enabled]),
            "recent_reports": len(reports),
            "next_scheduled_run": min([s.next_run for s in schedules if s.next_run and s.enabled], default=None),
            "last_report_generated": max([r.generated_at for r in reports], default=None),
            "available_report_types": [rt.value for rt in ReportType],
            "available_frequencies": [rf.value for rf in ReportFrequency],
            "available_formats": [rf.value for rf in ReportFormat]
        }
        
        # Convert datetime objects to ISO strings
        if status["next_scheduled_run"]:
            status["next_scheduled_run"] = status["next_scheduled_run"].isoformat()
        if status["last_report_generated"]:
            status["last_report_generated"] = status["last_report_generated"].isoformat()
        
        logger.info(f"📊 Reporting status retrieved for user {auth_context.user_id}")
        return status
        
    except Exception as e:
        logger.error(f"❌ Failed to get reporting status: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve reporting status: {str(e)}"
        )
