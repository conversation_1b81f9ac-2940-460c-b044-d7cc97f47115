#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Player Props System
==============================================

Advanced player props prediction system for NBA and WNBA.
Provides comprehensive player performance predictions for betting markets
including points, rebounds, assists, and other statistical categories.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger("PlayerPropsSystem")

class PropType(Enum):
    POINTS = "points"
    REBOUNDS = "rebounds"
    ASSISTS = "assists"
    STEALS = "steals"
    BLOCKS = "blocks"
    THREE_POINTERS = "three_pointers"
    TURNOVERS = "turnovers"
    DOUBLE_DOUBLE = "double_double"
    TRIPLE_DOUBLE = "triple_double"

@dataclass
class PlayerPropPrediction:
    """Individual player prop prediction"""
    player_id: str
    player_name: str
    prop_type: PropType
    line_value: float
    predicted_value: float
    over_probability: float
    under_probability: float
    confidence: float
    edge: float
    recommendation: str
    factors: Dict[str, float] = field(default_factory=dict)

@dataclass
class PlayerGameProjection:
    """Complete player game projection"""
    player_id: str
    player_name: str
    team_id: str
    opponent_id: str
    game_date: datetime
    
    # Projected stats
    projected_points: float = 0.0
    projected_rebounds: float = 0.0
    projected_assists: float = 0.0
    projected_steals: float = 0.0
    projected_blocks: float = 0.0
    projected_three_pointers: float = 0.0
    projected_turnovers: float = 0.0
    projected_minutes: float = 0.0
    
    # Probabilities
    double_double_probability: float = 0.0
    triple_double_probability: float = 0.0
    
    # Confidence metrics
    projection_confidence: float = 0.0
    variance_factors: Dict[str, float] = field(default_factory=dict)

class PlayerPropsSystem:
    """
    Advanced player props prediction and analysis system
    """
    
    def __init__(self, db_path: str = "data/basketball_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("PlayerPropsSystem")
        
        # Historical performance weights
        self.performance_weights = {
            'last_5_games': 0.35,
            'last_10_games': 0.25,
            'season_average': 0.20,
            'vs_opponent': 0.15,
            'situational': 0.05
        }
        
        # Variance factors for different prop types
        self.prop_variance = {
            PropType.POINTS: 0.15,
            PropType.REBOUNDS: 0.20,
            PropType.ASSISTS: 0.18,
            PropType.STEALS: 0.35,
            PropType.BLOCKS: 0.40,
            PropType.THREE_POINTERS: 0.30,
            PropType.TURNOVERS: 0.25
        }
    
    def get_player_historical_performance(self, player_id: str, 
                                        prop_type: PropType,
                                        games_back: int = 20) -> Dict[str, float]:
        """
        Get historical performance data for a player
        """
        try:
            # Mock historical data - replace with real database queries
            mock_performance = {
                PropType.POINTS: {
                    'last_5_avg': 24.2,
                    'last_10_avg': 23.8,
                    'season_avg': 24.5,
                    'vs_opponent_avg': 26.1,
                    'home_avg': 25.2,
                    'away_avg': 23.8,
                    'std_dev': 6.2
                },
                PropType.REBOUNDS: {
                    'last_5_avg': 8.4,
                    'last_10_avg': 8.1,
                    'season_avg': 8.3,
                    'vs_opponent_avg': 9.2,
                    'home_avg': 8.7,
                    'away_avg': 7.9,
                    'std_dev': 2.8
                },
                PropType.ASSISTS: {
                    'last_5_avg': 6.8,
                    'last_10_avg': 6.5,
                    'season_avg': 6.7,
                    'vs_opponent_avg': 7.2,
                    'home_avg': 7.1,
                    'away_avg': 6.3,
                    'std_dev': 2.1
                }
            }
            
            return mock_performance.get(prop_type, {
                'last_5_avg': 10.0,
                'last_10_avg': 10.0,
                'season_avg': 10.0,
                'vs_opponent_avg': 10.0,
                'home_avg': 10.0,
                'away_avg': 10.0,
                'std_dev': 3.0
            })
            
        except Exception as e:
            self.logger.error(f"Error getting historical performance: {e}")
            return {
                'last_5_avg': 10.0,
                'last_10_avg': 10.0,
                'season_avg': 10.0,
                'vs_opponent_avg': 10.0,
                'home_avg': 10.0,
                'away_avg': 10.0,
                'std_dev': 3.0
            }
    
    def calculate_situational_adjustments(self, player_id: str, 
                                        game_context: Dict[str, Any]) -> Dict[str, float]:
        """
        Calculate situational adjustments for player performance
        """
        try:
            adjustments = {
                'rest_adjustment': 1.0,
                'matchup_adjustment': 1.0,
                'venue_adjustment': 1.0,
                'motivation_adjustment': 1.0,
                'injury_adjustment': 1.0
            }
            
            # Rest days adjustment
            rest_days = game_context.get('rest_days', 2)
            if rest_days == 0:  # Back-to-back
                adjustments['rest_adjustment'] = 0.92
            elif rest_days == 1:
                adjustments['rest_adjustment'] = 0.96
            elif rest_days >= 3:
                adjustments['rest_adjustment'] = 1.02
            
            # Home/away adjustment
            is_home = game_context.get('is_home', True)
            adjustments['venue_adjustment'] = 1.03 if is_home else 0.97
            
            # Opponent strength adjustment
            opponent_def_rating = game_context.get('opponent_def_rating', 110.0)
            league_avg_def = 110.0
            adjustments['matchup_adjustment'] = league_avg_def / opponent_def_rating
            
            # Game importance adjustment
            game_importance = game_context.get('importance', 'regular')
            if game_importance == 'playoff':
                adjustments['motivation_adjustment'] = 1.05
            elif game_importance == 'rivalry':
                adjustments['motivation_adjustment'] = 1.02
            
            return adjustments
            
        except Exception as e:
            self.logger.error(f"Error calculating situational adjustments: {e}")
            return {
                'rest_adjustment': 1.0,
                'matchup_adjustment': 1.0,
                'venue_adjustment': 1.0,
                'motivation_adjustment': 1.0,
                'injury_adjustment': 1.0
            }
    
    def project_player_performance(self, player_id: str, 
                                 game_context: Dict[str, Any]) -> PlayerGameProjection:
        """
        Project complete player performance for a game
        """
        try:
            # Get historical performance for each stat
            points_data = self.get_player_historical_performance(player_id, PropType.POINTS)
            rebounds_data = self.get_player_historical_performance(player_id, PropType.REBOUNDS)
            assists_data = self.get_player_historical_performance(player_id, PropType.ASSISTS)
            
            # Calculate situational adjustments
            adjustments = self.calculate_situational_adjustments(player_id, game_context)
            overall_adjustment = np.mean(list(adjustments.values()))
            
            # Calculate weighted projections
            projected_points = (
                points_data['last_5_avg'] * self.performance_weights['last_5_games'] +
                points_data['last_10_avg'] * self.performance_weights['last_10_games'] +
                points_data['season_avg'] * self.performance_weights['season_average'] +
                points_data['vs_opponent_avg'] * self.performance_weights['vs_opponent']
            ) * overall_adjustment
            
            projected_rebounds = (
                rebounds_data['last_5_avg'] * self.performance_weights['last_5_games'] +
                rebounds_data['last_10_avg'] * self.performance_weights['last_10_games'] +
                rebounds_data['season_avg'] * self.performance_weights['season_average'] +
                rebounds_data['vs_opponent_avg'] * self.performance_weights['vs_opponent']
            ) * overall_adjustment
            
            projected_assists = (
                assists_data['last_5_avg'] * self.performance_weights['last_5_games'] +
                assists_data['last_10_avg'] * self.performance_weights['last_10_games'] +
                assists_data['season_avg'] * self.performance_weights['season_average'] +
                assists_data['vs_opponent_avg'] * self.performance_weights['vs_opponent']
            ) * overall_adjustment
            
            # Calculate double-double and triple-double probabilities
            dd_prob = self._calculate_double_double_probability(projected_points, projected_rebounds, projected_assists)
            td_prob = self._calculate_triple_double_probability(projected_points, projected_rebounds, projected_assists)
            
            # Calculate projection confidence
            confidence = self._calculate_projection_confidence(points_data, rebounds_data, assists_data, adjustments)
            
            return PlayerGameProjection(
                player_id=player_id,
                player_name=f"Player_{player_id}",
                team_id=game_context.get('team_id', 'TEAM'),
                opponent_id=game_context.get('opponent_id', 'OPP'),
                game_date=game_context.get('game_date', datetime.now()),
                projected_points=projected_points,
                projected_rebounds=projected_rebounds,
                projected_assists=projected_assists,
                projected_steals=1.2 * overall_adjustment,
                projected_blocks=0.8 * overall_adjustment,
                projected_three_pointers=2.1 * overall_adjustment,
                projected_turnovers=2.8 * overall_adjustment,
                projected_minutes=32.5 * overall_adjustment,
                double_double_probability=dd_prob,
                triple_double_probability=td_prob,
                projection_confidence=confidence,
                variance_factors=adjustments
            )
            
        except Exception as e:
            self.logger.error(f"Error projecting player performance: {e}")
            return PlayerGameProjection(
                player_id=player_id,
                player_name=f"Player_{player_id}",
                team_id="TEAM",
                opponent_id="OPP",
                game_date=datetime.now()
            )
    
    def predict_player_prop(self, player_id: str, prop_type: PropType, 
                          line_value: float, game_context: Dict[str, Any]) -> PlayerPropPrediction:
        """
        Predict outcome for a specific player prop
        """
        try:
            # Get player projection
            projection = self.project_player_performance(player_id, game_context)
            
            # Get projected value for this prop type
            projected_value = getattr(projection, f'projected_{prop_type.value}', 0.0)
            
            # Calculate probabilities using normal distribution
            historical_data = self.get_player_historical_performance(player_id, prop_type)
            std_dev = historical_data.get('std_dev', 3.0)
            
            # Adjust standard deviation based on prop variance
            adjusted_std = std_dev * (1 + self.prop_variance.get(prop_type, 0.2))
            
            # Calculate probabilities
            from scipy import stats
            over_prob = 1 - stats.norm.cdf(line_value, projected_value, adjusted_std)
            under_prob = 1 - over_prob
            
            # Calculate edge and recommendation
            edge = abs(projected_value - line_value) / line_value
            confidence = projection.projection_confidence * (1 - self.prop_variance.get(prop_type, 0.2))
            
            if edge > 0.1 and confidence > 0.7:
                if projected_value > line_value:
                    recommendation = "OVER" if over_prob > 0.55 else "LEAN_OVER"
                else:
                    recommendation = "UNDER" if under_prob > 0.55 else "LEAN_UNDER"
            else:
                recommendation = "PASS"
            
            return PlayerPropPrediction(
                player_id=player_id,
                player_name=projection.player_name,
                prop_type=prop_type,
                line_value=line_value,
                predicted_value=projected_value,
                over_probability=over_prob,
                under_probability=under_prob,
                confidence=confidence,
                edge=edge,
                recommendation=recommendation,
                factors=projection.variance_factors
            )
            
        except Exception as e:
            self.logger.error(f"Error predicting player prop: {e}")
            return PlayerPropPrediction(
                player_id=player_id,
                player_name=f"Player_{player_id}",
                prop_type=prop_type,
                line_value=line_value,
                predicted_value=line_value,
                over_probability=0.5,
                under_probability=0.5,
                confidence=0.5,
                edge=0.0,
                recommendation="PASS"
            )
    
    def _calculate_double_double_probability(self, points: float, rebounds: float, assists: float) -> float:
        """Calculate probability of double-double"""
        try:
            # Simple heuristic - improve with historical data
            categories_likely = 0
            if points >= 10: categories_likely += 1
            if rebounds >= 10: categories_likely += 1
            if assists >= 10: categories_likely += 1
            
            if categories_likely >= 2:
                return 0.75
            elif categories_likely == 1:
                return 0.35
            else:
                return 0.10
        except:
            return 0.25
    
    def _calculate_triple_double_probability(self, points: float, rebounds: float, assists: float) -> float:
        """Calculate probability of triple-double"""
        try:
            # Simple heuristic - improve with historical data
            if points >= 10 and rebounds >= 10 and assists >= 10:
                return 0.45
            elif (points >= 8 and rebounds >= 8 and assists >= 8):
                return 0.15
            else:
                return 0.02
        except:
            return 0.05
    
    def _calculate_projection_confidence(self, points_data: Dict, rebounds_data: Dict, 
                                       assists_data: Dict, adjustments: Dict) -> float:
        """Calculate overall projection confidence"""
        try:
            # Base confidence from data consistency
            points_consistency = 1.0 - (points_data.get('std_dev', 5.0) / points_data.get('season_avg', 20.0))
            rebounds_consistency = 1.0 - (rebounds_data.get('std_dev', 3.0) / rebounds_data.get('season_avg', 8.0))
            assists_consistency = 1.0 - (assists_data.get('std_dev', 2.0) / assists_data.get('season_avg', 6.0))
            
            base_confidence = np.mean([points_consistency, rebounds_consistency, assists_consistency])
            
            # Adjust for situational factors
            adjustment_variance = np.std(list(adjustments.values()))
            situational_confidence = 1.0 - adjustment_variance
            
            return min(0.95, max(0.3, base_confidence * situational_confidence))
        except:
            return 0.7
