import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from enum import Enum
from fastapi import APIRouter, HTTPException, Query, Request, Depends, BackgroundTasks
from pydantic import BaseModel, Field, validator
import redis.asyncio as redis
from backend.auth.dependencies import get_expert_context
from src.schemas.api_models import PlayerPropSuggestion, AltLine, PlayerProp
from src.schemas.unified_schemas import StandardizedPlayer, MatchupContext


"""
HYPER MEDUSA NEURAL VAULT™ - Expert Player Props Router
=======================================================
Enterprise-grade player proposition analysis with advanced ML/AI insights
Version: 1.0.0 | Classification: EXPERT
"""




# Configure expert logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("HYPER_MEDUSA_PLAYER_PROPS")

# Expert router with versioned API
router = APIRouter(
    prefix="/api/v1/player-props",
    tags=["HYPER MEDUSA - Player Props"],
    responses={
        404: {"description": "Player props not found"},
        500: {"description": "Neural vault processing error"}
    }
)

class PropType(str, Enum):
    """Expert prop type classifications"""
    POINTS = "points"
    REBOUNDS = "rebounds"
    ASSISTS = "assists"
    BLOCKS = "blocks"
    STEALS = "steals"
    THREES = "three_pointers"
    TURNOVERS = "turnovers"
    MINUTES = "minutes"
    COMBOS = "combos"

class ConfidenceLevel(str, Enum):
    """Expert confidence levels"""
    NEURAL_LOCK = "neural_lock" # 95%+
    HIGH_CONVICTION = "high_conviction" # 85-95%
    MODERATE = "moderate" # 70-85%
    SPECULATIVE = "speculative" # 50-70%

class ExpertPlayerPropRequest(BaseModel):
    """Expert player prop request model"""
    team: Optional[str] = Field(None, description="Team filter")
    player_name: Optional[str] = Field(None, description="Specific player")
    stat_type: Optional[PropType] = Field(None, description="Stat type filter")
    min_confidence: float = Field(0.7, ge=0.0, le=1.0, description="Minimum confidence threshold")
    game_date: Optional[str] = Field(None, description="Target game date (YYYY-MM-DD)")
    limit: int = Field(20, ge=1, le=100, description="Results limit")

class ExpertPlayerProp(BaseModel):
    """Expert player prop response model"""
    prop_id: str = Field(..., description="Unique prop identifier")
    player_name: str = Field(..., description="Player name")
    team: str = Field(..., description="Player team")
    opponent: str = Field(..., description="Opponent team")
    stat_type: PropType = Field(..., description="Stat category")
    line: float = Field(..., description="Betting line")
    over_odds: int = Field(..., description="Over odds")
    under_odds: int = Field(..., description="Under odds")
    prediction: float = Field(..., description="Neural prediction")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    confidence_level: ConfidenceLevel = Field(..., description="Confidence classification")
    edge_percentage: float = Field(..., description="Betting edge percentage")
    alt_lines: List[AltLine] = Field(default_factory=list, description="Alternative lines")
    recent_form: List[float] = Field(default_factory=list, description="Last 5 games")
    matchup_context: Dict[str, Any] = Field(default_factory=dict, description="Matchup analysis")
    injury_risk: float = Field(0.0, ge=0.0, le=1.0, description="Injury risk factor")
    minutes_projection: float = Field(..., description="Projected minutes")
    usage_rate: float = Field(..., description="Usage rate projection")
    pace_factor: float = Field(..., description="Game pace factor")
    home_away_split: Dict[str, float] = Field(default_factory=dict, description="Home/away splits")
    neural_insights: List[str] = Field(default_factory=list, description="AI insights")
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")

class ExpertPropAnalytics(BaseModel):
    """Expert prop analytics model"""
    total_props: int = Field(..., description="Total props analyzed")
    neural_locks: int = Field(..., description="Neural lock count")
    high_conviction: int = Field(..., description="High conviction count")
    average_edge: float = Field(..., description="Average edge percentage")
    top_opportunities: List[str] = Field(..., description="Top opportunity categories")
    market_efficiency: float = Field(..., description="Market efficiency score")
    volatility_index: float = Field(..., description="Market volatility")

@router.get(
    "/neural-props",
    response_model=List[ExpertPlayerProp],
    summary=" Get neural-enhanced player props with ML insights",
    description="Advanced player proposition analysis powered by HYPER MEDUSA neural networks"
)
async def get_neural_player_props(
    background_tasks: BackgroundTasks,
    team: Optional[str] = Query(None, description="Filter by team"),
    player_name: Optional[str] = Query(None, description="Filter by player"),
    stat_type: Optional[PropType] = Query(None, description="Filter by stat type"),
    min_confidence: float = Query(0.7, ge=0.0, le=1.0, description="Minimum confidence"),
    game_date: Optional[str] = Query(None, description="Target game date"),
    limit: int = Query(20, ge=1, le=100, description="Results limit"),
    ctx=Depends(get_expert_context)
):
    """Get neural-enhanced player props with advanced ML insights"""
    try:
        # Build neural prop request
        request_params = {
            "team": team,
            "player_name": player_name,
            "stat_type": stat_type.value if stat_type else None,
            "min_confidence": min_confidence,
            "game_date": game_date,
            "limit": limit
        }

        # Get props from neural prediction service
        props_data = await ctx.prediction_service.get_player_props(request_params)

        # Enhance with neural insights
        enhanced_props = []
        for prop_data in props_data[:limit]:
            # Calculate confidence level
            confidence = prop_data.get("confidence", 0.0)
            if confidence >= 0.95:
                confidence_level = ConfidenceLevel.NEURAL_LOCK
            elif confidence >= 0.85:
                confidence_level = ConfidenceLevel.HIGH_CONVICTION
            elif confidence >= 0.70:
                confidence_level = ConfidenceLevel.MODERATE
            else:
                confidence_level = ConfidenceLevel.SPECULATIVE

            # Calculate edge percentage
            prediction = prop_data.get("prediction", 0.0)
            line = prop_data.get("line", 0.0)
            edge_percentage = abs((prediction - line) / line * 100) if line > 0 else 0.0

            enhanced_prop = ExpertPlayerProp(
                prop_id=f"medusa_{prop_data.get('player_name', '')}_{prop_data.get('stat_type', '')}_{datetime.utcnow().strftime('%Y%m%d')}",
                player_name=prop_data.get("player_name", ""),
                team=prop_data.get("team", ""),
                opponent=prop_data.get("opponent", ""),
                stat_type=PropType(prop_data.get("stat_type", "points")),
                line=prop_data.get("line", 0.0),
                over_odds=prop_data.get("over_odds", -110),
                under_odds=prop_data.get("under_odds", -110),
                prediction=prediction,
                confidence=confidence,
                confidence_level=confidence_level,
                edge_percentage=edge_percentage,
                alt_lines=prop_data.get("alt_lines", []),
                recent_form=prop_data.get("recent_form", []),
                matchup_context=prop_data.get("matchup_context", {}),
                injury_risk=prop_data.get("injury_risk", 0.0),
                minutes_projection=prop_data.get("minutes_projection", 30.0),
                usage_rate=prop_data.get("usage_rate", 20.0),
                pace_factor=prop_data.get("pace_factor", 100.0),
                home_away_split=prop_data.get("home_away_split", {}),
                neural_insights=prop_data.get("neural_insights", [])
            )
            enhanced_props.append(enhanced_prop)

        # Log neural analysis
        background_tasks.add_task(
            log_prop_analysis,
            len(enhanced_props),
            min_confidence,
            stat_type.value if stat_type else "all"
        )

        logger.info(f"HYPER MEDUSA: Generated {len(enhanced_props)} neural player props")
        return enhanced_props

    except Exception as e:
        logger.error(f"Neural props error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Neural vault processing error: {str(e)}")

@router.get(
    "/prop-analytics",
    response_model=ExpertPropAnalytics,
    summary=" Get player prop market analytics",
    description="Advanced analytics on player prop market efficiency and opportunities"
)
async def get_prop_analytics(
    date: Optional[str] = Query(None, description="Analysis date (YYYY-MM-DD)"),
    ctx=Depends(get_expert_context)
):
    """Get comprehensive prop market analytics"""
    try:
        # Get analytics from prediction service
        analytics_data = await ctx.prediction_service.get_prop_analytics(date)

        analytics = ExpertPropAnalytics(
            total_props=analytics_data.get("total_props", 0),
            neural_locks=analytics_data.get("neural_locks", 0),
            high_conviction=analytics_data.get("high_conviction", 0),
            average_edge=analytics_data.get("average_edge", 0.0),
            top_opportunities=analytics_data.get("top_opportunities", []),
            market_efficiency=analytics_data.get("market_efficiency", 0.0),
            volatility_index=analytics_data.get("volatility_index", 0.0)
        )

        logger.info(" MEDUSA VAULT: HYPER MEDUSA: Generated prop analytics")
        return analytics

    except Exception as e:
        logger.error(f"Prop analytics error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Neural analytics error: {str(e)}")

@router.get(
    "/neural-locks",
    response_model=List[ExpertPlayerProp],
    summary="🔒 Get neural lock props (95%+ confidence)",
    description="Highest confidence player props from HYPER MEDUSA neural analysis"
)
async def get_neural_locks(
    limit: int = Query(10, ge=1, le=50, description="Max neural locks"),
    ctx=Depends(get_expert_context)
):
    """Get neural lock props with 95%+ confidence"""
    try:
        # Get high-confidence props
        props_data = await ctx.prediction_service.get_player_props({
            "min_confidence": 0.95,
            "limit": limit
        })

        neural_locks = []
        for prop_data in props_data:
            enhanced_prop = ExpertPlayerProp(
                prop_id=f"lock_{prop_data.get('player_name', '')}_{prop_data.get('stat_type', '')}",
                player_name=prop_data.get("player_name", ""),
                team=prop_data.get("team", ""),
                opponent=prop_data.get("opponent", ""),
                stat_type=PropType(prop_data.get("stat_type", "points")),
                line=prop_data.get("line", 0.0),
                over_odds=prop_data.get("over_odds", -110),
                under_odds=prop_data.get("under_odds", -110),
                prediction=prop_data.get("prediction", 0.0),
                confidence=prop_data.get("confidence", 0.95),
                confidence_level=ConfidenceLevel.NEURAL_LOCK,
                edge_percentage=prop_data.get("edge_percentage", 0.0),
                neural_insights=prop_data.get("neural_insights", ["Neural lock identified"])
            )
            neural_locks.append(enhanced_prop)

        logger.info(f"HYPER MEDUSA: Found {len(neural_locks)} neural locks")
        return neural_locks

    except Exception as e:
        logger.error(f"Neural locks error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Neural lock error: {str(e)}")

@router.get(
    "/player/{player_name}/props",
    response_model=List[ExpertPlayerProp],
    summary="👤 Get all props for specific player",
    description="Comprehensive prop analysis for individual player"
)
async def get_player_props(
    player_name: str,
    game_date: Optional[str] = Query(None, description="Target game date"),
    ctx=Depends(get_expert_context)
):
    """Get all props for a specific player"""
    try:
        props_data = await ctx.prediction_service.get_player_props({
            "player_name": player_name,
            "game_date": game_date
        })

        player_props = []
        for prop_data in props_data:
            confidence = prop_data.get("confidence", 0.0)
            if confidence >= 0.95:
                confidence_level = ConfidenceLevel.NEURAL_LOCK
            elif confidence >= 0.85:
                confidence_level = ConfidenceLevel.HIGH_CONVICTION
            elif confidence >= 0.70:
                confidence_level = ConfidenceLevel.MODERATE
            else:
                confidence_level = ConfidenceLevel.SPECULATIVE

            enhanced_prop = ExpertPlayerProp(
                prop_id=f"player_{player_name}_{prop_data.get('stat_type', '')}",
                player_name=player_name,
                team=prop_data.get("team", ""),
                opponent=prop_data.get("opponent", ""),
                stat_type=PropType(prop_data.get("stat_type", "points")),
                line=prop_data.get("line", 0.0),
                over_odds=prop_data.get("over_odds", -110),
                under_odds=prop_data.get("under_odds", -110),
                prediction=prop_data.get("prediction", 0.0),
                confidence=confidence,
                confidence_level=confidence_level,
                edge_percentage=prop_data.get("edge_percentage", 0.0),
                neural_insights=prop_data.get("neural_insights", [])
            )
            player_props.append(enhanced_prop)

        logger.info(f"HYPER MEDUSA: Generated props for {player_name}")
        return player_props

    except Exception as e:
        logger.error(f"Player props error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Player props error: {str(e)}")

@router.get(
    "/health",
    summary="🏥 Player props service health check",
    description="Health status of HYPER MEDUSA player props neural vault"
)
async def health_check(ctx=Depends(get_expert_context)):
    """Health check for player props service"""
    try:
        # Check prediction service
        health_status = await ctx.prediction_service.health_check()

        return {
            "status": "HYPER MEDUSA NEURAL VAULT ONLINE",
            "service": "Player Props Expert Router",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "prediction_service": health_status.get("status", "unknown"),
            "neural_networks": "operational",
            "confidence_threshold": "optimized"
        }

    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        return {
            "status": "DEGRADED",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

async def log_prop_analysis(prop_count: int, min_confidence: float, stat_type: str):
    """Background task to log prop analysis"""
    try:
        logger.info(f"HYPER MEDUSA: Analyzed {prop_count} props, min_confidence={min_confidence}, stat_type={stat_type}")
    except Exception as e:
        logger.error(f"Logging error: {str(e)}")
