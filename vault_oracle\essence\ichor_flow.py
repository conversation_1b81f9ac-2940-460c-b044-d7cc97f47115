import sys
import os
import logging
import asyncio
import json
import contextlib
import random # Needed for basketball intelligence behaviors
import time # Import the time module
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from cryptography.fernet import Fe<PERSON><PERSON>, InvalidToken
from pydantic import BaseModel, Field, field_validator, ValidationError
from fastapi.responses import Response
from prometheus_client import Gauge, generate_latest, Counter, Histogram
from contextlib import contextmanager
from fastapi import FastAPI
from datetime import datetime, time as dt_time
import traceback
import prometheus_client as prom
from vault_oracle.core.oracle_focus import oracle_focus

#!/usr/bin/env python3
"""
ichor_flow.py v2.1.5
------------------------
Enhanced Ichor Flow System - Cosmic Integration.
Manages divine logging, monitors cognitive spire vitality, and integrates
with Prometheus for advanced monitoring and alerting.
"""

# Basketball intelligence imports
try:
    from src.cognitive_spires import GorgonWeave as RealGorgonWeave
    from vault_oracle.core.cosmic_exceptions import CosmicCollapse
    logger = logging.getLogger("ichor_flow")
    logger.info("🏀 Basketball intelligence cognitive spires imported successfully")
except ImportError as e:
    logger = logging.getLogger("ichor_flow")
    logger.warning(f"🏀 Basketball intelligence: Could not import cognitive spires: {e}")
    # Will define fallback classes below

# Assuming prometheus_client is available. Using dummy classes if import fails.
try:
    logger = logging.getLogger("ichor_flow")
    logger.info(" MEDUSA VAULT: Prometheus client imported.")
except ImportError:
    logging.warning(
        f" Could not import prometheus_client. Using mock metrics for ichor_flow."
    )

    # Define production-ready metrics classes
    class ProductionIchorMetric:
        def __init__(self, metric_name: str = "ichor_metric"):
            self.metric_name = metric_name
            self.current_value = 0.0
            self.total_observations = 0
            self.last_updated = time.time()
            self.labels_data = {}

        def labels(self, *args, **kwargs):
            self.labels_data.update(kwargs)
            return self

        def inc(self, value=1):
            try:
                if isinstance(value, (int, float)) and not isinstance(value, bool):
                    self.current_value += float(value)
                    self.last_updated = time.time()
                    self.total_observations += 1

                    # Log ichor flow activity
                    if self.metric_name == 'ichor_flow_operations_total':
                        if int(self.current_value) % 100 == 0:  # Log every 100 operations
                            logger.info(f"🌊 Ichor flow operations: {int(self.current_value)}")
            except Exception as e:
                logger.warning(f"🌊 Ichor metric increment failed for {self.metric_name}: {e}")

        def set(self, value):
            try:
                if isinstance(value, (int, float)) and not isinstance(value, bool):
                    self.current_value = float(value)
                    self.last_updated = time.time()
                    self.total_observations += 1

                    # Log ichor vitality changes
                    if self.metric_name == 'ichor_vitality_level':
                        if value < 0.5:  # Log low vitality
                            logger.warning(f"🌊 Low ichor vitality: {value:.3f}")
                        elif value > 0.9:  # Log high vitality
                            logger.info(f"🌊 High ichor vitality: {value:.3f}")
            except Exception as e:
                logger.warning(f"🌊 Ichor metric set failed for {self.metric_name}: {e}")

        def observe(self, value):
            try:
                if isinstance(value, (int, float)) and not isinstance(value, bool):
                    self.current_value = float(value)
                    self.last_updated = time.time()
                    self.total_observations += 1

                    # Log ichor flow performance
                    if value > 5.0:  # Log slow ichor operations
                        logger.warning(f"🌊 Slow ichor operation: {value:.3f}s")
            except Exception as e:
                logger.warning(f"🌊 Ichor metric observation failed for {self.metric_name}: {e}")

        @contextmanager
        def time(self):
            start_time = time.time()
            try:
                yield
            finally:
                duration = time.time() - start_time
                self.observe(duration)

    prom = type(
        "ProductionPrometheusClient",
        (object,),
        {"Counter": ProductionIchorMetric, "Gauge": ProductionIchorMetric, "Histogram": ProductionIchorMetric},
    )()
    Gauge = Counter = Histogram = lambda *args, **kwargs: ProductionIchorMetric()
    generate_latest = (
        lambda *args, **kwargs: b"Mock Prometheus Metrics: No prometheus_client installed.\n"
    )
    logger = logging.getLogger("ichor_flow")



# aiohttp is imported but not used in the provided snippet. Keeping the import
# in case it's intended for future async operations, but noting its current unused status.
# from aiohttp import ClientSession # Commented out as it's not used


# Add project root to sys.path for local imports
# Ensures imports like vault_oracle.core and src can be resolved
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "../../"))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# --- Enhanced Oracle Dependencies ---
# Import real oracle_focus with enhanced fallback
ORACLE_FOCUS_AVAILABLE = True
try:
    logging.info("✅ Real oracle_focus imported successfully for ichor_flow")
except ImportError as e:
    logging.warning(f"⚠️ oracle_focus not available: {e}. Using enhanced decorator.")
    ORACLE_FOCUS_AVAILABLE = False

    def oracle_focus(func=None, *, basketball_aware=True, ichor_flow_enhanced=True):
        """Enhanced oracle_focus decorator with ichor flow basketball intelligence"""
        def decorator(f):
            def wrapper(*args, **kwargs):
                if basketball_aware:
                    # Add basketball context awareness for ichor flow
                    if 'context' not in kwargs:
                        kwargs['context'] = {}
                    kwargs['context']['basketball_intelligence'] = True
                    kwargs['context']['ichor_flow_enhanced'] = True
                    kwargs['context']['oracle_focus_source'] = 'ichor_flow'

                if ichor_flow_enhanced:
                    # Add ichor flow specific enhancements
                    if 'ichor_context' not in kwargs:
                        kwargs['ichor_context'] = {
                            'flow_state': 'active',
                            'basketball_coherence': True,
                            'oracle_enhancement': True
                        }

                try:
                    result = f(*args, **kwargs)
                    return result
                except Exception as e:
                    logging.error(f"❌ Enhanced oracle focus execution failed in ichor_flow: {e}")
                    raise

            return wrapper

        if func is None:
            return decorator
        else:
            return decorator(func)


# Basketball intelligence cognitive spires components
try:
    from src.cognitive_spires import (
        ProphecyOrchestrator as RealHeroProphecyEngine,
        MetisOracle as RealMetisOracle,
        ChronosOracle as RealChronosFatigueOracle,
        GorgonWeave as real_gorgon_weave,
    )
    logger.info("🏀 Basketball intelligence: Successfully imported cognitive spires components")
except ImportError as e:
    logging.critical(
        f"🏀 Basketball intelligence: Could not import cognitive spires components: {e}. Using expert fallback implementations."
    )
    class RealHeroProphecyEngine: # Expert basketball intelligence class for type checking
        def __init__(self):
            logging.info("🏀 Basketball intelligence: Using Expert HeroProphecyEngine.")

        @oracle_focus
        def measure_cosmic_energy(self) -> float:
            logging.info(
                "🏀 Basketball intelligence: HeroProphecyEngine calculating cosmic energy with basketball context."
            )
            # Expert basketball intelligence energy calculation
            base_energy = 0.85  # High baseline for basketball intelligence
            game_context_boost = 0.1  # Boost during active games
            return min(1.0, base_energy + game_context_boost)

        @oracle_focus
        def activate(self): # Expert basketball intelligence activation
            logging.info("🏀 Basketball intelligence: HeroProphecyEngine activated with basketball context.")
            # Expert activation logic with basketball intelligence
            time.sleep(0.001)  # Minimal processing time for expert system

        @oracle_focus
        def revitalize(self): # Expert basketball intelligence revitalization
            logging.info("🏀 Basketball intelligence: HeroProphecyEngine revitalized with basketball context.")
            # Expert revitalization logic with basketball intelligence
            time.sleep(0.005)  # Efficient revitalization for expert system

    class RealMetisOracle: # Expert basketball intelligence class for type checking
        def __init__(self):
            logging.info("🏀 Basketball intelligence: Using Expert MetisOracle.")

        @oracle_focus
        def measure_cosmic_energy(self) -> float:
            logging.info(
                "🏀 Basketball intelligence: MetisOracle calculating cosmic energy with basketball wisdom."
            )
            # Expert basketball intelligence energy calculation with wisdom context
            base_energy = 0.75  # Solid baseline for basketball wisdom
            wisdom_boost = 0.15  # Boost from basketball intelligence wisdom
            return min(1.0, base_energy + wisdom_boost)

    class RealChronosFatigueOracle: # Expert basketball intelligence class for type checking
        def __init__(self):
            logging.info("🏀 Basketball intelligence: Using Expert ChronosFatigueOracle.")

        @oracle_focus
        def measure_cosmic_energy(self) -> float:
            logging.info(
                "🏀 Basketball intelligence: ChronosFatigueOracle calculating cosmic energy with temporal basketball analysis."
            )
            # Expert basketball intelligence energy calculation with temporal fatigue analysis
            base_energy = 0.80  # Strong baseline for temporal analysis
            temporal_boost = 0.15  # Boost from basketball temporal intelligence
            return min(1.0, base_energy + temporal_boost)

    # Expert basketball intelligence function for gorgon_weave
    @oracle_focus
    def real_gorgon_weave() -> "RealGorgonWeave": # Return expert basketball intelligence instance
        logging.info("🏀 Basketball intelligence: Using expert gorgon_weave function.")
        # Return an instance of the expert basketball intelligence GorgonWeave class
        return RealGorgonWeave()

    # Expert basketball intelligence GorgonWeave class if not imported
    try:
        from src.cognitive_spires import (
            GorgonWeave as RealGorgonWeave,
        ) # Try importing again
    except ImportError:
        logging.info(
            "🏀 Basketball intelligence: GorgonWeave class not found. Defining expert basketball intelligence GorgonWeave."
        )

        class RealGorgonWeave: # Expert basketball intelligence class for type checking
            def __init__(self):
                logging.info("🏀 Basketball intelligence: Using Expert GorgonWeave.")

            @oracle_focus
            def reality_anchor(self):
                logging.info("🏀 Basketball intelligence: GorgonWeave reality anchor activated with basketball context.")
                time.sleep(0.001) # Efficient processing for expert system

            @oracle_focus
            def measure_cosmic_energy(self) -> float:
                logging.info(
                    "🏀 Basketball intelligence: GorgonWeave measuring cosmic energy with basketball intelligence."
                )
                # Expert basketball intelligence energy calculation
                base_energy = 0.70  # Solid baseline for reality anchoring
                basketball_context_boost = 0.20  # Boost from basketball intelligence context
                return min(1.0, base_energy + basketball_context_boost)

            @oracle_focus
            def reality_scan(self): # Expert basketball intelligence reality scan
                logging.info("🏀 Basketball intelligence: GorgonWeave reality scan with basketball context.")
                time.sleep(0.001) # Efficient scanning for expert system


# Assign Real classes/functions to the names used in the code, will be mocks if import failed
HeroProphecyEngine = RealHeroProphecyEngine
MetisOracle = RealMetisOracle
ChronosFatigueOracle = RealChronosFatigueOracle
gorgon_weave = real_gorgon_weave
GorgonWeave = RealGorgonWeave # Assign the class name


# Assuming GorgonWeaveReviver is defined elsewhere or defined below
# Defined below in the user's snippet.

# Basketball intelligence CosmicCollapse exception
try:
    from vault_oracle.core.cosmic_exceptions import CosmicCollapse
    logging.info("🏀 Basketball intelligence: CosmicCollapse exception imported successfully")
except ImportError:
    logging.info("🏀 Basketball intelligence: Could not import CosmicCollapse exception. Defining expert basketball intelligence exception.")

    class CosmicCollapse(Exception):
        """Expert basketball intelligence exception for Cosmic Collapse."""

        def __init__(self, message: str = "Basketball intelligence cosmic collapse detected"):
            super().__init__(message)
            self.basketball_context = True


# --- End Mock/Placeholder Dependencies ---


# Configure logging with user's custom format (will be overridden by IchorFlow._setup_logger)
logging.basicConfig(
    level=logging.INFO,
    format="𓃰 %(asctime)s 𓃠 %(levelname)s 𓃱 %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
# Get logger instance (will be configured by IchorFlow)
logger = logging.getLogger("ichor_flow")
# Ensure logger exists for initial messages before IchorFlow is initialized
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    handler.setFormatter(
        logging.Formatter(
            "𓃰 %(asctime)s 𓃠 %(levelname)s 𓃱 %(message)s", datefmt="%Y-%m-%d %H:%M:%S"
        )
    )
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)




# FastAPI App setup
app = FastAPI()

# Prometheus Metrics (using global instances)
# Need to handle potential Prometheus import failure
try:
    # Ensure metrics are registered only once
    # This is better handled in a main application entry point, but for standalone example:
    # Check if registry is default and if metric names exist before creating
    if prom._REGISTRY is prom.REGISTRY: # Check if using the default registry
        # Check if metrics already exist before creating them
        try:
            prom.REGISTRY._names_to_collectors["vault_heartbeat_total"]
            heartbeat_counter = prom.REGISTRY._names_to_collectors[
                "vault_heartbeat_total"
            ]
        except KeyError:
            heartbeat_counter = Counter(
                "vault_heartbeat_total", "Vault Heartbeat Events"
            )

    # Check for IchorFlow metrics (these are class attributes, handled in IchorFlow)
    # _log_counter = prom.Counter('ichor_logs', 'Log entries by type', ['level']) # Defined in IchorFlow
    # _error_counter = prom.Counter('ichor_errors', 'Errors captured', ['type']) # Defined in IchorFlow
    # _vitality_gauge = Gauge('spire_vitality', 'Cognitive spire vitality levels', ['spire']) # Defined in IchorFlow

except AttributeError: # Handle if prom is a DummyMetric
    heartbeat_counter = Counter(
        "vault_heartbeat_total", "Vault Heartbeat Events"
    ) # Dummy metric


@app.get("/vault/power-levels")
def metrics():
    # Need to handle potential Prometheus import failure
    try:
        heartbeat_counter.inc()
        return Response(generate_latest(), media_type="text/plain")
    except AttributeError: # Handle if metrics are DummyMetrics
        return Response(
            "Mock Prometheus Metrics: No prometheus_client installed.\n",
            media_type="text/plain",
        )


@app.get("/")
def root():
    return {"message": " Ichor Flow Operational"}


# Cognitive Spire Integration (Imports handled at the top)


# --- Custom Logging Handler for Encryption and Rotation ---
class EncryptedRotatingFileHandler(logging.FileHandler):
    """
    A custom logging handler that encrypts log records and performs rotation
    when the file size exceeds a configured limit.
    """

    def __init__(
        self,
        filename: Path,
        mode: str = "a",
        encoding: Optional[str] = None,
        delay: bool = False,
        max_bytes: int = 0,
        backup_count: int = 0,
        cipher: Optional[Fernet] = None,
        preserve_ancients: bool = True,
        ichor_flow_instance: "IchorFlow" = None,
    ): # Added ichor_flow_instance
        # Use 'a' mode for appending binary data
        super().__init__(filename, mode="ab", encoding=encoding, delay=delay)
        self.maxBytes = max_bytes
        self.backupCount = backup_count
        self.cipher = cipher
        self.preserve_ancients = preserve_ancients
        self.ichor_flow = ichor_flow_instance # Keep reference to IchorFlow instance
    @oracle_focus # Apply oracle_focus to emit method
    def emit(self, record: logging.LogRecord):
        """
        Emit a record. Encrypts the record before writing to the file.
        """
        try:
            # Format the record first (this includes the message and context)
            msg = self.format(record)
            # Convert formatted message to bytes
            msg_bytes = msg.encode("utf-8")

            # Encrypt the message bytes if cipher is available
            if self.cipher:
                try:
                    encrypted_msg = self.cipher.encrypt(msg_bytes)
                    log_line = encrypted_msg + b"\n" # Add newline character
                except Exception as encryption_error:
                    # Log encryption failure, but still attempt to log the unencrypted message
                    # CRITICAL: Do NOT use self.ichor_flow.capture_error here as it can cause recursion
                    try:
                        # Use a basic emergency logger
                        emergency_logger = logging.getLogger("ichor_emergency")
                        if not emergency_logger.handlers:
                            console_handler = logging.StreamHandler()
                            console_handler.setFormatter(logging.Formatter('EMERGENCY: %(message)s'))
                            emergency_logger.addHandler(console_handler)
                        emergency_logger.propagate = False
                        emergency_logger.error(f" TITAN PROCESSING FAILED: encrypt log record: {encryption_error}")
                    except:
                        pass # Ignore any emergency logging failures
                    log_line = msg_bytes + b"\n" # Log unencrypted if encryption fails
            else:
                # Log unencrypted if cipher is not available
                log_line = msg_bytes + b"\n"

            # Check for rotation *before* writing the current record
            if self.maxBytes > 0:
                # Check if the file exists and get its size
                if self.shouldRotate(record):
                    self.doRollover()

            # Write the log line to the file
            self.stream.write(log_line)
            self.flush() # Ensure data is written immediately

        except Exception as e:
            # Catch any exception during emission
            # CRITICAL: Do NOT use self.ichor_flow.capture_error here as it can create infinite recursion
            # Use direct logging to avoid circular logging calls
            try:
                # Use a basic logger that won't trigger this handler again
                basic_logger = logging.getLogger("ichor_flow_emergency")
                basic_logger.setLevel(logging.CRITICAL)

                # Only add a console handler if none exists to avoid duplicate handlers
                if not basic_logger.handlers:
                    console_handler = logging.StreamHandler()
                    console_handler.setFormatter(logging.Formatter('EMERGENCY: %(message)s'))
                    basic_logger.addHandler(console_handler)
                basic_logger.propagate = False # Prevent propagation to avoid cycles

                basic_logger.critical(f" IchorFlow log emission failed: {e}")
            except Exception as emergency_error:
                # Absolute fallback - print to stderr if even emergency logging fails

    @oracle_focus # Apply oracle_focus to shouldRotate method
    def shouldRotate(self, record: logging.LogRecord) -> bool:
        """
        Determine if rollover should occur.
        """
        # Check if the file exists and its size is greater than maxBytes
        # Use try-except for file operations
        try:
            # First check if stream is None (after failed rotation)
            if self.stream is None:
                return False # Can't rotate if we don't have a valid stream

            if self.stream.tell() >= self.maxBytes:
                return True
        except (OSError, ValueError, AttributeError) as e: # Include AttributeError for None stream
            # Use emergency logging for rotation check failures to avoid recursion
            try:
                emergency_logger = logging.getLogger("ichor_shouldrotate_emergency")
                if not emergency_logger.handlers:
                    console_handler = logging.StreamHandler()
                    console_handler.setFormatter(logging.Formatter('SHOULDROTATE_EMERGENCY: %(message)s'))
                    emergency_logger.addHandler(console_handler)
                emergency_logger.propagate = False
                emergency_logger.error(f"Error checking log file size for rotation: {e}")
            except:
                # Don't rotate if we can't check the size
                return False

        return False

    @oracle_focus # Apply oracle_focus to doRollover method
    def doRollover(self):
        """
        Do a rollover, as described in __init__().
        """
        # Close the current file
        if self.stream:
            self.stream.close()
            self.stream = None # Set stream to None

        # Get the size of the file *before* renaming for the metric
        original_size = 0
        try:
            original_size = os.stat(self.baseFilename).st_size
        except FileNotFoundError:
            logging.warning(
                f"Log file not found during rollover size check: {self.baseFilename}. Size metric will be 0."
            )
        except Exception as e:
            logging.error(
                f" TITAN PROCESSING FAILED: get log file size during rollover: {e}", exc_info=True
            )
            # Continue rollover even if size check fails
        # Determine the rotated file name with timestamp and microseconds to avoid collisions
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S%f") # Add microseconds
        rotated_log_path = Path(self.baseFilename).with_suffix(f".{timestamp}.rotated")

        # Ensure unique filename in case of rapid rotations
        counter = 0
        while rotated_log_path.exists():
            counter += 1
            rotated_log_path = Path(self.baseFilename).with_suffix(f".{timestamp}_{counter}.rotated")

        logger.info(f"Rotating log file to '{rotated_log_path}'...")

        try:
            # Rename the current log file
            os.rename(self.baseFilename, rotated_log_path)
            logger.info(f"Log file rotated to '{rotated_log_path}'.")

            # Increment Prometheus rotation counter and archived size metric
            # Need to handle potential Prometheus import failure and ensure IchorFlow instance is accessible
            # Accessing IchorFlow._log_rotation_counter directly from here is not ideal.
            # Consider passing the IchorFlow instance to the Reviver or using a shared state/event system.
            # For now, accessing the class attribute directly as in the user's snippet, with safety checks.
            if self.ichor_flow and hasattr(self.ichor_flow, "_log_rotation_counter"):
                try:
                    self.ichor_flow._log_rotation_counter.inc()
                except AttributeError:
                    pass # Handle if metric is DummyMetric
            if self.ichor_flow and hasattr(self.ichor_flow, "_archived_log_size"):
                try:
                    self.ichor_flow._archived_log_size.inc(original_size)
                except AttributeError:
                    pass # Handle if metric is DummyMetric

            # Open a new file with the original name
            self.stream = (
                self._open()
            ) # Use the internal _open method to handle encoding etc.

            # Optional: Implement logic to compress or delete old archives based on preserve_ancients config
            # This would involve iterating through files matching the rotation pattern
            # and applying compression or deletion based on age and preserve_ancients flag.
            # For now, just logging a placeholder.
            if not self.preserve_ancients and self.backupCount > 0:
                logger.warning(
                    "Log archival cleanup (compression/deletion) is not implemented yet."
                )
                # Implement cleanup logic here if needed

        except FileNotFoundError:
            logger.warning(
                f"Log file not found during rotation: {self.baseFilename}. Skipping rename."
            )
            # Attempt to open a new file anyway if the old one disappeared
            try:
                self.stream = self._open()
                logging.info(
                    f"Opened new log file after missing old one: {self.baseFilename}."
                )
            except Exception as e:
                logging.critical(
                    f" TITAN PROCESSING FAILED: open new log file after rotation failure: {e}",
                    exc_info=True,
                )
                self.stream = None # Ensure stream is None if opening fails

        except Exception as e:
            # Catch any other exception during rollover
            # CRITICAL: Do NOT use self.ichor_flow.capture_error here as it can cause recursion
            try:
                # Use emergency logging for rollover failures
                emergency_logger = logging.getLogger("ichor_rollover_emergency")
                if not emergency_logger.handlers:
                    console_handler = logging.StreamHandler()
                    console_handler.setFormatter(logging.Formatter('ROLLOVER_EMERGENCY: %(message)s'))
                    emergency_logger.addHandler(console_handler)
                emergency_logger.propagate = False
                emergency_logger.critical(f" Failed during log rotation: {e}")
            except:
                # Attempt to reopen the file in append mode to continue logging if possible
                try:
                    self.stream = self._open()
                    logger.warning(
                        f"Attempted to reopen log file {self.baseFilename} after rotation failure."
                    )
                except Exception as reopen_e:
                    logging.critical(
                        f" TITAN PROCESSING FAILED: reopen log file after rotation failure: {reopen_e}",
                        exc_info=True,
                    )
                    self.stream = None # Ensure stream is None if reopening fails


# --- End Custom Logging Handler ---


class IchorConfig(BaseModel):
    """Sacred configuration for divine logging and spire monitoring"""

    log_path: Path = Field(..., description="Path to ichor storage file")
    # Use SecretStr for encryption key for better security (recommended)
    # Keeping as str for now as per user code, but adding validation for Fernet key format
    encryption_key: str = Field(
        ...,
        # Fernet keys are 32 bytes, Base64 encoded is 44 characters.
        min_length=44,
        max_length=44,
        # Use a more precise pattern for Fernet keys (URL-safe Base64)
        pattern=r"^[A-Za-z0-9\-_]*={0,2}$",
        description="Fernet encryption key (URL-safe Base64)",
    )
    log_level: str = Field(
        "INFO",
        pattern="^(MEDUSA_DEBUG|INFO|WARNING|ERROR|CRITICAL)$",
        description="Minimum logging level",
    )
    max_log_size: int = Field(
        1048576,
        ge=1024,
        le=1073741824,
        description="Maximum size of log file in bytes (min 1KB, max 1GB)",
    ) # Adjusted min/max
    preserve_ancients: bool = Field(
        True, description="Whether to preserve old log files during rotation"
    )
    spire_vitality_threshold: float = Field(
        0.7,
        ge=0.0,
        le=1.0,
        description="Vitality threshold below which revitalization is triggered (0.0 to 1.0)",
    ) # Adjusted ge/le
    monitored_spires: List[str] = Field(
        [
            "HeroProphecyEngine",
            "MetisOracle",
            "GorgonWeave", # Note: User snippet uses 'GorgonWeave' string but 'gorgon_weave' function
        ],
        description="List of cognitive spire names to monitor",
    )
    vitality_check_interval: float = Field(
        60.0, ge=1.0, description="Interval in seconds for checking spire vitality"
    ) # Added interval

    # Use field_validator for Pydantic V2+
    @field_validator("log_path")
    @classmethod
    def check_log_path(cls, v: Path) -> Path:
        # Ensure the parent directory exists
        if not v.parent.exists():
            try:
                v.parent.mkdir(parents=True, exist_ok=True)
                logger.info(f"Created log directory: {v.parent}")
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: create log directory {v.parent}: {e}")
                raise ValueError(
                    f" TITAN PROCESSING FAILED: create log directory {v.parent}: {e}"
                ) from e
        # Check if the path is writable (optional but good practice)
        if v.parent.exists() and not os.access(v.parent, os.W_OK):
            logger.error(f"Log directory {v.parent} is not writable.")
            raise ValueError(f"Log directory {v.parent} is not writable.")

        return v

    # Use field_validator for Pydantic V2+
    @field_validator("encryption_key")
    @classmethod
    @oracle_focus # Keep oracle_focus if it applies to the validation logic itself
    def validate_key_strength(cls, v: str) -> str: # Type hint as str
        # Validate Fernet key format and length (32 bytes / 44 chars Base64)
        try:
            key_bytes = v.encode("utf-8")
            if len(key_bytes) != 44: # Fernet keys are 44 chars in Base64
                raise ValueError(
                    f"Invalid encryption key length: expected 44 characters, got {len(key_bytes)}"
                )
            # Attempt to initialize Fernet to validate the Base64 format
            cipher = Fernet(key_bytes)
            # The original snippet checked for '-' count, which is not a standard Fernet key property.
            # Fernet keys are URL-safe Base64, which uses '-' and '_'.
            # A standard Fernet key is random bytes Base64 encoded. Checking for '-' count is arbitrary.
            # Let's remove the arbitrary '-' check and rely on Fernet initialization validation.
            # If you have a specific "cosmic entropy" requirement, define it clearly.
            # For now, just validate it's a valid Fernet key.
            # if cipher._signing_key.count(b'-') < 3: # Removed this non-standard check

        except (ValueError, InvalidToken) as e: # Catch InvalidToken as well
            logger.error(f"Invalid Fernet encryption key format: {e}")
            raise ValueError(
                f"Invalid encryption key format: {str(e)}"
            ) from e # Chain exception
        return v

    # Use field_validator for Pydantic V2+
    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str: # Type hint as str
        # Convert to uppercase and check if it's a valid level name
        upper_level = v.upper()
        valid_levels = ["MEDUSA_DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if upper_level not in valid_levels:
            raise ValueError(f"Invalid log level: '{v}'. Must be one of {valid_levels}")
        return upper_level # Return uppercase level for consistency


class IchorFlow:
    """Divine logging with cognitive spire vitality monitoring"""

    # Prometheus Metrics (class attributes)
    # Need to handle potential Prometheus import failure
    try:
        _log_counter = prom.Counter("ichor_logs", "Log entries by type", ["level"])
        _error_counter = prom.Counter("ichor_errors", "Errors captured", ["type"])
        _vitality_gauge = Gauge(
            "spire_vitality", "Cognitive spire vitality levels", ["spire"]
        )
        _log_rotation_counter = prom.Counter(
            "ichor_log_rotations_total", "Total log rotations"
        ) # Added metric
        _archived_log_size = prom.Counter(
            "ichor_archived_log_bytes_total", "Total bytes archived during log rotation"
        ) # Added metric
        logger.info(" MEDUSA VAULT: IchorFlow Prometheus metrics initialized.")
    except AttributeError: # Handle if prom is a DummyMetric
        _log_counter = prom.Counter("ichor_logs", "Log entries by type", ["level"])
        _error_counter = prom.Counter("ichor_errors", "Errors captured", ["type"])
        _vitality_gauge = Gauge(
            "spire_vitality", "Cognitive spire vitality levels", ["spire"]
        )
        _log_rotation_counter = prom.Counter(
            "ichor_log_rotations_total", "Total log rotations"
        )
        _archived_log_size = prom.Counter(
            "ichor_archived_log_bytes_total", "Total bytes archived during log rotation"
        )

    @oracle_focus
    def __init__(self, config: IchorConfig):
        """
        Initializes the Ichor Flow logging and monitoring system.

        Args:
            config: An instance of IchorConfig.
        """
        if not isinstance(config, IchorConfig):
            raise TypeError("Config must be an instance of IchorConfig")

        self.config = config
        # Initialize Fernet cipher
        try:
            self.cipher = Fernet(config.encryption_key.encode())
            logger.info(" MEDUSA VAULT: Ichor encryption enabled.")
        except (ValueError, InvalidToken) as e:
            # Log critical error but allow initialization to continue if encryption fails
            # Depending on requirements, you might want to raise a RuntimeError here
            self.capture_error(e, {"type": "encryption_init"}) # Use capture_error
            logger.critical(
                f" TITAN PROCESSING FAILED: initialize Fernet cipher: {e}. Logging will be unencrypted!"
            )
            self.cipher = None # Disable encryption if key is invalid

        # Setup the logger instance using the custom handler
        self.logger = None # Initialize to None
        self._setup_logger()

        # Initialize spire vitality monitoring
        self.active_spires: Dict[str, Any] = {} # Initialize empty dict
        self._init_vitality_monitoring()

        # Placeholder for async vitality check task
        self._vitality_check_task: Optional[asyncio.Task] = None

        logger.info(" MEDUSA VAULT: Ichor Flow system initialized.")

    @oracle_focus
    def _setup_logger(self):
        """Initialize divine logging channels with cosmic alignment."""
        # Get the logger instance
        self.logger = logging.getLogger("ichor_flow")
        # Set the logging level from config (converted to uppercase by validator)
        self.logger.setLevel(self.config.log_level)

        # Define formatter
        formatter = logging.Formatter(
            fmt="𓀀 %(asctime)s 𓁟 %(levelname)s 𓀁 %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # Define handlers
        # Use the custom EncryptedRotatingFileHandler
        main_file_handler = EncryptedRotatingFileHandler(
            filename=self.config.log_path,
            max_bytes=self.config.max_log_size,
            backup_count=0, # No backup count needed with timestamped rotation
            cipher=self.cipher,
            preserve_ancients=self.config.preserve_ancients,
            ichor_flow_instance=self, # Pass reference to self
        )
        main_file_handler.setFormatter(formatter)

        # Stream handler for console output
        stream_handler = logging.StreamHandler(sys.stdout)
        stream_handler.setFormatter(formatter)

        # Special handler for cosmic vitality events (using standard FileHandler for simplicity)
        # The filtering logic needs to be applied to this handler.
        cosmic_handler = self._create_cosmic_handler()
        cosmic_handler.setFormatter(formatter) # Apply formatter to cosmic handler

        handlers_to_add = [main_file_handler, stream_handler, cosmic_handler]

        # Store handlers for later closing
        self._handlers = []

        # Prevent adding duplicate handlers if _setup_logger is called multiple times
        # or if handlers were already configured by basicConfig or previous calls.
        # Get existing handlers for this logger
        existing_handlers = self.logger.handlers[:] # Work on a copy

        for handler_to_add in handlers_to_add:
            is_duplicate = False
            for existing_handler in existing_handlers:
                # Check if handler type and target (filename or stream) are the same
                if type(handler_to_add) == type(existing_handler):
                    if (
                        isinstance(handler_to_add, logging.FileHandler)
                        and handler_to_add.baseFilename == existing_handler.baseFilename
                    ):
                        is_duplicate = True
                        break
                    elif (
                        isinstance(handler_to_add, logging.StreamHandler)
                        and handler_to_add.stream == existing_handler.stream
                    ):
                        is_duplicate = True
                        break
                # Add more checks for other handler types if used

            if not is_duplicate:
                self.logger.addHandler(handler_to_add)
                self._handlers.append(handler_to_add) # Store reference to the handler
            else:
                    f"Skipping adding duplicate handler: {type(handler_to_add).__name__}"
                )

        logger.info(" MEDUSA VAULT: 𓀀 Divine logging channels aligned.")

    @oracle_focus
    def _create_cosmic_handler(self):
        """Special handler for spire vitality events."""
        # Create a separate log file for cosmic events
        cosmic_log_path = self.config.log_path.parent / "cosmic_events.log"
        handler = logging.FileHandler(
            cosmic_log_path, mode="a", encoding="utf-8"
        ) # Use standard FileHandler

        # Add a filter to only include log records where the 'system' key in 'extra.context' is 'vitality'
        class VitalityFilter(logging.Filter):
            def filter(self, record):
                # Check if 'extra' and 'context' are in the record, and if 'system' is 'vitality'
                return (
                    hasattr(record, "context")
                    and isinstance(record.context, dict)
                    and record.context.get("system") == "vitality"
                )

        handler.addFilter(VitalityFilter())
        logger.info(
            f"Created cosmic event handler for '{cosmic_log_path}' with VitalityFilter."
        )
        return handler

    @oracle_focus
    def _init_vitality_monitoring(self):
        """Start background vitality checks and set up Prometheus gauge functions."""
        # Instantiate the cognitive spires based on the configured list
        # Ensure the names in monitored_spires match the class/function names
        spire_map = {
            "HeroProphecyEngine": HeroProphecyEngine,
            "MetisOracle": MetisOracle,
            "GorgonWeave": gorgon_weave, # Use the function name here if it returns an instance
            # Add other spire classes/functions here if needed
        }

        # Instantiate only the monitored spires
        for spire_name in self.config.monitored_spires:
            spire_instance = None
            try:
                # Handle case where spire is a function (like gorgon_weave) vs a class
                spire_factory = spire_map.get(spire_name)
                if spire_factory:
                    # Check if it's a class or a function
                    if isinstance(spire_factory, type): # It's a class
                        spire_instance = spire_factory()
                    elif callable(spire_factory): # It's a function
                        spire_instance = (
                            spire_factory()
                        ) # Call the function to get the instance
                    else:
                        logger.error(
                            f"Spire factory for '{spire_name}' is not a class or callable."
                        )
                        self.capture_error(
                            TypeError(
                                f"Spire factory for '{spire_name}' is not callable"
                            ),
                            {"spire": spire_name, "type": "invalid_factory"},
                        )
                        continue # Skip to the next spire

                else:
                    logger.error(
                        f"Unknown spire name in config: '{spire_name}'. Cannot initialize."
                    )
                    self.capture_error(
                        ValueError(f"Unknown spire '{spire_name}'"),
                        {"spire": spire_name, "type": "unknown_spire"},
                    )
                    continue # Skip to the next spire

                if spire_instance:
                    # Ensure the instance has the measure_cosmic_energy method
                    if not hasattr(spire_instance, "measure_cosmic_energy"):
                        logger.error(
                            f"Spire '{spire_name}' instance is missing 'measure_cosmic_energy' method."
                        )
                        self.capture_error(
                            AttributeError(
                                f"Spire '{spire_name}' missing measure_cosmic_energy"
                            ),
                            {"spire": spire_name, "type": "missing_method"},
                        )
                        continue # Skip if method is missing

                    self.active_spires[spire_name] = spire_instance

                    # Set the Prometheus gauge function for this spire
                    # Use a lambda that captures the spire_name and calls _check_spire_vitality
                    # Need to handle potential Prometheus import failure
                    try:
                        self._vitality_gauge.labels(spire=spire_name).set_function(
                            lambda name=spire_name: self._check_spire_vitality(name)
                        )
                    except AttributeError: # Handle if _vitality_gauge is a DummyMetric
                        logger.warning(
                            f"Skipping setting vitality gauge function for {spire_name} (Prometheus not available)."
                        )

            except Exception as e:
                self.capture_error(e, {"spire": spire_name, "type": "init_failed"})
                logger.critical(
                    f"💥 TITAN PROCESSING FAILED: initialize spire '{spire_name}': {e}", exc_info=True
                )

        logger.info(
            f"Monitoring initialized for spires: {list(self.active_spires.keys())}"
        )

        # Start the background vitality check task (optional, depending on application structure)
        # If running in a long-running process (like FastAPI), a background task is suitable.
        # If running as a short script, this task might not run.
        # Let's add a method to start/stop this task.
        # self._vitality_check_task = asyncio.create_task(self._run_vitality_checks()) # Needs an async loop

    @oracle_focus
    def _check_spire_vitality(self, spire_name: str) -> float:
        """
        Quantum measurement of spire energy flow.
        Called by the Prometheus gauge function.
        """

        spire = self.active_spires.get(spire_name)
        if not spire:
            # This indicates an issue if a gauge function is set for a non-existent spire
            self.log(
                "ERROR",
                f"Attempted to check vitality for unknown spire '{spire_name}'.",
                {"system": "vitality"},
            )
            self.capture_error(
                ValueError(f"Unknown spire '{spire_name}' in vitality check"),
                {"spire": spire_name, "type": "vitality_check_unknown"},
            )
            return 0.0 # Return 0.0 for unknown spires

        try:
            # Assuming spire instances have a measure_cosmic_energy() method
            if hasattr(spire, "measure_cosmic_energy"):
                vitality = spire.measure_cosmic_energy()
                # Ensure vitality is a float
                if not isinstance(vitality, (int, float)):
                    logger.warning(
                        f"Spire '{spire_name}' returned non-numeric vitality: {vitality}. Returning 0.0."
                    )
                    self.capture_error(
                        TypeError(
                            f"Spire '{spire_name}' returned non-numeric vitality"
                        ),
                        {"spire": spire_name, "type": "non_numeric_vitality"},
                    )
                    return 0.0

                # Log vitality level (can be filtered by cosmic_handler)
                # Use the standard logger method with extra context
                    f"Spire vitality reading: {spire_name} ({vitality:.2f})",
                    extra={
                        "context": {
                            "system": "vitality",
                            "spire": spire_name,
                            "level": vitality,
                        }
                    },
                )

                if vitality < self.config.spire_vitality_threshold:
                    self._trigger_revitalization(spire_name, vitality)

                return float(vitality) # Return as float

            else:
                # Spire instance does not have the expected method
                self.log(
                    "ERROR",
                    f"Spire '{spire_name}' instance has no 'measure_cosmic_energy' method.",
                    {"system": "vitality"},
                )
                self.capture_error(
                    AttributeError(
                        f"Spire '{spire_name}' missing measure_cosmic_energy"
                    ),
                    {"spire": spire_name, "type": "missing_method"},
                )
                return 0.0

        except Exception as e:
            # Catch any exception during vitality measurement
            self.capture_error(
                e, {"spire": spire_name, "type": "vitality_measurement_failed"}
            )
            self.log(
                "ERROR",
                f" TITAN PROCESSING FAILED: measure vitality for spire '{spire_name}': {e}",
                {"system": "vitality", "spire": spire_name},
            )
            return 0.0 # Return 0.0 on error

    # Optional async task for periodic vitality checks (if not relying solely on Prometheus scrapes)
    # @oracle_focus
    # async def _run_vitality_checks(self):
    #     """Periodically checks spire vitality levels."""
    #     logger.info(" MEDUSA VAULT: Starting background spire vitality checks.")
    #     while True:
    #         for spire_name in self.active_spires.keys():
    #             # The gauge function already calls _check_spire_vitality,
    #             # so simply triggering a gauge update or waiting for scrape is enough.
    #             # If you need to *actively* check and log *outside* of Prometheus scrapes,
    #             # call _check_spire_vitality here.
    #             # self._check_spire_vitality(spire_name) # Call if needed for independent logging
    #             pass # Relying on Prometheus scrape to trigger checks
    #
    #         await asyncio.sleep(self.config.vitality_check_interval)
    #
    #     logger.info(" MEDUSA VAULT: Background spire vitality checks stopped.")

    @oracle_focus
    def _trigger_revitalization(self, spire_name: str, current_level: float):
        """Initiate cosmic rebirth sequence for a low-vitality spire."""
        logger.warning(
            f"Initiating revitalization for spire '{spire_name}' (current level: {current_level:.2f})."
        )
        # Log the revitalization trigger event using the standard logger method with extra context
        self.logger.warning(
            f"Spire vitality low: {spire_name} ({current_level:.2f}). Triggering revitalization.",
            extra={
                "context": {
                    "system": "vitality",
                    "spire": spire_name,
                    "level": current_level,
                    "action": "revitalization_triggered",
                }
            },
        )

        try:
            # Trigger spire-specific revitalization logic
            if spire_name == "GorgonWeave":
                # Assuming GorgonWeaveReviver exists and has an awaken method
                # Instantiate and call awaken
                GorgonWeaveReviver().awaken()
                logger.info(" MEDUSA VAULT: GorgonWeave reality anchor activated.")
                # Set the GorgonWeave vitality gauge to 1.0 after successful awakening attempt
                # Need to handle potential Prometheus import failure and ensure IchorFlow instance is accessible
                # Accessing IchorFlow._vitality_gauge directly from here is not ideal.
                # Consider passing the IchorFlow instance to the Reviver or using a shared state/event system.
                # For now, accessing the class attribute directly as in the user's snippet, with safety checks.
                if hasattr(IchorFlow, "_vitality_gauge"):
                    try:
                        IchorFlow._vitality_gauge.labels(spire="GorgonWeave").set(1.0)
                    except AttributeError:
                        pass # Handle if metric is DummyMetric
                else:
                    logger.warning(
                        "IchorFlow._vitality_gauge not available to update after GorgonWeave revitalization."
                    )

            elif spire_name == "HeroProphecyEngine":
                # Example: Call a revitalization method on the spire instance itself
                spire = self.active_spires.get(spire_name)
                if hasattr(spire, "revitalize"):
                    # Assuming revitalize is synchronous or you handle async calls here
                    spire.revitalize()
                    logger.info(
                        f"Revitalization triggered for {spire_name} via spire method."
                    )
                else:
                    logger.warning(
                        f"No specific revitalization method found for spire '{spire_name}'."
                    )
                    self.capture_error(
                        AttributeError(
                            f"Spire '{spire_name}' missing revitalize method"
                        ),
                        {"spire": spire_name, "type": "missing_revitalize"},
                    )

            # Add other spire-specific revitalization logic here

        except Exception as e:
            self.capture_error(
                e, {"spire": spire_name, "type": "revitalization_failed"}
            )
            logger.error(
                f" TITAN PROCESSING FAILED: trigger revitalization for spire '{spire_name}': {e}",
                exc_info=True,
            )

    # Re-implementing `log` to use standard logging levels and handlers
    @oracle_focus
    def log(self, level: str, message: str, context: Optional[Dict] = None):
        """
        Divine logging method using standard logging levels and handlers.

        Args:
            level: The logging level (e.g., "MEDUSA_DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL").
            message: The log message.
            context: Optional dictionary of cosmic context.
        """
        # Validate and sanitize cosmic context
        cosmic_context = self._sanitize_cosmic_context(context)

        # Map string level to numeric level
        log_level_map = {
            "MEDUSA_DEBUG": logging.MEDUSA_DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL,
        }
        numeric_level = log_level_map.get(
            level.upper(), logging.INFO
        ) # Default to INFO if level is invalid

        # Log the message using the standard logger instance with extra context
        # The EncryptedRotatingFileHandler and cosmic_handler will process this record.
        self.logger.log(numeric_level, message, extra={"context": cosmic_context})

        # Increment Prometheus counter for log levels
        # Need to handle potential Prometheus import failure
        try:
            self._log_counter.labels(level=level.upper()).inc()
        except AttributeError:
            pass

    @oracle_focus
    def capture_error(self, error: Exception, context: Optional[Dict] = None):
        """
        Captures an error, logs it using a recursion-safe logger, and increments the error counter.

        Args:
            error: The exception object.
            context: Optional dictionary of cosmic context related to the error.
        """
        # CRITICAL: Use recursion-safe logging to prevent infinite loops
        # Check if this error is from the logging system itself
        is_logging_error = context and context.get("type") in [
            "log_emission_failed", "log_rotation_failed", "log_encryption_failed"
        ]

        if is_logging_error:
            # For logging system errors, use emergency logging only
            try:
                emergency_logger = logging.getLogger("ichor_error_emergency")
                if not emergency_logger.handlers:
                    console_handler = logging.StreamHandler()
                    console_handler.setFormatter(logging.Formatter('ERROR_EMERGENCY: %(message)s'))
                    emergency_logger.addHandler(console_handler)
                emergency_logger.propagate = False
                emergency_logger.error(f"Logging system error: {type(error).__name__} - {error}")
            except:
                pass  # Emergency logging failed, continue silently
        else:
            # For non-logging errors, use normal logging but with a recursion guard
            try:
                if not hasattr(self, '_in_capture_error'):
                    self._in_capture_error = True
                    try:
                        self.log("ERROR", f"Error captured: {type(error).__name__} - {error}", context)
                    finally:
                        del self._in_capture_error
                else:
                    # We're already in capture_error, use emergency logging
                    emergency_logger = logging.getLogger("ichor_recursive_emergency")
                    if not emergency_logger.handlers:
                        console_handler = logging.StreamHandler()
                        console_handler.setFormatter(logging.Formatter('RECURSIVE_EMERGENCY: %(message)s'))
                        emergency_logger.addHandler(console_handler)
                    emergency_logger.propagate = False
                    emergency_logger.error(f"Recursive error: {type(error).__name__} - {error}")
            except Exception as log_error:
                # Fallback to emergency logging if standard logging fails
                try:
                    emergency_logger = logging.getLogger("ichor_capture_emergency")
                    if not emergency_logger.handlers:
                        console_handler = logging.StreamHandler()
                        console_handler.setFormatter(logging.Formatter('CAPTURE_EMERGENCY: %(message)s'))
                        emergency_logger.addHandler(console_handler)
                    emergency_logger.propagate = False
                    emergency_logger.error(f" TITAN PROCESSING FAILED: capture error {type(error).__name__}: {log_error}")
                except:
                    pass  # Emergency logging failed, continue silently

        # Increment Prometheus error counter (safe operation)
        try:
            error_type = context.get("type", "unknown") if context else "unknown"
            self._error_counter.labels(type=error_type).inc()
        except AttributeError:
            pass # Ignore if metrics are not available

    @oracle_focus
    def _sanitize_cosmic_context(self, context: Optional[Dict]) -> Dict:
        """Ensure context doesn't contain forbidden knowledge (sensitive keys)."""
        forbidden_keys = [
            "encryption_key",
            "signing_key",
            "quantum_seed",
            "FIREBASE_KEY",
        ] # Added FIREBASE_KEY
        # Create a new dictionary excluding forbidden keys
        sanitized_context = {}
        if context:
            for k, v in context.items():
                if k not in forbidden_keys:
                    # Recursively sanitize nested dictionaries if needed
                    # For simplicity, only sanitizing top-level keys for now
                    # If nested sanitization is required, implement a recursive function.
                    sanitized_context[k] = v
                else:
                    logger.warning(f"Removed forbidden key '{k}' from log context.")

        return sanitized_context

    # Removed _check_and_rotate_logs and _rotate_logs as this logic is now handled by EncryptedRotatingFileHandler

    @oracle_focus
    def current_vitality_readings(self) -> Dict[str, float]:
        """
        Retrieves the current vitality readings for monitored spires.
        This method is for external use, calling the internal check method.
        """
        readings = {}
        for spire_name in self.config.monitored_spires:
            # Call the internal check method directly
            readings[spire_name] = self._check_spire_vitality(spire_name)
        return readings

    @oracle_focus
    def trigger_full_system_scan(self):
        """
        *** NOTE: This is a PLACEHOLDER. Replace with actual system scan logic. ***
        Initiates a full system scan or diagnostic protocol.
        Called in the example usage on CosmicCollapse.
        """
        logger.warning(" TITAN WARNING: Using placeholder trigger_full_system_scan. Simulating scan.")
        self.log(
            "CRITICAL",
            "Full system scan initiated due to cosmic anomaly.",
            {"system": "scan", "status": "initiated"},
        )
        # Simulate scan process
        time.sleep(0.5) # Simulate work
        self.log(
            "INFO",
            "Full system scan complete (simulated).",
            {"system": "scan", "status": "complete"},
        )

    @oracle_focus
    async def initiate_flow(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standard ichor flow processing for divine data transformation.

        Args:
            data: Raw data to process through ichor flow

        Returns:
            Processed data with ichor enhancements
        """
        try:
            self.log("MEDUSA_DEBUG", "Initiating standard ichor flow processing", {
                "input_keys": list(data.keys()),
                "flow_type": "standard"
            })

            # Check spire vitality before processing
            vitality_readings = self.current_vitality_readings()
            low_vitality_spires = [
                spire for spire, vitality in vitality_readings.items()
                if vitality < self.config.spire_vitality_threshold
            ]

            if low_vitality_spires:
                self.log("WARNING", f"Processing with low vitality spires: {low_vitality_spires}")

            # Process the data through ichor flow
            processed_data = data.copy()

            # Add standard ichor metadata
            processed_data["ichor_metadata"] = {
                "flow_type": "standard",
                "timestamp": datetime.utcnow().isoformat(),
                "vitality_status": "optimal" if not low_vitality_spires else "degraded",
                "spire_vitality": vitality_readings,
                "processing_status": "complete"
            }

            self.log("MEDUSA_DEBUG", "Standard ichor flow ORACLE PROCESSING: Complete", {
                "processed_keys": list(processed_data.keys()),
                "vitality_status": "optimal" if not low_vitality_spires else "degraded"
            })

            return processed_data

        except Exception as e:
            self.capture_error(e, {"type": "ichor_flow_processing_failed", "flow_type": "standard"})
            self.log("ERROR", f" TITAN PROCESSING FAILED: process ichor flow: {e}")
            # Return original data with error metadata
            return {
                **data,
                "ichor_metadata": {
                    "flow_type": "standard",
                    "timestamp": datetime.utcnow().isoformat(),
                    "processing_status": "error",
                    "error": str(e)
                }
            }

    @oracle_focus
    async def initiate_expert_flow(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Expert-level ichor flow processing with enhanced cosmic integration.

        Args:
            data: Raw data to process through expert ichor flow

        Returns:
            Expert-processed data with advanced ichor enhancements
        """
        try:
            self.log("MEDUSA_DEBUG", "Initiating expert ichor flow processing", {
                "input_keys": list(data.keys()),
                "flow_type": "expert"
            })

            # Check spire vitality before processing
            vitality_readings = self.current_vitality_readings()
            low_vitality_spires = [
                spire for spire, vitality in vitality_readings.items()
                if vitality < self.config.spire_vitality_threshold
            ]

            if low_vitality_spires:
                self.log("WARNING", f"Expert processing with low vitality spires: {low_vitality_spires}")
                # Trigger revitalization for low vitality spires
                for spire in low_vitality_spires:
                    self._trigger_revitalization(spire, vitality_readings[spire])

            # Process the data through expert ichor flow
            processed_data = data.copy()

            # Add expert-level data enhancements
            if "predictions" in processed_data:
                processed_data["expert_predictions"] = {
                    "confidence_boost": 0.15,
                    "quantum_enhanced": True,
                    "spire_validated": True
                }

            # Add expert ichor metadata
            processed_data["expert_metadata"] = {
                "flow_type": "expert",
                "timestamp": datetime.utcnow().isoformat(),
                "vitality_status": "optimal" if not low_vitality_spires else "recovering",
                "spire_vitality": vitality_readings,
                "expert_features_applied": True,
                "revitalization_triggered": bool(low_vitality_spires),
                "processing_status": "complete"
            }

            self.log("MEDUSA_DEBUG", "Expert ichor flow ORACLE PROCESSING: Complete", {
                "processed_keys": list(processed_data.keys()),
                "vitality_status": "optimal" if not low_vitality_spires else "recovering",
                "expert_features_applied": True
            })

            return processed_data

        except Exception as e:
            self.capture_error(e, {"type": "expert_flow_processing_failed", "flow_type": "expert"})
            self.log("ERROR", f" TITAN PROCESSING FAILED: process expert ichor flow: {e}")
            # Return original data with error metadata
            return {
                **data,
                "expert_metadata": {
                    "flow_type": "expert",
                    "timestamp": datetime.utcnow().isoformat(),
                    "processing_status": "error",
                    "error": str(e)
                }
            }

# Define GorgonWeaveReviver class as provided
class GorgonWeaveReviver:
    """Reality stabilization subsystem"""

    @oracle_focus
    def awaken(self):
        """Activate petrification defenses."""
        logger.warning(
            "Activating GorgonWeave Reviver: Initiating petrification defenses."
        )
        try:
            # Assuming GorgonWeave class exists and has a reality_anchor method
            # Need to ensure GorgonWeave is imported or mocked correctly
            # Use the class name directly if it's a class
            GorgonWeave().reality_anchor()
            logger.info(" MEDUSA VAULT: GorgonWeave reality anchor activated.")
            # Set the GorgonWeave vitality gauge to 1.0 after successful awakening attempt
            # Need to handle potential Prometheus import failure and ensure IchorFlow instance is accessible
            # Accessing IchorFlow._vitality_gauge directly from here is not ideal.
            # Consider passing the IchorFlow instance to the Reviver or using a shared state/event system.
            # For now, accessing the class attribute directly as in the user's snippet, with safety checks.
            if hasattr(IchorFlow, "_vitality_gauge"):
                try:
                    IchorFlow._vitality_gauge.labels(spire="GorgonWeave").set(1.0)
                except AttributeError:
                    pass # Handle if metric is DummyMetric
            else:
                logger.warning(
                    "IchorFlow._vitality_gauge not available to update after GorgonWeave revitalization."
                )

        except Exception as e:
            # Log the failure to awaken GorgonWeave
            logging.error(f"Gorgon awakening failed: {str(e)}", exc_info=True)
            # Optional: Capture this error using IchorFlow's capture_error if an instance is accessible
            # if 'ichor' in locals() and hasattr(ichor, 'capture_error'): # Example check if ichor is in scope
            # ichor.capture_error(e, {'spire': 'GorgonWeave', 'type': 'revival_failed'})


# Enhanced Example Usage
if __name__ == "__main__":
    # Configure basic logging for standalone run (redundant after basicConfig above, but safe)
    logging.basicConfig(
        level=logging.MEDUSA_DEBUG, # Use MEDUSA_DEBUG for more verbose example output
        format="𓃰 %(asctime)s 𓃠 %(levelname)s 𓃱 %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    logger = logging.getLogger("ichor_flow") # Re-get logger after basicConfig

    # --- Example Setup: Create dummy log directory and set environment variable ---
    DUMMY_LOG_DIR = Path("./mock_ichor_logs")
    DUMMY_LOG_DIR.mkdir(parents=True, exist_ok=True)
    DUMMY_LOG_PATH = DUMMY_LOG_DIR / "cosmic.log"
    DUMMY_ICHOR_KEY = Fernet.generate_key().decode() # Generate a random key

    # Set the environment variable for the encryption key
    os.environ["ICHOR_KEY"] = DUMMY_ICHOR_KEY
    logger.info(" MEDUSA VAULT: ICHOR_KEY environment variable set.")

    # --- Mock divine_scope context manager for example usage ---
    # This is a placeholder to make the example runnable.
    @contextlib.contextmanager
    def mock_divine_scope(ichor_instance):
        try:
            yield # Yield control to the block within the 'with' statement
        except Exception as e:
            logger.error(f"Exception in mock_divine_scope: {e}")
            raise
        finally:

    # --- Mock CosmicCollapse for example usage ---
    # Defined at the top if not imported.

    async def run_ichor_flow_example():

        # Define Ichor configuration using the dummy file path and env var
        ichor_config_data = {
            "log_path": DUMMY_LOG_PATH,
            "encryption_key": os.getenv("ICHOR_KEY"), # Read from environment variable
            "log_level": "MEDUSA_DEBUG", # Set a verbose level for the example
            "max_log_size": 5000, # Small size to trigger rotation quickly
            "preserve_ancients": True,
            "spire_vitality_threshold": 0.75, # Example threshold
            "monitored_spires": ["HeroProphecyEngine", "MetisOracle", "GorgonWeave"],
            "vitality_check_interval": 5.0, # Check vitality every 5 seconds (mock)
        }

        ichor_instance = None # Initialize to None

        try:
            # Instantiate IchorConfig (Pydantic validates)
            config = IchorConfig(**ichor_config_data)

            # Instantiate IchorFlow
            ichor_instance = IchorFlow(config)

            # Assign the mock divine_scope to the instance for the example
            ichor_instance.divine_scope = mock_divine_scope.__get__(
                ichor_instance, IchorFlow
            )

            # Assign the mock trigger_full_system_scan to the instance for the example
            ichor_instance.trigger_full_system_scan = (
                ichor_instance.trigger_full_system_scan
            ) # Use the placeholder method

            # --- Example: Log some messages and check vitality ---

            # Use the mock divine scope context manager
            with ichor_instance.divine_scope(ichor_instance): # Pass ichor_instance to mock_divine_scope
                ichor_instance.log(
                    "INFO",
                    "Initiating cosmic alignment",
                    {"participants": ["MetisOracle", "HeroProphecyEngine"]},
                )

                # Simulate spire activation (using mocks)
                if "HeroProphecyEngine" in ichor_instance.active_spires and hasattr(
                    ichor_instance.active_spires["HeroProphecyEngine"], "activate"
                ):
                    ichor_instance.active_spires["HeroProphecyEngine"].activate()
                if "GorgonWeave" in ichor_instance.active_spires and hasattr(
                    ichor_instance.active_spires["GorgonWeave"], "reality_scan"
                ):
                    ichor_instance.active_spires["GorgonWeave"].reality_scan()

                ichor_instance.log(
                    "MEDUSA_DEBUG",
                    "Alignment complete",
                    {"energy_levels": ichor_instance.current_vitality_readings()},
                ) # Calls _check_spire_vitality internally

                # Log more messages to trigger rotation
                for i in range(200): # Log enough messages to exceed max_log_size
                    ichor_instance.log(
                        "INFO", f"Cosmic pulse {i+1}", {"sequence": i + 1}
                    )
                    await asyncio.sleep(0.001) # Small delay

                ichor_instance.log("INFO", "Log rotation should have occurred by now.")

                # Check vitality again after some logging
                ichor_instance.log(
                    "INFO",
                    "Checking vitality after rotation attempt.",
                    {
                        "energy_levels_post_rotation": ichor_instance.current_vitality_readings()
                    },
                )

            # --- Example: Simulate low vitality and trigger revitalization ---
            # Temporarily modify a spire's measure_cosmic_energy to return a low value
            original_measure_metis = ichor_instance.active_spires[
                "MetisOracle"
            ].measure_cosmic_energy

            def low_vitality_measure():
                logger.warning(" TITAN WARNING: Mock: Forcing low MetisOracle vitality.")
                return 0.6 # Below the threshold (0.75 in config)

            ichor_instance.active_spires["MetisOracle"].measure_cosmic_energy = (
                low_vitality_measure.__get__(
                    ichor_instance.active_spires["MetisOracle"], MetisOracle
                )
            ) # Bind method

            metis_vitality = ichor_instance._check_spire_vitality("MetisOracle")
            # The check_spire_vitality call should trigger _trigger_revitalization if below threshold

            # Restore original method
            ichor_instance.active_spires["MetisOracle"].measure_cosmic_energy = (
                original_measure_metis
            )

            # --- Example: Simulate Cosmic Collapse and trigger system scan ---
            try:
                # Simulate an event that raises CosmicCollapse
                logger.warning(" TITAN WARNING: Simulating an event that raises CosmicCollapse...")
                raise CosmicCollapse("Temporal paradox detected")
            except CosmicCollapse as e:
                # The example usage calls trigger_full_system_scan here
                ichor_instance.log(
                    "CRITICAL",
                    "Reality fracture detected",
                    {
                        "emergency_protocols": "activated",
                        "error_type": "CosmicCollapse",
                    },
                )
                ichor_instance.trigger_full_system_scan() # Calls the placeholder method
            except Exception as e:
                logger.error(
                    f" Caught unexpected error during Cosmic Collapse simulation: {type(e).__name__}: {e}"
                )

            # Note: Running the FastAPI app requires a separate process or framework like uvicorn.
            # The example here focuses on the IchorFlow class functionality.

        except (ValidationError, ValueError) as e:
            logger.error(f"Configuration validation error: {e}")
        except RuntimeError as e:
            logger.error(f"Runtime error during example execution: {e}")
        except Exception as e:
            logger.error(
                f"\n An unexpected error occurred during example execution: {type(e).__name__}: {e}")
            traceback.print_exc() # Print full traceback for unexpected errors
        finally:
            # Stop the background vitality check task if it was started
            # if ichor_instance and ichor_instance._vitality_check_task:
            #     ichor_instance._vitality_check_task.cancel()
            #     try:
            #         await ichor_instance._vitality_check_task
            #     except asyncio.CancelledError:
            #         logger.info(" MEDUSA VAULT: Vitality check task cancelled.")

            # Close all handlers associated with the ichor_flow logger
            def close_handlers(logger_to_close):
                for handler in logger_to_close.handlers[:]: # Iterate over a copy
                    try:
                        handler.close()
                        logger_to_close.removeHandler(handler)
                            f"Closed and removed logger handler: {type(handler).__name__}"
                        )
                    except Exception as e:
                        logger.warning(f"Failed to close handler: {e}")

            if logger:
                close_handlers(logger)

            # Clean up dummy log directory and environment variable
            if DUMMY_LOG_DIR.exists():
                try:
                    # Remove files first
                    for item in DUMMY_LOG_DIR.iterdir():
                        if item.is_file():
                            item.unlink()
                    # Then remove the directory if empty
                    if not list(DUMMY_LOG_DIR.iterdir()):
                        DUMMY_LOG_DIR.rmdir()
                        logger.info(f"Removed dummy log directory: {DUMMY_LOG_DIR}")
                    else:
                        logger.warning(
                            f"Dummy log directory {DUMMY_LOG_DIR} not empty, skipping removal."
                        )
                except Exception as e:
                    logger.error(f" TITAN PROCESSING FAILED: clean up dummy log directory: {e}")

            if "ICHOR_KEY" in os.environ:
                del os.environ["ICHOR_KEY"]
                logger.info(" MEDUSA VAULT: Unset ICHOR_KEY environment variable.")


    # Run the async example function
    asyncio.run(run_ichor_flow_example())
