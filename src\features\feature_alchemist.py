import pandas as pd
import numpy as np
import logging
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any, List
from functools import lru_cache
import sys
import os
from vault_oracle.core.data_processing_utils import OracleDataProcessor
from vault_oracle.core.oracle_focus import oracle_focus
from nba_api.stats.static import teams
from nba_api.stats.endpoints import TeamDashboardByGeneralSplits
from vault_oracle.core.TeamOlympianAnalytics import TeamOlympianAnalytics
from src.weavers.SynergyWeaver_Expert import ExpertSynergyWeaver as SynergyWeaver
from src.features.feature_feedback import FeatureFeedback, FeatureFeedbackCollector
from vault_oracle.core.QuantumEntangler import ProphecyFeatureForge
from vault_oracle.wells.oracle_wells.simulation.MoiraiSimulacrum import ExpertMoiraiSimulacrum
from src.models.revolutionary_prediction_system import AutoMLFeatureDiscovery
from sklearn.preprocessing import PolynomialFeatures
from sklearn.feature_selection import SelectKBest, f_regression
# Lazy imports to avoid circular dependencies - imported when needed
# from src.cognitive_basketball_cortex.cognitive_spires_manager import CognitiveSpiresManager
# from src.cognitive_spires.NikeVictoryOracle_Expert import NikeVictoryOracle_Expert
# from src.cognitive_spires.AthenaStrategyEngine_Expert import AthenaStrategyEngine_Expert
import asyncio
from datetime import datetime
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression
from sklearn.preprocessing import StandardScaler
import traceback

# src/features/feature_alchemist.py

"""
FEATURE_ALCHEMIST.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Core feature generation pipeline for the Hyper Medusa Neural Vault.
Transforms raw data into battle-ready features for the models.
This file consolidates feature engineering logic.
"""


# Configure logger early
logger = logging.getLogger(__name__)
if not logger.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="𓄿 %(asctime)s 𓃬 %(levelname)s 𓄢 %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger = logging.getLogger(__name__) # Re-get logger after basicConfig
    logger.info(" MEDUSA VAULT: Feature Alchemist logger initialized.")

# --- PRODUCTION IMPORTS ---
# These modules are assumed to be fully implemented and available in the system.
try:
    from vault_oracle.core.data_processing_utils import (
        clean_and_normalize,
        reduce_memory_usage,
        validate_input_schema,
    )
    logger.info("🏀 MEDUSA VAULT: All production dependencies for Feature Alchemist imported successfully.")
except ImportError as e:
    logger.critical(f"FATAL: Could not import production dependencies for Feature Alchemist: {e}. "
                    "Ensure all modules (data_processing_utils, oracle_focus, nba_api, TeamOlympianAnalytics, SynergyWeaver) are correctly installed and configured.")
    raise  # Re-raise the exception as these are critical dependencies now


# ====================
# ⚙️ FEATURE CONSTRUCTION COMPONENTS
# (Consolidated/Refined)
# ====================


# Cache team ID mappings to handle NBA API inconsistencies
@lru_cache(maxsize=None)
def get_valid_team_ids() -> Dict[int, str]:
    """
    Maps NBA API team IDs to their abbreviations using lru_cache for efficiency.
    """
    try:
        # Rely on the actual 'teams' module from nba_api
        team_id_map = {t["id"]: t["abbreviation"] for t in teams.get_teams()}
        return team_id_map
    except Exception as e:
        logger.error(
            f" Error fetching valid team IDs from NBA API: {e}", exc_info=True
        )
        return {} # Return empty dict on error


@oracle_focus # Applying the real decorator
def enhance_with_olympian_insights(df: pd.DataFrame) -> pd.DataFrame:
    """
    Infuses player features with team-level mythological analytics.

    Requires TeamOlympianAnalytics and SynergyWeaver to be correctly imported
    and functional.

    Args:
        df: DataFrame containing player data with 'mythic_roster_id' and 'hero_id'.

    Returns:
        DataFrame with added team-level insight features.
    """
    logger.info(" MEDUSA VAULT: Enhancing features with Olympian insights.")
    
    try:
        # Instantiate the real classes
        team_oracle = TeamOlympianAnalytics()
        synergy_weaver = SynergyWeaver()
    except Exception as e:
        logger.error(f"Failed to instantiate TeamOlympianAnalytics or SynergyWeaver: {e}. Skipping Olympian insights.", exc_info=True)
        return df # Return original df if instantiation fails

    # Ensure 'mythic_roster_id' column exists
    if "mythic_roster_id" not in df.columns:
        logger.error(" MEDUSA ERROR: Input DataFrame missing 'mythic_roster_id' column for Olympian insights.")
        return df # Return original df

    # Convert NBA API team IDs to standard format using cached mapping
    team_map = get_valid_team_ids()
    if team_map:
        # Ensure that the 'mythic_roster_id' column is of a type that can be mapped (e.g., int, float, str)
        # Handle potential NaNs in mapping by filling them with a default 'UNK' or the original value
        df['mythic_roster_id'] = df['mythic_roster_id'].astype(str).map(team_map).fillna(df['mythic_roster_id'].astype(str))
    else:
        logger.warning(" TITAN WARNING: Team ID mapping failed or team_map is empty. Proceeding without mapping.")
        # If mapping fails, 'mythic_roster_id' might still be NBA API IDs.
        # For this function, assume it now uses abbreviations if mapping was intended.

    # Filter teams with at least 5 players for lineup analysis (basic check)
    if "hero_id" in df.columns:
        # Ensure df is not empty before grouping
        if not df.empty:
            valid_teams = df.groupby("mythic_roster_id").filter(lambda x: len(x) >= 5)["mythic_roster_id"].unique()
        else:
            valid_teams = np.array([]) # No valid teams if df is empty
    else:
        logger.warning("Input DataFrame missing 'hero_id' column. Cannot filter valid teams based on player count.")
        valid_teams = df["mythic_roster_id"].unique() # Use all unique teams if hero_id is missing

    team_insights: Dict[str, Dict[str, Any]] = {}
    for mythic_roster_id in valid_teams:
        try:
            essence_raw = team_oracle.get_team_essence(str(mythic_roster_id)) # Ensure team_id is str
            
            # Get players for synergy calculation - take up to 5 unique players from the team in the df
            players_for_synergy = (
                df[df["mythic_roster_id"] == mythic_roster_id]["hero_id"].unique().tolist()[:5]
            )

            synergy_raw = synergy_weaver.calculate_lineup_synergy(players_for_synergy)

            # Safely extract values from essence_raw and synergy_raw
            elemental_profile = essence_raw.get("elemental_profile", {})
            war_council_score = essence_raw.get("war_council_report", {}).get("clash_prophecy")
            synergy_nexus = synergy_raw.get("synergy_signature")
            primary_element = essence_raw.get("primary_element") # Capture primary element from essence

            # Add API alignment check
            api_alignment_data = check_nba_api_alignment(str(mythic_roster_id)) # Ensure team_id is str

            team_insights[str(mythic_roster_id)] = { # Ensure key is string
                **elemental_profile,
                "war_council_score": war_council_score,
                "synergy_nexus": synergy_nexus,
                "api_alignment_status": api_alignment_data.get("status"), # Extract status or full data
                "api_alignment_details": api_alignment_data, # Store full details if needed
                "primary_element": primary_element, # Store primary element
            }

        except Exception as e:
            logger.error(
                f" Team insight generation failed for {mythic_roster_id}: {e}", exc_info=True
            )
            # Add a placeholder entry in insights for teams that failed
            team_insights[str(mythic_roster_id)] = {"error": str(e), "status": "failed_insight_generation"}
            continue # Continue to the next team

    # Merge team insights back into the main DataFrame
    if team_insights:
        try:
            team_insights_df = (
                pd.DataFrame.from_dict(team_insights, orient="index")
                .reset_index()
                .rename(columns={"index": "mythic_roster_id"})
            )
            # Ensure 'mythic_roster_id' column in team_insights_df is of the same type as in df
            # This is critical for successful merge.
            # Assuming 'mythic_roster_id' in df is string due to map().astype(str)
            team_insights_df['mythic_roster_id'] = team_insights_df['mythic_roster_id'].astype(df['mythic_roster_id'].dtype)

            merged_df = pd.merge(
                df,
                team_insights_df,
                on="mythic_roster_id",
                how="left",
            )
            logger.info(" MEDUSA VAULT: Merged team insights into DataFrame.")
            return merged_df
        except Exception as e:
            logger.error(
                f" Error merging team insights DataFrame: {e}", exc_info=True
            )
            return df # Return original df on merge error
    else:
        logger.warning(" TITAN WARNING: No valid team insights generated or processed for merging.")
        return df # Return original df if no insights generated


# ====================
# NBA API ALIGNMENT CHECKS (candidates for relocation)
# ====================
# These functions interact with external NBA API and TeamOlympianAnalytics.
# They are kept here as they are used by enhance_with_olympian_insights,
# but conceptually, some of their functionality (especially direct API calls)
# might fit better in a dedicated data ingestion or validation layer.

@oracle_focus # Applying the real decorator
def check_nba_api_alignment(mythic_roster_id_abbr: str) -> Dict[str, Any]:
    """
    Verifies data matches NBA API reality anchors for a given team abbreviation.

    Requires nba_api and TeamOlympianAnalytics to be correctly imported.

    Args:
        mythic_roster_id_abbr: The abbreviation of the team (e.g., 'LAL').

    Returns:
        Dictionary with alignment check results.
    """
    logger.info(f"Checking NBA API alignment for team {mythic_roster_id_abbr}.")
    
    team_oracle = TeamOlympianAnalytics() # Instantiate the real class

    try:
        # Get NBA API ID from abbreviation
        reverse_team_map = {v: k for k, v in get_valid_team_ids().items()}
        nba_api_team_id = reverse_team_map.get(mythic_roster_id_abbr)

        if nba_api_team_id is None:
            logger.warning(
                f"Could not find NBA API ID for team abbreviation: {mythic_roster_id_abbr}. Skipping API alignment check."
            )
            return {
                "status": "skipped",
                "reason": "NBA API ID not found for abbreviation",
            }

        # Fetch raw data from NBA API using the real TeamDashboardByGeneralSplits
        raw_data_list = TeamDashboardByGeneralSplits(
            team_id=nba_api_team_id
        ).get_data_frames() # Use team_id argument name
        
        if not raw_data_list:
            logger.warning(
                f"No data returned from NBA API for team ID {nba_api_team_id} ({mythic_roster_id_abbr}). Skipping API alignment check."
            )
            return {"status": "skipped", "reason": "No data from NBA API"}

        raw_data = raw_data_list[0] # Assuming the first DataFrame is the relevant one

        # Ensure required columns exist in API data before using them
        required_api_cols = ["PACE", "OFF_RATING", "DEF_RATING"] # Removed "mythic_roster_id" as it's not always in TeamDashboard output
        if not all(col in raw_data.columns for col in required_api_cols):
            logger.error(
                f"NBA API data missing required columns for alignment check: {list(set(required_api_cols) - set(raw_data.columns))}"
            )
            return {"status": "failed", "reason": "NBA API data missing columns"}

        # Ensure elemental_analysis method exists and returns expected structure
        elemental_analysis_result = team_oracle.elemental_analysis(
            mythic_roster_id_abbr
        ) # Use abbreviation here
        if (
            not isinstance(elemental_analysis_result, dict)
            or "elemental_balance" not in elemental_analysis_result
            or not isinstance(elemental_analysis_result["elemental_balance"], dict)
        ):
            logger.error(
                f"TeamOlympianAnalytics.elemental_analysis returned unexpected format for {mythic_roster_id_abbr}."
            )
            return {
                "status": "failed",
                "reason": "Elemental analysis returned unexpected format",
            }

        elemental_balance = elemental_analysis_result["elemental_balance"]

        # Calculate pace difference
        pace_diff = abs(
            raw_data["PACE"].values[0] - elemental_balance.get("air", 0.0)
        )

        # Validate team ratings
        rating_validation_results = validate_team_ratings(raw_data, elemental_balance)

        return {
            "status": "success",
            "pace_diff": float(pace_diff),
            "rating_validation": rating_validation_results,
        }

    except Exception as e:
        logger.error(f" API alignment check failed for {mythic_roster_id_abbr}: {e}", exc_info=True)
        return {"status": "failed", "error": str(e)}


@oracle_focus # Applying the real decorator
def validate_team_ratings(
    api_data: pd.DataFrame, elemental_balance: Dict[str, float]
) -> Dict[str, Any]:
    """
    Ensures our metrics match NBA API fundamentals for team ratings.

    Requires numpy to be correctly imported.

    Args:
        api_data: DataFrame from NBA API TeamDashboardByGeneralSplits.
        elemental_balance: Dictionary of elemental balance scores from TeamOlympianAnalytics.

    Returns:
        Dictionary with rating validation results.
    """
    logger.info(" MEDUSA VAULT: Validating team ratings against NBA API.")
    # Ensure required columns exist in api_data
    required_api_cols = ["OFF_RATING", "DEF_RATING"]
    if not all(col in api_data.columns for col in required_api_cols):
        logger.error(
            f"NBA API data missing required columns for rating validation: {list(set(required_api_cols) - set(api_data.columns))}"
        )
        return {"status": "failed", "reason": "NBA API data missing columns"}

    # Ensure required elements exist in elemental_balance
    required_elements = [
        "fire",
        "water",
    ] # Assuming fire relates to offense, water to defense
    if not all(elem in elemental_balance for elem in required_elements):
        logger.error(
            f"Elemental balance missing required elements for rating validation: {list(set(required_elements) - set(elemental_balance.keys()))}"
        )
        return {
            "status": "failed",
            "reason": "Elemental balance missing required elements",
        }

    try:
        # Calculate offensive difference
        offensive_diff = abs(
            api_data["OFF_RATING"].values[0] - elemental_balance.get("fire", 0.0)
        )

        # Calculate defensive drift (difference)
        defensive_diff = abs(
            api_data["DEF_RATING"].values[0] - elemental_balance.get("water", 0.0)
        ) # Use .get with default

        return {
            "status": "success",
            "offensive_diff": float(offensive_diff), # Ensure float
            "defensive_diff": float(defensive_diff), # Ensure float (renamed from defensive_drift for clarity)
        }

    except Exception as e:
        logger.error(f" Error validating team ratings: {e}", exc_info=True)
        return {"status": "failed", "error": str(e)}


# ====================
# ELEMENTAL ALIGNMENT CALCULATION
# ====================

# Ensure TeamOlympianAnalytics is available to access its ELEMENTAL_MAP
elemental_map = TeamOlympianAnalytics.ELEMENTAL_MAP # Now directly access the class attribute


@oracle_focus # Applying the real decorator
def calculate_elemental_compatibility(df: pd.DataFrame) -> pd.DataFrame:
    """
    Vectorized elemental compatibility calculation.

    Requires numpy and access to TeamOlympianAnalytics.ELEMENTAL_MAP.

    Args:
        df: DataFrame with elemental features, 'elemental_balance', and 'primary_element'.

    Returns:
        DataFrame with added 'elemental_alignment' feature.
    """
    logger.info(" MEDUSA VAULT: Calculating elemental compatibility.")
    # Ensure elemental_map is available and not empty
    if not elemental_map:
        logger.warning(
            "Elemental map not available for compatibility calculation. Skipping."
        )
        df["elemental_alignment"] = np.nan # Add column with NaN if calculation skipped
        return df

    elements = list(elemental_map.keys())
    # Ensure required columns exist in df before proceeding
    required_cols = elements + ["elemental_balance", "primary_element"]
    if not all(col in df.columns for col in required_cols):
        missing = set(required_cols) - set(df.columns)
        logger.error(
            f"Missing required columns for elemental compatibility calculation: {missing}. Skipping."
        )
        df["elemental_alignment"] = np.nan # Add column with NaN if calculation skipped
        return df

    balance_cols = [f"elemental_balance_{e}" for e in elements]

    # Explode balance dictionary into separate columns
    try:
        # Ensure 'elemental_balance' column contains dictionaries and has expected keys
        if not df["elemental_balance"].empty and all(
            isinstance(d, dict) for d in df["elemental_balance"].dropna()
        ):
            # Create a temporary DataFrame from the dictionary column, ensuring all elemental columns are present
            balance_df = pd.json_normalize(df["elemental_balance"])
            # Ensure all expected elemental columns are in balance_df, fill missing with 0
            for elem in elements:
                if elem not in balance_df.columns:
                    balance_df[elem] = 0.0
            # Reorder columns to match the 'elements' list
            balance_df = balance_df[elements]
            # Assign the values to the new balance_cols in the original df
            df[balance_cols] = balance_df.values # Assign values as numpy array

        else:
            logger.error(
                "elemental_balance column is empty, does not contain dictionaries, or has unexpected format for explosion. Skipping."
            )
            df["elemental_alignment"] = (
                np.nan
            ) # Add column with NaN if calculation skipped
            return df # Return original df

    except Exception as e:
        logger.error(f" Error exploding elemental_balance column: {e}", exc_info=True)
        df["elemental_alignment"] = np.nan # Add column with NaN if calculation skipped
        return df # Return original df

    # Calculate alignment using matrix operations
    try:
        element_strengths = df[elements].values.astype(float) # Ensure float type
        balance_weights = df[balance_cols].values.astype(float) # Ensure float type

        # Create dummy variables for 'primary_element', ensuring columns match expected elements
        # This is safer than relying on the order from get_dummies alone
        all_possible_elements = list(
            elemental_map.keys()
        ) # Get a fixed order based on the map
        # Use get_dummies with specified columns to ensure consistent output shape and order
        primary_elements_dummies = pd.get_dummies(
            df["primary_element"], columns=all_possible_elements
        ).values.astype(
            float
        ) # Ensure float type

        # Ensure the dummy columns are in the same order as 'elements' list for matrix multiplication
        if all(e in all_possible_elements for e in elements):
            primary_elements_dummies = primary_elements_dummies[
                :, [all_possible_elements.index(e) for e in elements]
            ]
        else:
            logger.error(
                "Elements list contains items not in elemental_map keys. Cannot align dummy columns. Skipping."
            )
            df["elemental_alignment"] = (
                np.nan
            ) # Add column with NaN if calculation skipped
            return df

        # Perform matrix multiplication
        if (
            element_strengths.shape == balance_weights.shape
            and element_strengths.shape == primary_elements_dummies.shape
        ):
            # Ensure shapes are (n_samples, n_elements)
            if element_strengths.shape[1] == len(elements):
                df["elemental_alignment"] = (
                    np.sum(
                        element_strengths * balance_weights * primary_elements_dummies,
                        axis=1,
                    )
                    * 3.0
                ) # Ensure float multiplication
            else:
                logger.error(
                    f"Matrix shape mismatch for elemental compatibility calculation (elements dimension): {element_strengths.shape}, expected dimension {len(elements)}. Skipping."
                )
                df["elemental_alignment"] = (
                    np.nan
                ) # Add column with NaN if calculation skipped

        else:
            logger.error(
                f"Matrix shape mismatch for elemental compatibility calculation: {element_strengths.shape}, {balance_weights.shape}, {primary_elements_dummies.shape}. Skipping."
            )
            df["elemental_alignment"] = (
                np.nan
            ) # Add column with NaN if calculation skipped

    except Exception as e:
        logger.error(
            f" Error during vectorized elemental compatibility calculation: {e}",
            exc_info=True,
        )
        df["elemental_alignment"] = np.nan # Add column with NaN if calculation skipped
        return df # Return original df

    # Drop the temporary balance columns
    return df.drop(columns=balance_cols, errors="ignore")


# ====================
# PRODUCTION-GRADE FEATURE PIPELINE
# ====================
# Apply oracle_focus decorator to the main pipeline function
@oracle_focus # Apply the decorator
def generate_features(
    df: pd.DataFrame, target: Optional[str] = None
) -> Tuple[pd.DataFrame, Optional[pd.Series]]:
    """
    Battle-tested feature pipeline with validation and enhancement.

    Orchestrates the process of cleaning, normalizing, enhancing, and finalizing
    raw data into features suitable for model training or inference.

    Args:
        df: The input DataFrame containing raw or partially processed data.
        target: Optional. The name of the target column to separate from features.

    Returns:
        A tuple containing:
        - X: DataFrame of features.
        - y: Series of the target variable, or None if no target is specified or found.

    Raises:
        ValueError: If initial input schema validation fails.
        RuntimeError: If critical feature generation steps fail.
    """
    logger.info(f"Generating features. Target: {target if target else 'None'}")
    processed_df = df.copy() # Work on a copy

    try:
        # Pre-validation - uses validate_input_schema from core utils
        logger.info(" MEDUSA VAULT: Performing input schema validation.")
        validate_input_schema(processed_df)
        logger.info(" MEDUSA VAULT: Input schema validation successful.")

        # Core cleaning and normalization - uses clean_and_normalize from core utils
        logger.info(" MEDUSA VAULT: Cleaning and normalizing data.")
        processed_df = clean_and_normalize(processed_df, nba_api_compat=True)
        logger.info(" MEDUSA VAULT: Data cleaning and normalization complete.")

        # --- Integrate other feature engineering steps here ---
        # These functions need to be found and imported from their correct locations.
        # Example calls (uncomment and adjust import paths when available):

        # logger.info(" MEDUSA VAULT: Computing recent form features.")
        # try:
        # # Assuming compute_recent_form takes df and returns df
        # from .some_other_feature_module import compute_recent_form # Placeholder import path
        # processed_df = compute_recent_form(processed_df, group_col="hero_id",
        # target_cols=["points", "rebounds", "assists"],
        # window=5,
        # min_games=3)
        # logger.info(" MEDUSA VAULT: Recent form features computed.")
        # except ImportError:
        # logger.warning(" TITAN WARNING: compute_recent_form not importable, skipping recent form calculation.")
        # except Exception as e:
        # logger.error(f" Error computing recent form: {e}", exc_info=True)
        # # Decide how to handle this failure - continue or raise?
        # # raise RuntimeError(" TITAN PROCESSING FAILED: compute recent form.") from e

        # logger.info(" MEDUSA VAULT: Merging contextual data.")
        # try:
        # # Assuming merge_contextual_data takes df and returns df
        # from .some_other_feature_module import merge_contextual_data # Placeholder import path
        # processed_df = merge_contextual_data(processed_df)
        # logger.info(" MEDUSA VAULT: Contextual data merged.")
        # # Context merging with memory management - uses reduce_memory_usage from core utils
        # logger.info(" MEDUSA VAULT: Reducing memory usage after context merge.")
        # # NOTE: reduce_memory_usage is now imported from vault_oracle.core.data_processing_utils
        # processed_df = reduce_memory_usage(processed_df)
        # logger.info(" MEDUSA VAULT: Memory usage reduced.")
        # except ImportError:
        # logger.warning(" TITAN WARNING: merge_contextual_data not importable, skipping context merge.")
        # except Exception as e:
        # logger.error(f" Error merging contextual data: {e}", exc_info=True)
        # # Decide how to handle this failure - continue or raise?
        # # raise RuntimeError(" TITAN PROCESSING FAILED: merge contextual data.") from e

        # Enhance with Olympian insights (uses TeamOlympianAnalytics and SynergyWeaver)
        logger.info(" MEDUSA VAULT: Enhancing features with Olympian insights.")
        processed_df = enhance_with_olympian_insights(processed_df)
        logger.info(" MEDUSA VAULT: Olympian insights enhancement complete.")

        # Calculate elemental compatibility (uses TeamOlympianAnalytics.ELEMENTAL_MAP)
        # NOTE: calculate_elemental_compatibility is now defined within this file
        logger.info(" MEDUSA VAULT: Calculating elemental compatibility.")
        processed_df = calculate_elemental_compatibility(processed_df)
        logger.info(" MEDUSA VAULT: Elemental compatibility calculation complete.")

        # Finalize features with version control - uses finalize_feature_pipeline
        logger.info(" MEDUSA VAULT: Finalizing feature pipeline.")
        # Drop the target column before finalizing features (X)
        X = processed_df.drop(
            columns=[target] if target and target in processed_df.columns else [],
            errors="ignore",
        ) # Use errors='ignore' if target column might not exist

        # Call the finalize pipeline function
        X = finalize_feature_pipeline(X, pipeline_version="nba_api_v2")
        logger.info(" MEDUSA VAULT: Feature pipeline finalized.")

        # Separate the target column if specified and exists
        y = processed_df[target] if target and target in processed_df.columns else None
        if target and y is None:
            logger.warning(
                f"Target column '{target}' not found in DataFrame after processing. Returning None for target."
            )

        logger.info(" MEDUSA VAULT: Feature generation complete.")
        return (X, y)

    except ValueError as e:
        logger.critical(
            f" Feature generation failed due to input schema validation error: {e}",
            exc_info=True,
        )
        raise ValueError(
            "Feature generation failed: Input schema validation error."
        ) from e
    except Exception as e:
        logger.critical(
            f" An unexpected error occurred during feature generation: {e}",
            exc_info=True,
        )
        raise RuntimeError(
            "Feature generation failed due to an unexpected error."
        ) from e


@oracle_focus # Applying the real decorator
def finalize_feature_pipeline(
    X: pd.DataFrame, pipeline_version: str = "nba_api_v2"
) -> pd.DataFrame:
    """
    Finalizes the feature DataFrame for model consumption.
    This is a production scaffold. Add versioning, feature selection, or post-processing as needed.

    Args:
        X: The feature DataFrame.
        pipeline_version: Version string for the pipeline (for tracking/compatibility).

    Returns:
        The finalized feature DataFrame.
    """
    logger.info(f"Finalizing features with pipeline version: {pipeline_version}")
    # Add any final feature selection, ordering, or type enforcement here
    return X


# ====================
# 🧠 SELF-LEARNING FEATURE ALCHEMIST EXTENSIONS
# ====================

# Import FeatureFeedback from separate module to avoid circular imports

class SelfLearningFeatureAlchemist:
    """
    Self-learning, automated feature engineering pipeline.
    Implements:
      1. Automated Feature Discovery
      2. Continuous Learning
      3. Performance Feedback Loop
      4. Integration with Model Training
      5. Minimal Human Touch (automation)
    """
    def __init__(self, model_trainer, data_source, feedback_db=None, enable_quantum=True):
        self.model_trainer = model_trainer  # Should have fit(), evaluate(), feature_importances_
        self.data_source = data_source      # Should provide get_new_data()
        self.feedback_db = feedback_db      # For storing feature performance
        self.current_features = []
        self.feature_history = []
        self.last_performance = None
        self.enable_quantum = enable_quantum

        # Initialize quantum systems integration
        self.quantum_forge = None
        self.moirai_simulacrum = None
        self.automl_discovery = None

        if enable_quantum:
            self._initialize_quantum_systems()

        # Initialize AutoML Discovery
        self._initialize_automl_discovery()

    def _initialize_quantum_systems(self):
        """Initialize quantum feature engineering systems"""
        try:

            self.quantum_forge = ProphecyFeatureForge()
            self.moirai_simulacrum = ExpertMoiraiSimulacrum()
            logger.info("🧬 [Alchemist] Quantum systems initialized successfully")

        except ImportError as e:
            logger.warning(f"[Alchemist] Quantum systems not available: {e}")
            self.enable_quantum = False
        except Exception as e:
            logger.error(f"[Alchemist] Failed to initialize quantum systems: {e}")
            self.enable_quantum = False

    def _initialize_automl_discovery(self):
        """Initialize AutoML feature discovery system"""
        try:
            self.automl_discovery = AutoMLFeatureDiscovery()
            logger.info("🔍 [Alchemist] AutoML Discovery initialized successfully")

        except ImportError as e:
            logger.warning(f"[Alchemist] AutoML Discovery not available: {e}")
        except Exception as e:
            logger.error(f"[Alchemist] Failed to initialize AutoML Discovery: {e}")

    def automated_feature_discovery(self, X, y):
        """Enhanced feature discovery using quantum systems and AutoML."""
        logger.info("[Alchemist] Running enhanced automated feature discovery...")

        X_enhanced = X.copy()

        # 1. Use AutoML Discovery if available
        if self.automl_discovery:
            try:
                logger.info("🔍 [Alchemist] Using AutoML feature discovery...")
                X_enhanced = self.automl_discovery.discover_features(X_enhanced, y)
                logger.info(f"🔍 [Alchemist] AutoML discovered {len(X_enhanced.columns) - len(X.columns)} new features")
            except Exception as e:
                logger.warning(f"[Alchemist] AutoML discovery failed: {e}")

        # 2. Use Quantum Feature Enhancement if available
        if self.enable_quantum and self.quantum_forge:
            try:
                X_enhanced = self._enhance_with_quantum_features(X_enhanced)
            except Exception as e:
                logger.warning(f"[Alchemist] Quantum enhancement failed: {e}")

        # 3. Fallback to traditional feature engineering
        if len(X_enhanced.columns) == len(X.columns):
            logger.info("[Alchemist] Using traditional polynomial feature discovery...")

            poly = PolynomialFeatures(degree=2, include_bias=False)
            X_poly = poly.fit_transform(X_enhanced)

            # Select best features
            selector = SelectKBest(score_func=f_regression, k=min(20, X_poly.shape[1]))
            X_selected = selector.fit_transform(X_poly, y)
            self.current_features = selector.get_support(indices=True)

            # Convert back to DataFrame if possible
            if hasattr(X, 'columns'):
                feature_names = [f"poly_feature_{i}" for i in range(X_selected.shape[1])]
                X_enhanced = pd.DataFrame(X_selected, columns=feature_names, index=X.index)
            else:
                X_enhanced = X_selected

        logger.info(f"[Alchemist] Feature discovery complete: {X_enhanced.shape[1]} total features")
        return X_enhanced

    def _enhance_with_quantum_features(self, X):
        """Enhance features using quantum systems"""
        logger.info("🔬 [Alchemist] Enhancing features with quantum systems...")

        X_enhanced = X.copy()
        quantum_features_added = 0

        # Check if we have player/team identifiers in the data
        player_cols = [col for col in X.columns if 'player' in col.lower() or 'hero' in col.lower()]
        team_cols = [col for col in X.columns if 'team' in col.lower()]

        # Enhance with quantum features for players
        if player_cols and self.quantum_forge:
            for col in player_cols[:3]:  # Limit to avoid explosion
                try:
                    # Get unique player IDs
                    player_ids = X[col].dropna().unique()[:5]  # Limit for performance

                    for player_id in player_ids:
                        quantum_result = self.quantum_forge.forge_quantum_features(str(player_id))
                        if quantum_result.get('quantum_enabled') and quantum_result.get('features'):
                            # Add quantum features as new columns
                            for feature_name, feature_value in quantum_result['features'].items():
                                if isinstance(feature_value, (int, float)):
                                    new_col_name = f"quantum_{col}_{feature_name}"
                                    # Create feature for all rows where this player appears
                                    X_enhanced.loc[X[col] == player_id, new_col_name] = feature_value
                                    quantum_features_added += 1

                except Exception as e:
                    logger.warning(f"[Alchemist] Failed to add quantum features for {col}: {e}")

        # Enhance with simulation features if available
        if self.moirai_simulacrum and len(X) > 0:
            try:
                # Use first row as sample for simulation
                sample_data = X.iloc[0].to_dict()
                simulation_result = self.moirai_simulacrum.simulate(sample_data)

                if isinstance(simulation_result, dict) and 'features' in simulation_result:
                    sim_features = simulation_result['features']
                    for feature_name, feature_value in sim_features.items():
                        if isinstance(feature_value, (int, float)):
                            new_col_name = f"simulation_{feature_name}"
                            X_enhanced[new_col_name] = feature_value  # Broadcast to all rows
                            quantum_features_added += 1

            except Exception as e:
                logger.warning(f"[Alchemist] Failed to add simulation features: {e}")

        # Fill NaN values in quantum features with 0
        quantum_cols = [col for col in X_enhanced.columns if col.startswith(('quantum_', 'simulation_'))]
        if quantum_cols:
            X_enhanced[quantum_cols] = X_enhanced[quantum_cols].fillna(0)

        logger.info(f"🔬 [Alchemist] Added {quantum_features_added} quantum-enhanced features")
        return X_enhanced

    def integrate_basketball_intelligence(self, X, game_data=None):
        """Integrate features from basketball cortex and cognitive spires"""
        logger.info("🏀 [Alchemist] Integrating basketball intelligence features...")

        X_enhanced = X.copy()
        basketball_features_added = 0

        try:
            # Lazy import basketball systems to avoid circular dependencies
            from src.cognitive_basketball_cortex.cognitive_spires_manager import CognitiveSpiresManager
            from src.cognitive_spires.NikeVictoryOracle_Expert import NikeVictoryOracle_Expert
            from src.cognitive_spires.AthenaStrategyEngine_Expert import AthenaStrategyEngine_Expert

            # Initialize basketball systems
            cortex_manager = CognitiveSpiresManager()
            nike_oracle = NikeVictoryOracle_Expert()
            athena_engine = AthenaStrategyEngine_Expert()

            # Use game_data if provided, otherwise create from X
            if game_data is None and len(X) > 0:
                game_data = X.iloc[0].to_dict()

            if game_data:
                # Get basketball cortex features
                try:
                    cortex_results = asyncio.run(cortex_manager.run_all(game_data))

                    # Add cortex features
                    for feature_name, feature_data in cortex_results.items():
                        if isinstance(feature_data, dict):
                            for sub_name, sub_value in feature_data.items():
                                if isinstance(sub_value, (int, float)):
                                    new_col_name = f"cortex_{feature_name}_{sub_name}"
                                    X_enhanced[new_col_name] = sub_value
                                    basketball_features_added += 1

                except Exception as e:
                    logger.warning(f"[Alchemist] Basketball cortex integration failed: {e}")

                # Get cognitive spires features
                try:
                    # Extract team IDs if available
                    home_team = game_data.get('home_team_id', 'unknown')
                    away_team = game_data.get('away_team_id', 'unknown')

                    if home_team != 'unknown' and away_team != 'unknown':

                        # Get Nike Victory Oracle features
                        victory_metrics = asyncio.run(nike_oracle.predict_victory(
                            home_team, away_team, datetime.now(),
                            game_data.get('home_stats', {}),
                            game_data.get('away_stats', {})
                        ))

                        # Add victory features
                        victory_features = {
                            'nike_win_probability': victory_metrics.win_probability,
                            'nike_momentum_score': victory_metrics.momentum_score,
                            'nike_clutch_factor': victory_metrics.clutch_factor,
                            'nike_upset_potential': victory_metrics.upset_potential,
                            'nike_confidence': victory_metrics.confidence_score
                        }

                        for feature_name, feature_value in victory_features.items():
                            if isinstance(feature_value, (int, float)):
                                X_enhanced[feature_name] = feature_value
                                basketball_features_added += 1

                except Exception as e:
                    logger.warning(f"[Alchemist] Cognitive spires integration failed: {e}")

        except ImportError as e:
            logger.warning(f"[Alchemist] Basketball systems not available: {e}")
        except Exception as e:
            logger.error(f"[Alchemist] Basketball integration failed: {e}")

        logger.info(f"🏀 [Alchemist] Added {basketball_features_added} basketball intelligence features")
        return X_enhanced

    def continuous_learning(self):
        """Retrain on new data automatically."""
        logger.info("[Alchemist] Checking for new data and retraining if needed...")
        new_data = self.data_source.get_new_data()
        if new_data is not None:
            X, y = new_data
            X_new = self.automated_feature_discovery(X, y)
            self.model_trainer.fit(X_new, y)
            self.last_performance = self.model_trainer.evaluate(X_new, y)
            self.update_feature_feedback(X_new, y)
            logger.info(f"[Alchemist] Retrained model. New performance: {self.last_performance}")
        else:
            logger.info("[Alchemist] No new data found.")

    def update_feature_feedback(self, X, y):
        """Track which features help or hurt performance."""
        logger.info("[Alchemist] Updating feature feedback...")
        if hasattr(self.model_trainer, 'feature_importances_'):
            importances = self.model_trainer.feature_importances_
            # Remove features with very low importance
            low_importance = [i for i, imp in enumerate(importances) if imp < 0.01]
            if low_importance:
                logger.info(f"[Alchemist] Removing {len(low_importance)} low-importance features.")
                # Remove from current_features (if using feature indices)
                self.current_features = [i for i in self.current_features if i not in low_importance]
            if self.feedback_db:
                self.feedback_db.save(importances)

    def integrate_with_model_training(self, X, y):
        """Connect feature engineering to model training for feedback."""
        logger.info("[Alchemist] Integrating with model training...")
        self.model_trainer.fit(X, y)
        self.last_performance = self.model_trainer.evaluate(X, y)
        self.update_feature_feedback(X, y)
        logger.info(f"[Alchemist] Model trained. Performance: {self.last_performance}")

    def run_automation(self):
        """Automate the whole process, minimal human touch."""
        logger.info("[Alchemist] Running full automation loop...")
        try:
            self.continuous_learning()
        except Exception as e:
            logger.error(f"[Alchemist] Automation error: {e}")
            # Optionally alert a human here

    def receive_feedback(self, feedback):
        """Receive feedback from a cognitive spire and trigger new feature search if needed."""
        logger.info(f"[Alchemist] Received feedback from {feedback.spire_name}: {feedback.message}")
        if feedback.performance < 0.5:  # Example threshold for 'bad' performance
            logger.info(f"[Alchemist] Performance was low ({feedback.performance}). Trying new features for {feedback.spire_name}.")
            # Optionally, keep a history of feedback and adapt feature search
            # Could trigger automated_feature_discovery with new parameters
            # ...
        else:
            logger.info(f"[Alchemist] Performance acceptable for {feedback.spire_name}.")

    def comprehensive_feature_engineering(self, X, y=None, game_data=None):
        """
        Comprehensive feature engineering using all available systems:
        - AutoML Discovery
        - Quantum Feature Enhancement
        - Basketball Intelligence Integration
        - Traditional Feature Engineering
        """
        logger.info("🧬 [Alchemist] Starting comprehensive feature engineering...")

        X_enhanced = X.copy()
        total_features_added = 0

        # 1. AutoML Feature Discovery
        if self.automl_discovery:
            try:
                logger.info("🔍 [Alchemist] Phase 1: AutoML feature discovery...")
                X_enhanced = self.automated_feature_discovery(X_enhanced, y)
                automl_features = len(X_enhanced.columns) - len(X.columns)
                total_features_added += automl_features
                logger.info(f"🔍 [Alchemist] AutoML added {automl_features} features")
            except Exception as e:
                logger.warning(f"[Alchemist] AutoML phase failed: {e}")

        # 2. Quantum Enhancement
        if self.enable_quantum:
            try:
                logger.info("🔬 [Alchemist] Phase 2: Quantum feature enhancement...")
                X_quantum = self._enhance_with_quantum_features(X_enhanced)
                quantum_features = len(X_quantum.columns) - len(X_enhanced.columns)
                X_enhanced = X_quantum
                total_features_added += quantum_features
                logger.info(f"🔬 [Alchemist] Quantum enhancement added {quantum_features} features")
            except Exception as e:
                logger.warning(f"[Alchemist] Quantum phase failed: {e}")

        # 3. Basketball Intelligence Integration
        try:
            logger.info("🏀 [Alchemist] Phase 3: Basketball intelligence integration...")
            X_basketball = self.integrate_basketball_intelligence(X_enhanced, game_data)
            basketball_features = len(X_basketball.columns) - len(X_enhanced.columns)
            X_enhanced = X_basketball
            total_features_added += basketball_features
            logger.info(f"🏀 [Alchemist] Basketball intelligence added {basketball_features} features")
        except Exception as e:
            logger.warning(f"[Alchemist] Basketball intelligence phase failed: {e}")

        # 4. Feature Quality Assessment
        if y is not None and len(X_enhanced) > 0:
            try:
                logger.info("📊 [Alchemist] Phase 4: Feature quality assessment...")
                X_enhanced = self._assess_and_filter_features(X_enhanced, y)
            except Exception as e:
                logger.warning(f"[Alchemist] Feature assessment failed: {e}")

        logger.info(f"🧬 [Alchemist] Comprehensive feature engineering complete!")
        logger.info(f"📈 [Alchemist] Total features: {len(X_enhanced.columns)} (added {total_features_added})")

        return X_enhanced

    def _assess_and_filter_features(self, X, y):
        """Assess feature quality and filter out low-value features"""
        logger.info("📊 [Alchemist] Assessing feature quality...")

        try:

            # Handle non-numeric columns
            numeric_cols = X.select_dtypes(include=[np.number]).columns
            X_numeric = X[numeric_cols]

            if len(X_numeric.columns) == 0:
                logger.warning("[Alchemist] No numeric features found for assessment")
                return X

            # Fill NaN values
            X_numeric = X_numeric.fillna(0)

            # Scale features for assessment
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X_numeric)

            # Use mutual information for feature selection
            selector = SelectKBest(score_func=mutual_info_regression, k=min(50, X_scaled.shape[1]))
            X_selected = selector.fit_transform(X_scaled, y)

            # Get selected feature names
            selected_features = numeric_cols[selector.get_support()]

            # Keep selected numeric features plus any non-numeric features
            non_numeric_cols = X.select_dtypes(exclude=[np.number]).columns
            final_features = list(selected_features) + list(non_numeric_cols)

            X_filtered = X[final_features]

            removed_features = len(X.columns) - len(X_filtered.columns)
            logger.info(f"📊 [Alchemist] Feature assessment complete: removed {removed_features} low-value features")

            return X_filtered

        except Exception as e:
            logger.warning(f"[Alchemist] Feature assessment failed: {e}")
            return X


# Example Usage and Testing
if __name__ == "__main__":
    """
    Expert-level testing and demonstration of the generate_features system.
    """
    
    # Create mock raw data (similar structure to what DataTitan might provide)
    # Ensure 'mythic_roster_id' and 'hero_id' are present for Olympian insights
    raw_data = {
        "hero_id": ["player_A", "player_B", "player_C", "player_D", "player_E", "player_F", "player_G"],
        "mythic_roster_id": ["LAL", "LAL", "LAL", "LAL", "LAL", "BOS", "BOS"], # Lakers need 5 players for this test
        "points": [25, 18, 12, 10, 8, 22, 15],
        "rebounds": [10, 7, 5, 4, 3, 9, 6],
        "assists": [8, 5, 3, 2, 1, 7, 4],
        "blocks": [1, 0, 2, 0, 1, 0, 1],
        "steals": [2, 1, 1, 0, 0, 2, 1],
        "field_goal_pct": [0.45, 0.48, 0.42, 0.50, 0.38, 0.46, 0.43],
        "three_point_pct": [0.35, 0.30, 0.25, 0.40, 0.20, 0.36, 0.28],
        "free_throw_pct": [0.80, 0.75, 0.85, 0.90, 0.70, 0.82, 0.78],
        "minutes": [35, 30, 25, 20, 15, 33, 28],
        "turnovers": [3, 2, 1, 1, 0, 2, 1],
        "fouls": [2, 3, 4, 2, 1, 3, 2],
        "target_variable": [1, 0, 1, 0, 1, 0, 1] # Example target variable
    }
    
    df = pd.DataFrame(raw_data)
    
    
    # Generate features
    try:
        features_df, target_series = generate_features(df.copy(), target="target_variable")

        if target_series is not None:
            logger.info("🏀 Feature Alchemist: Target series generated successfully")
            logger.info(f"🏀 Features shape: {features_df.shape}, Target shape: {target_series.shape}")
        else:
            logger.warning("⚠️ Feature Alchemist: No target series generated")

        # Verify added columns (e.g., elemental insights)
        expected_new_cols = [
            'fire', 'water', 'earth', 'air', 'aether',
            'war_council_score', 'synergy_nexus', 'api_alignment_status',
            'api_alignment_details', 'primary_element', 'elemental_balance_fire',
            'elemental_balance_water', 'elemental_balance_earth',
            'elemental_balance_air', 'elemental_balance_aether',
            'elemental_alignment'
        ]
        found_new_cols = [col for col in expected_new_cols if col in features_df.columns]
        if found_new_cols:
            logger.info(f"🏀 Feature Alchemist: Found {len(found_new_cols)} elemental features: {found_new_cols}")
        else:
            logger.info("🏀 Feature Alchemist: No elemental features found in generated data")

        logger.info("✅ Feature Alchemist test completed successfully!")

    except Exception as e:
        logger.error(f"❌ Feature Alchemist test failed: {e}")
        traceback.print_exc()
        sys.exit(1)

