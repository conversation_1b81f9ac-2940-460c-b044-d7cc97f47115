#!/usr/bin/env python3
"""
Test Expert Router Integration Status
Check if Phase 1 has been completed
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.expert_router_integration_fix import apply_expert_router_fixes

def main():
    print("🔍 Testing Expert Router Integration Status...")
    print("=" * 60)
    
    try:
        # Apply expert router fixes and get results
        result = apply_expert_router_fixes()
        
        print("📊 Expert Router Integration Results:")
        print(f"   Success Rate: {result['success_rate']:.1f}%")
        print(f"   Summary: {result['summary']}")
        
        # Check specific components
        print("\n🔧 Component Status:")
        if 'prometheus_fix' in result:
            prometheus_status = result['prometheus_fix']['status']
            print(f"   Prometheus Fix: {prometheus_status}")
            
        if 'import_fix' in result:
            import_status = result['import_fix']['status']
            print(f"   Import Fix: {import_status}")
            
        if 'loading_test' in result:
            loading_status = result['loading_test']['status']
            loaded_count = result['loading_test'].get('successfully_loaded', 0)
            failed_count = result['loading_test'].get('failed_to_load', 0)
            print(f"   Router Loading: {loading_status}")
            print(f"   Successfully Loaded: {loaded_count}")
            print(f"   Failed to Load: {failed_count}")
            
            if 'loaded_routers' in result['loading_test']:
                print(f"   Loaded Routers: {result['loading_test']['loaded_routers']}")
                
            if 'failed_routers' in result['loading_test']:
                print(f"   Failed Routers: {result['loading_test']['failed_routers']}")
        
        # Overall assessment
        print("\n🎯 Phase 1 Assessment:")
        if result['success_rate'] >= 80:
            print("   ✅ Phase 1: Expert Router Integration - COMPLETED")
        else:
            print("   ❌ Phase 1: Expert Router Integration - NEEDS WORK")
            
        return result
        
    except Exception as e:
        print(f"❌ Error testing expert router integration: {e}")
        return None

if __name__ == "__main__":
    main()
