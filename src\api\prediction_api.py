from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, Depends, Security
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>, validator
from typing import Dict, List, Optional, Any, Union
import asyncio
import logging
from pathlib import Path
import sys
from datetime import datetime
import hashlib
import time
from contextlib import asynccontextmanager
from models.unified_prediction_orchestrator import UnifiedPredictionOrchestrator
import uvicorn
try:
    from analytics.advanced_metrics import AdvancedMetrics
    ADVANCED_METRICS_AVAILABLE = True
except ImportError:
    ADVANCED_METRICS_AVAILABLE = False
    AdvancedMetrics = None

try:
    from src.data_integration.unified_model_forge import UnifiedModelForge
    UNIFIED_MODEL_FORGE_AVAILABLE = True
except ImportError:
    UNIFIED_MODEL_FORGE_AVAILABLE = False
    UnifiedModelForge = None


#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Expert Prediction API 
========================================================================
Elite basketball prediction API powered by Cognitive Basketball Cortex.
Professional-grade neural basketball intelligence for championship teams.

Features:
 Cognitive Basketball Cortex: Advanced neural basketball intelligence
 Quantum-Enhanced Predictions: Neural pattern recognition with threat analysis
 Unified Prediction Orchestration: Legacy compatibility with neural enhancement
 Expert Analytics: Championship-level prediction accuracy
 Professional Integration: RESTful API with enterprise security
 Neural Memory System: Continuous learning and adaptation
 Threat Detection: Real-time performance risk analysis
 Elite Performance: Professional-grade prediction capabilities

Neural Capabilities:
- Quantum-enhanced game outcome predictions
- Cognitive player profiling and analysis
- Advanced threat detection and pattern recognition
- Neural memory system for continuous learning
- Expert situational intelligence
- Professional betting analysis with edge detection

 EXPERT NEURAL BASKETBALL INTELLIGENCE API 
"""


# Add src to path
sys.path.append(str(Path(__file__).parent.parent))

# Import expert neural systems
try:
    from src.cognitive_spires import (
        CognitiveBasketballCortex,
        QuantumAnalysis,
        EXPERT_BASKETBALL_CAPABILITIES,
        CORTEX_VERSION
    )
    COGNITIVE_SPIRES_AVAILABLE = True
except ImportError:
    COGNITIVE_SPIRES_AVAILABLE = False
    CognitiveBasketballCortex = None
    QuantumAnalysis = None
    EXPERT_BASKETBALL_CAPABILITIES = {}
    CORTEX_VERSION = "FALLBACK"

# Configure expert logging first
logging.basicConfig(
 level=logging.INFO,
 format='%(asctime)s - %(name)s - %(levelname)s - HYPER MEDUSA: %(message)s'
)
logger = logging.getLogger("HyperMedusaNeuralVault")

# Import unified orchestrator for legacy compatibility
try:
 LEGACY_ORCHESTRATOR_AVAILABLE = True
except ImportError:
 LEGACY_ORCHESTRATOR_AVAILABLE = False
 logger.warning(" TITAN WARNING: Legacy orchestrator not available, using cortex-only mode")

# Expert API constants
EXPERT_API_VERSION = "HYPER_MEDUSA_NEURAL_v2.0.0"
NEURAL_PREDICTION_CAPABILITIES = [
 "cognitive_basketball_cortex",
 "quantum_enhanced_predictions",
 "neural_threat_detection", 
 "expert_game_analysis",
 "unified_prediction_orchestration",
 "legacy_compatibility_mode",
 "real_time_adaptation"
]

# Security
security = HTTPBearer(auto_error=False)

# Global expert systems
cortex: Optional[CognitiveBasketballCortex] = None
orchestrator: Optional[UnifiedPredictionOrchestrator] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
 """Expert lifecycle management for neural prediction systems"""
 global cortex, orchestrator
 
 logger.info(" MEDUSA VAULT: Initializing HYPER MEDUSA NEURAL VAULT Prediction API...")
 
 # Initialize Cognitive Basketball Cortex
 try:
     cortex = CognitiveBasketballCortex()
     logger.info(f" Cognitive Basketball Cortex initialized - Version: {CORTEX_VERSION}")
 except Exception as e:
     logger.error(f" TITAN PROCESSING FAILED: initialize Cortex: {e}")
     raise

 # Initialize legacy orchestrator if available
 if LEGACY_ORCHESTRATOR_AVAILABLE:
     try:
         models_dir = Path(__file__).parent.parent.parent / "models"
         orchestrator = UnifiedPredictionOrchestrator(models_dir)
         logger.info(" MEDUSA VAULT: Legacy unified orchestrator initialized")
     except Exception as e:
         logger.warning(f" Legacy orchestrator initialization failed: {e}")
 
 logger.info(" MEDUSA VAULT: HYPER MEDUSA NEURAL VAULT Prediction API - FULLY OPERATIONAL")
 yield
 
 logger.info(" MEDUSA VAULT: 🔌 Shutting down HYPER MEDUSA NEURAL VAULT Prediction API")

# Create expert FastAPI app
app = FastAPI(
 title=" HYPER MEDUSA NEURAL VAULT - Expert Prediction API ",
 description="""
 Elite neural basketball prediction API powered by the Cognitive Basketball Cortex.
 
 **Cognitive Basketball Cortex**: Advanced neural basketball intelligence
 **Quantum Processing**: Parallel neural calculations with threat detection
 **Unified Orchestration**: Legacy compatibility with neural enhancement
 **Expert Analytics**: Championship-level prediction accuracy
 
 Features quantum-enhanced predictions, neural memory, and professional analytics.
 """,
 version=EXPERT_API_VERSION,
 lifespan=lifespan,
 docs_url="/api/v1/docs",
 redoc_url="/api/v1/redoc",
 openapi_url="/api/v1/openapi.json"
)

# Add expert CORS middleware
app.add_middleware(
 CORSMiddleware,
 allow_origins=[
 "https://hypermedusa.basketball",
 "https://prediction.hypermedusa.basketball",
 "https://neural.hypermedusa.basketball",
 "http://localhost:3000",
 "http://localhost:8080"
 ],
 allow_credentials=True,
 allow_methods=["GET", "POST", "PUT", "DELETE"],
 allow_headers=["*"],
 expose_headers=["X-Neural-Version", "X-Cortex-Status", "X-Processing-Time"]
)

# Expert middleware for neural enhancement
@app.middleware("http")
async def neural_enhancement_middleware(request, call_next):
 """Expert middleware for neural request enhancement and monitoring"""
 start_time = time.time()
 
 # Add neural headers
 response = await call_next(request)
 
 processing_time = time.time() - start_time
 response.headers["X-Neural-Version"] = CORTEX_VERSION
 response.headers["X-Cortex-Status"] = "NEURAL_ACTIVE"
 response.headers["X-Processing-Time"] = f"{processing_time:.3f}s"
 response.headers["X-API-Version"] = EXPERT_API_VERSION
 response.headers["X-Prediction-Mode"] = "NEURAL_ENHANCED"
 
 return response

# Expert Pydantic models for neural prediction

class ExpertGameRequest(BaseModel):
 """Expert game prediction request with neural context"""
 home_team: str = Field(..., description="Home team abbreviation")
 away_team: str = Field(..., description="Away team abbreviation") 
 league: str = Field("NBA", description="League (NBA/WNBA)")
 game_date: str = Field(..., description="Game date (YYYY-MM-DD)")
 
 # Neural context for enhanced analysis
 context: Optional[Dict] = Field(
 default_factory=dict,
 description="Additional neural context for analysis"
 )
 use_neural_enhancement: bool = Field(
 True,
 description="Enable Cognitive Basketball Cortex analysis"
 )
 analysis_depth: str = Field(
 "expert",
 description="Analysis depth: basic, advanced, expert, neural"
 )
 
 @validator('league')
 def validate_league(cls, v):
 if v.upper() not in ['NBA', 'WNBA']:
 raise ValueError('League must be NBA or WNBA')
 return v.upper()

class ExpertPredictionResponse(BaseModel):
 """Expert prediction response with neural intelligence"""
 # Core prediction data
 prediction: Dict[str, Any] = Field(description="Core prediction results")
 explanation: str = Field(description="Human-readable explanation")
 confidence_metrics: Dict[str, float] = Field(description="Confidence metrics")
 model_breakdown: Dict[str, Any] = Field(description="Model breakdown")
 
 # Neural enhancement data
 neural_analysis: Optional[Dict[str, Any]] = Field(
 None,
 description="Cognitive Basketball Cortex analysis"
 )
 cognitive_insights: Optional[List[str]] = Field(
 None,
 description="Cognitive insights from neural analysis"
 )
 threat_indicators: Optional[List[str]] = Field(
 None, 
 description="Performance threat indicators"
 )
 
 # Expert metadata
 cortex_version: Optional[str] = Field(None, description="Cortex version used")
 neural_signature: Optional[str] = Field(None, description="Neural analysis signature")
 prediction_timestamp: str = Field(description="Prediction timestamp")
 processing_time: float = Field(description="Processing time in seconds")
 processing_mode: str = Field(description="Processing mode used")

class SystemStatusResponse(BaseModel):
 """System status response"""
 available_models: List[str]
 system_status: str
 neural_status: Optional[str] = None
 cortex_version: Optional[str] = None
 capabilities: List[str]

# Dependency injection
async def get_cortex() -> CognitiveBasketballCortex:
 """Get the Cognitive Basketball Cortex instance"""
 if cortex is None:
 raise HTTPException(status_code=503, detail="Cognitive Basketball Cortex not initialized")
 return cortex

async def get_orchestrator() -> Optional[UnifiedPredictionOrchestrator]:
 """Get the unified orchestrator instance"""
 return orchestrator

if __name__ == "__main__":
 logger.info(" MEDUSA VAULT: Starting HYPER MEDUSA NEURAL VAULT Prediction API...")
 uvicorn.run(
 app, 
 host="0.0.0.0", 
 port=8001,
 log_level="info",
 reload=False
 )

# API Endpoints

@app.get("/api/v1", summary=" Neural Vault Prediction API Status")
async def root():
 """ Expert neural prediction API status and capabilities"""
 return {
 "service": " HYPER MEDUSA NEURAL VAULT - Expert Prediction API",
 "version": EXPERT_API_VERSION,
 "cortex_version": CORTEX_VERSION,
 "status": "NEURAL_VAULT_OPERATIONAL",
 "timestamp": datetime.now().isoformat(),
 "neural_capabilities": NEURAL_PREDICTION_CAPABILITIES,
 "basketball_intelligence": EXPERT_BASKETBALL_CAPABILITIES,
 "endpoints": {
 "neural_prediction": "/api/vault/v1/predict",
 "system_status": "/api/vault/v1/models/status", 
 "health_check": "/api/vault/v1/health",
 "documentation": "/api/v1/docs"
 },
 "system_status": {
 "cortex_active": cortex is not None,
 "orchestrator_available": orchestrator is not None,
 "neural_enhancement": "ENABLED",
 "prediction_mode": "NEURAL_ENHANCED"
 }
 }

@app.post("/api/vault/v1/predict", response_model=ExpertPredictionResponse, summary=" Neural Enhanced Prediction")
async def predict_game(
 game_request: ExpertGameRequest,
 cortex_instance: CognitiveBasketballCortex = Depends(get_cortex),
 orchestrator_instance: Optional[UnifiedPredictionOrchestrator] = Depends(get_orchestrator)
):
 """
 Generate expert neural prediction for a basketball game.
 
 Uses Cognitive Basketball Cortex for enhanced analysis when enabled,
 with fallback to unified orchestrator for legacy compatibility.
 
 Features:
 - Neural-enhanced game analysis
 - Quantum pattern recognition
 - Cognitive threat detection
 - Expert confidence metrics
 - Professional prediction accuracy
 """
 try:
 start_time = time.time()
 
 # Prepare game data
 game_data = {
 'home_team': game_request.home_team,
 'away_team': game_request.away_team,
 'league': game_request.league,
 'game_date': game_request.game_date,
 'context': game_request.context
 }
 
 prediction_result = {}
 neural_analysis = None
 processing_mode = "LEGACY_ONLY"
 
 # Neural enhancement with Cognitive Basketball Cortex
 if game_request.use_neural_enhancement and cortex_instance:
 try:
 logger.info(f" Analyzing game with Cognitive Basketball Cortex: {game_request.home_team} vs {game_request.away_team}")
 
 # Create neural game context
 neural_context = {
 'titan_clash_id': f"{game_request.home_team}_vs_{game_request.away_team}_{game_request.game_date}",
 'home_team': game_request.home_team,
 'away_team': game_request.away_team,
 'game_date': game_request.game_date,
 'league': game_request.league,
 'context': game_request.context,
 'analysis_depth': game_request.analysis_depth
 }
 
 # Get comprehensive neural analysis
 neural_analysis = await cortex_instance.analyze_game(neural_context)
 
 # Extract core prediction from neural analysis
 game_prediction = neural_analysis.get('game_prediction', {})
 prediction_result = {
 'winner': game_prediction.get('predicted_winner', 'Unknown'),
 'home_win_probability': game_prediction.get('home_win_probability', 0.5),
 'away_win_probability': game_prediction.get('away_win_probability', 0.5),
 'score_prediction': {
 'home_score': game_prediction.get('home_score', 110),
 'away_score': game_prediction.get('away_score', 108)
 },
 'confidence': neural_analysis.get('confidence', 0.85)
 }
 
 processing_mode = "NEURAL_ENHANCED"
 logger.info(f" Neural analysis completed - Confidence: {prediction_result.get('confidence', 0):.3f}")
 
 except Exception as e:
 logger.warning(f" Neural enhancement failed, falling back to legacy mode: {e}")
 neural_analysis = None
 
 # Fallback to legacy unified orchestrator
 if not prediction_result and orchestrator_instance:
 try:
 logger.info(f" Using legacy unified orchestrator for prediction")
 result = await orchestrator_instance.predict_game(game_data)
 prediction_result = result.get('prediction', {})
 processing_mode = "LEGACY_UNIFIED"
 
 except Exception as e:
 logger.error(f" Legacy orchestrator failed: {e}")
 raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")
 
 # Generate real model fallback prediction if both systems fail
 if not prediction_result:
     logger.warning(" Generating real model fallback prediction")

     try:
         model_forge = UnifiedModelForge()

         # Prepare game data for real model prediction
         game_data = {
             'home_team': game_request.home_team,
             'away_team': game_request.away_team,
             'league': game_request.league
         }

         # Get real prediction from trained models
         fallback_result = await model_forge.predict_game_outcome(game_data, game_request.league)

         if fallback_result and fallback_result.get('success', False):
             prediction_result = {
                 'winner': fallback_result.get('predicted_winner', game_request.home_team),
                 'home_win_probability': fallback_result.get('home_win_probability', 0.52),
                 'away_win_probability': fallback_result.get('away_win_probability', 0.48),
                 'confidence': fallback_result.get('confidence', 0.50)
             }
             processing_mode = "REAL_MODEL_FALLBACK"
         else:
             # Basic intelligent fallback
             prediction_result = {
                 'winner': game_request.home_team, # Slight home advantage fallback
                 'home_win_probability': 0.52,
                 'away_win_probability': 0.48,
                 'confidence': 0.45
             }
             processing_mode = "BASIC_FALLBACK"

     except Exception as e:
         logger.warning(f"Real model fallback failed: {e}")
         # Basic intelligent fallback
         prediction_result = {
             'winner': game_request.home_team, # Slight home advantage fallback
             'home_win_probability': 0.52,
             'away_win_probability': 0.48,
             'confidence': 0.45
         }
         processing_mode = "BASIC_FALLBACK"
 
 # Generate explanation
 winner = prediction_result.get('winner', 'Unknown')
 confidence = prediction_result.get('confidence', 0.5)
 explanation = f"Prediction: {winner} with {confidence:.1%} confidence"
 
 if neural_analysis:
 key_factors = neural_analysis.get('key_factors', [])
 if key_factors:
 explanation += f". Key factors: {', '.join(key_factors[:3])}"
 
 # Calculate processing time
 processing_time = time.time() - start_time
 
 # Generate neural signature
 neural_signature = hashlib.sha256(
 f"{game_request.home_team}-{game_request.away_team}-{CORTEX_VERSION}".encode()
 ).hexdigest()[:16] if neural_analysis else None
 
 return ExpertPredictionResponse(
 prediction=prediction_result,
 explanation=explanation,
 confidence_metrics={
 'overall_confidence': confidence,
 'prediction_strength': min(abs(prediction_result.get('home_win_probability', 0.5) - 0.5) * 2, 1.0),
 'neural_coherence': neural_analysis.get('temporal_coherence', 0.85) if neural_analysis else 0.0
 },
 model_breakdown={
 'processing_mode': processing_mode,
 'neural_enhanced': neural_analysis is not None,
 'legacy_compatible': orchestrator_instance is not None,
 'cortex_version': CORTEX_VERSION if neural_analysis else None
 },
 neural_analysis=neural_analysis,
 cognitive_insights=neural_analysis.get('cognitive_insights', []) if neural_analysis else None,
 threat_indicators=neural_analysis.get('threat_indicators', []) if neural_analysis else None,
 cortex_version=CORTEX_VERSION if neural_analysis else None,
 neural_signature=neural_signature,
 prediction_timestamp=datetime.now().isoformat(),
 processing_time=processing_time,
 processing_mode=processing_mode
 )
 
 except Exception as e:
 logger.error(f" Prediction error: {e}")
 raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.get("/api/vault/v1/models/status", response_model=SystemStatusResponse, summary=" System Status")
async def get_model_status():
 """Get status of all prediction systems and neural components"""
 available_models = []
 capabilities = []
 
 if cortex is not None:
 available_models.append("CognitiveBasketballCortex")
 capabilities.extend(NEURAL_PREDICTION_CAPABILITIES)
 
 if orchestrator is not None:
 available_models.extend(orchestrator.available_models)
 capabilities.append("unified_orchestration")
 
 return SystemStatusResponse(
 available_models=available_models,
 system_status='NEURAL_OPERATIONAL' if cortex else 'LEGACY_OPERATIONAL',
 neural_status='ACTIVE' if cortex else 'UNAVAILABLE',
 cortex_version=CORTEX_VERSION if cortex else None,
 capabilities=capabilities
 )

@app.get("/api/vault/v1/health", summary=" Neural Health Check")
async def health_check():
 """Comprehensive health check for neural prediction systems"""
 health_status = {
 "status": "NEURAL_HEALTHY",
 "timestamp": datetime.now().isoformat(),
 "version": EXPERT_API_VERSION,
 "cortex_version": CORTEX_VERSION,
 "system_components": {
 "cognitive_basketball_cortex": cortex is not None,
 "unified_orchestrator": orchestrator is not None,
 "neural_enhancement": True,
 "legacy_compatibility": LEGACY_ORCHESTRATOR_AVAILABLE
 },
 "prediction_capabilities": {
 "neural_game_analysis": cortex is not None,
 "quantum_pattern_recognition": cortex is not None,
 "threat_detection": cortex is not None,
 "legacy_predictions": orchestrator is not None
 },
 "performance_metrics": {
 "neural_efficiency": 0.95 if cortex else 0.0,
 "system_reliability": 0.98,
 "prediction_accuracy": 0.89,
 "processing_speed": "ELITE"
 }
 }
 
 # Determine overall health
 if not cortex and not orchestrator:
 health_status["status"] = "DEGRADED_OPERATION"
 elif not cortex:
 health_status["status"] = "LEGACY_OPERATION"
 
 return health_status
