#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Game Environment Context Analyzer
============================================================

Advanced game environment analysis system that evaluates contextual factors
affecting basketball game outcomes including venue, schedule, weather, and
situational factors for both NBA and WNBA.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json

logger = logging.getLogger("GameEnvironmentContext")

class VenueType(Enum):
    HOME = "HOME"
    AWAY = "AWAY"
    NEUTRAL = "NEUTRAL"

class GameImportance(Enum):
    REGULAR = "REGULAR"
    RIVALRY = "RIVALRY"
    PLAYOFF_RACE = "PLAYOFF_RACE"
    PLAYOFFS = "PLAYOFFS"
    FINALS = "FINALS"

@dataclass
class GameContext:
    """Comprehensive game context information"""
    game_id: str
    home_team_id: str
    away_team_id: str
    game_date: datetime
    venue_name: str
    
    # Schedule context
    home_rest_days: int = 0
    away_rest_days: int = 0
    home_back_to_back: bool = False
    away_back_to_back: bool = False
    home_travel_distance: float = 0.0
    away_travel_distance: float = 0.0
    
    # Venue factors
    altitude: float = 0.0
    venue_capacity: int = 0
    expected_attendance: float = 0.0
    home_court_advantage: float = 0.0
    
    # Situational factors
    game_importance: GameImportance = GameImportance.REGULAR
    playoff_implications: bool = False
    rivalry_game: bool = False
    national_tv: bool = False
    
    # Environmental scores
    schedule_advantage_home: float = 0.0
    schedule_advantage_away: float = 0.0
    venue_impact_score: float = 0.0
    situational_pressure: float = 0.0

@dataclass
class EnvironmentalFactors:
    """Environmental factors affecting game performance"""
    temperature: Optional[float] = None
    humidity: Optional[float] = None
    air_pressure: Optional[float] = None
    crowd_noise_level: float = 0.0
    referee_crew_rating: float = 0.0
    media_attention: float = 0.0

class GameEnvironmentContextAnalyzer:
    """
    Advanced game environment context analysis system
    """
    
    def __init__(self, db_path: str = "data/basketball_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("GameEnvironmentAnalyzer")
        
        # Venue-specific data (mock data - replace with real venue database)
        self.venue_data = {
            'Madison Square Garden': {
                'capacity': 20789,
                'altitude': 33,
                'home_advantage': 0.58,
                'noise_factor': 0.85
            },
            'Staples Center': {
                'capacity': 20000,
                'altitude': 308,
                'home_advantage': 0.56,
                'noise_factor': 0.75
            },
            'Ball Arena': {
                'capacity': 19520,
                'altitude': 5280,  # Denver altitude
                'home_advantage': 0.62,
                'noise_factor': 0.80
            }
        }
        
        # Travel distance matrix (mock data)
        self.travel_distances = {
            ('LAL', 'BOS'): 2600,
            ('BOS', 'LAL'): 2600,
            ('MIA', 'DEN'): 1800,
            ('DEN', 'MIA'): 1800
        }
    
    def analyze_schedule_factors(self, team_id: str, game_date: datetime, 
                               recent_games: List[Dict]) -> Dict[str, float]:
        """
        Analyze schedule-related factors affecting team performance
        """
        try:
            # Calculate rest days
            if recent_games:
                last_game_date = datetime.fromisoformat(recent_games[0]['date'])
                rest_days = (game_date - last_game_date).days
            else:
                rest_days = 3  # Default rest
            
            # Check for back-to-back games
            back_to_back = rest_days == 1
            
            # Calculate travel fatigue
            travel_distance = 0.0
            if recent_games and len(recent_games) > 1:
                last_venue = recent_games[0].get('venue', '')
                current_venue = recent_games[1].get('venue', '')
                travel_key = (last_venue[:3], current_venue[:3])
                travel_distance = self.travel_distances.get(travel_key, 500)
            
            # Calculate schedule advantage score
            rest_advantage = min(1.0, rest_days / 3.0)  # Optimal rest is 2-3 days
            travel_fatigue = max(0.0, 1.0 - (travel_distance / 3000.0))
            back_to_back_penalty = 0.7 if back_to_back else 1.0
            
            schedule_score = rest_advantage * travel_fatigue * back_to_back_penalty
            
            return {
                'rest_days': rest_days,
                'back_to_back': back_to_back,
                'travel_distance': travel_distance,
                'schedule_advantage': schedule_score,
                'fatigue_factor': 1.0 - schedule_score
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing schedule factors: {e}")
            return {
                'rest_days': 2,
                'back_to_back': False,
                'travel_distance': 500,
                'schedule_advantage': 0.8,
                'fatigue_factor': 0.2
            }
    
    def analyze_venue_impact(self, venue_name: str, home_team_id: str) -> Dict[str, float]:
        """
        Analyze venue-specific factors
        """
        try:
            venue_info = self.venue_data.get(venue_name, {
                'capacity': 18000,
                'altitude': 500,
                'home_advantage': 0.54,
                'noise_factor': 0.70
            })
            
            # Altitude impact (higher altitude affects shooting)
            altitude_factor = 1.0 + (venue_info['altitude'] / 10000.0) * 0.1
            
            # Crowd impact
            crowd_factor = venue_info['noise_factor']
            
            # Home court advantage
            home_advantage = venue_info['home_advantage']
            
            # Calculate overall venue impact
            venue_impact = (altitude_factor * 0.3 + 
                          crowd_factor * 0.4 + 
                          home_advantage * 0.3)
            
            return {
                'altitude': venue_info['altitude'],
                'capacity': venue_info['capacity'],
                'altitude_factor': altitude_factor,
                'crowd_factor': crowd_factor,
                'home_advantage': home_advantage,
                'venue_impact_score': venue_impact
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing venue impact: {e}")
            return {
                'altitude': 500,
                'capacity': 18000,
                'altitude_factor': 1.0,
                'crowd_factor': 0.7,
                'home_advantage': 0.54,
                'venue_impact_score': 0.75
            }
    
    def analyze_situational_pressure(self, game_context: Dict[str, Any]) -> Dict[str, float]:
        """
        Analyze situational pressure factors
        """
        try:
            # Game importance factors
            importance_multipliers = {
                'REGULAR': 1.0,
                'RIVALRY': 1.2,
                'PLAYOFF_RACE': 1.4,
                'PLAYOFFS': 1.8,
                'FINALS': 2.0
            }
            
            game_importance = game_context.get('importance', 'REGULAR')
            importance_factor = importance_multipliers.get(game_importance, 1.0)
            
            # Media attention
            national_tv = game_context.get('national_tv', False)
            media_factor = 1.3 if national_tv else 1.0
            
            # Playoff implications
            playoff_implications = game_context.get('playoff_implications', False)
            playoff_factor = 1.2 if playoff_implications else 1.0
            
            # Calculate overall pressure
            pressure_score = importance_factor * media_factor * playoff_factor
            normalized_pressure = min(2.0, pressure_score) / 2.0
            
            return {
                'game_importance': importance_factor,
                'media_attention': media_factor,
                'playoff_implications': playoff_factor,
                'pressure_score': normalized_pressure,
                'clutch_factor': normalized_pressure * 0.8
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing situational pressure: {e}")
            return {
                'game_importance': 1.0,
                'media_attention': 1.0,
                'playoff_implications': 1.0,
                'pressure_score': 0.5,
                'clutch_factor': 0.4
            }
    
    def get_comprehensive_game_context(self, game_id: str, 
                                     home_team_id: str, 
                                     away_team_id: str,
                                     game_date: datetime,
                                     venue_name: str) -> GameContext:
        """
        Get comprehensive game context analysis
        """
        try:
            # Mock recent games data
            home_recent_games = [
                {'date': (game_date - timedelta(days=2)).isoformat(), 'venue': 'Away Arena'},
                {'date': (game_date - timedelta(days=5)).isoformat(), 'venue': 'Home Arena'}
            ]
            away_recent_games = [
                {'date': (game_date - timedelta(days=1)).isoformat(), 'venue': 'Neutral Arena'},
                {'date': (game_date - timedelta(days=4)).isoformat(), 'venue': 'Away Arena'}
            ]
            
            # Analyze schedule factors
            home_schedule = self.analyze_schedule_factors(home_team_id, game_date, home_recent_games)
            away_schedule = self.analyze_schedule_factors(away_team_id, game_date, away_recent_games)
            
            # Analyze venue impact
            venue_impact = self.analyze_venue_impact(venue_name, home_team_id)
            
            # Analyze situational pressure
            game_context_data = {
                'importance': 'REGULAR',
                'national_tv': False,
                'playoff_implications': False
            }
            pressure_analysis = self.analyze_situational_pressure(game_context_data)
            
            return GameContext(
                game_id=game_id,
                home_team_id=home_team_id,
                away_team_id=away_team_id,
                game_date=game_date,
                venue_name=venue_name,
                home_rest_days=home_schedule['rest_days'],
                away_rest_days=away_schedule['rest_days'],
                home_back_to_back=home_schedule['back_to_back'],
                away_back_to_back=away_schedule['back_to_back'],
                home_travel_distance=home_schedule['travel_distance'],
                away_travel_distance=away_schedule['travel_distance'],
                altitude=venue_impact['altitude'],
                venue_capacity=venue_impact['capacity'],
                home_court_advantage=venue_impact['home_advantage'],
                schedule_advantage_home=home_schedule['schedule_advantage'],
                schedule_advantage_away=away_schedule['schedule_advantage'],
                venue_impact_score=venue_impact['venue_impact_score'],
                situational_pressure=pressure_analysis['pressure_score']
            )
            
        except Exception as e:
            self.logger.error(f"Error getting comprehensive game context: {e}")
            return GameContext(
                game_id=game_id,
                home_team_id=home_team_id,
                away_team_id=away_team_id,
                game_date=game_date,
                venue_name=venue_name
            )
    
    def calculate_environment_impact(self, game_context: GameContext) -> Dict[str, float]:
        """
        Calculate overall environmental impact on game outcome
        """
        try:
            # Home team advantages
            home_advantages = [
                game_context.home_court_advantage,
                game_context.schedule_advantage_home,
                1.0 - game_context.situational_pressure * 0.1  # Home team handles pressure better
            ]
            
            # Away team disadvantages
            away_disadvantages = [
                1.0 - game_context.home_court_advantage,
                game_context.schedule_advantage_away,
                1.0 - game_context.situational_pressure * 0.15  # Away team more affected by pressure
            ]
            
            home_environment_score = np.mean(home_advantages)
            away_environment_score = np.mean(away_disadvantages)
            
            # Calculate impact on spread and total
            spread_impact = (home_environment_score - away_environment_score) * 5.0  # Points impact
            total_impact = (game_context.venue_impact_score - 0.75) * 10.0  # Total points impact
            
            return {
                'home_environment_advantage': home_environment_score,
                'away_environment_disadvantage': 1.0 - away_environment_score,
                'spread_adjustment': spread_impact,
                'total_adjustment': total_impact,
                'confidence_factor': min(home_environment_score, away_environment_score)
            }
            
        except Exception as e:
            self.logger.error(f"Error calculating environment impact: {e}")
            return {
                'home_environment_advantage': 0.54,
                'away_environment_disadvantage': 0.46,
                'spread_adjustment': 0.0,
                'total_adjustment': 0.0,
                'confidence_factor': 0.5
            }
