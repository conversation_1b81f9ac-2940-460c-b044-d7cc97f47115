import os
import sys
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
import pandas as pd
import json
from vault_oracle.wells.nba_api_connector import BasketballDataConnector, APIConnectorConfig
from src.integrations.odds_prediction_integrator import create_odds_prediction_integrator

#!/usr/bin/env python3
"""
Live Real-Time Data Integrator - HYPER MEDUSA NEURAL VAULT
=========================================================

Comprehensive integration of live real-time data from NBA API including:
- Live scoreboards and game status
- Real-time play-by-play updates
- Live box scores and player stats
- Current odds integration
- Live game state tracking

Built upon existing NBA API connector and live data infrastructure.
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# NBA Live API Integration for Ultra-Low Latency Data
try:
    from nba_api.live.nba.endpoints._base import Endpoint
    from nba_api.live.nba.library.http import NBALiveHTTP
    NBA_LIVE_API_AVAILABLE = True
except ImportError:
    NBA_LIVE_API_AVAILABLE = False
    logger.warning("NBA Live API not available - falling back to standard API")

@dataclass
class LiveGameState:
    """Complete live game state data"""
    
    # Game identification
    titan_clash_id: str
    game_id: str
    league: str
    
    # Teams
    home_team: str
    away_team: str
    home_team_id: str
    away_team_id: str
    
    # Current score
    home_score: int = 0
    away_score: int = 0
    
    # Game timing
    period: int = 1
    time_remaining: str = "12:00"
    game_status: str = "SCHEDULED"  # SCHEDULED, LIVE, FINAL
    
    # Live stats
    last_play: Optional[str] = None
    momentum_indicator: float = 0.0
    
    # Timestamps
    last_updated: datetime = field(default_factory=datetime.utcnow)
    game_time: Optional[datetime] = None

@dataclass
class LivePlayByPlay:
    """Live play-by-play data structure"""
    
    play_id: str
    titan_clash_id: str
    period: int
    time_remaining: str
    play_type: str
    description: str
    home_score: int
    away_score: int
    player_id: Optional[str] = None
    team: Optional[str] = None
    impact_score: float = 0.0
    momentum_change: float = 0.0
    timestamp: datetime = field(default_factory=datetime.utcnow)

@dataclass
class LiveBoxScore:
    """Live box score data structure"""
    
    titan_clash_id: str
    player_id: str
    player_name: str
    team: str
    
    # Traditional stats
    minutes: float = 0.0
    points: int = 0
    rebounds: int = 0
    assists: int = 0
    steals: int = 0
    blocks: int = 0
    turnovers: int = 0
    
    # Shooting
    field_goals_made: int = 0
    field_goals_attempted: int = 0
    three_pointers_made: int = 0
    three_pointers_attempted: int = 0
    free_throws_made: int = 0
    free_throws_attempted: int = 0
    
    # Advanced
    plus_minus: float = 0.0
    
    # Timestamps
    last_updated: datetime = field(default_factory=datetime.utcnow)


class NBALiveBoxScore(Endpoint):
    """
    NBA Live API BoxScore endpoint for ultra-low latency live data
    Provides comprehensive real-time player and team statistics
    """
    endpoint_url = "boxscore/boxscore_{game_id}.json"

    def __init__(self, game_id, proxy=None, headers=None, timeout=30, get_request=True):
        self.game_id = game_id
        self.proxy = proxy
        if headers is not None:
            self.headers = headers
        self.timeout = timeout
        if get_request:
            self.get_request()

    def get_request(self):
        """Get live boxscore data from NBA Live API"""
        if not NBA_LIVE_API_AVAILABLE:
            raise ImportError("NBA Live API not available")

        self.nba_response = NBALiveHTTP().send_api_request(
            endpoint=self.endpoint_url.format(game_id=self.game_id),
            parameters={},
            proxy=self.proxy,
            headers=self.headers,
            timeout=self.timeout,
        )
        self.load_response()

    def load_response(self):
        """Load and parse the NBA Live API response"""
        data_sets = self.nba_response.get_dict()
        if "game" in data_sets:
            self.game = Endpoint.DataSet(data=data_sets["game"])
            self.game_details = self.game.get_dict().copy()

            # Parse arena information
            if "arena" in self.game.get_dict():
                self.arena = Endpoint.DataSet(data=data_sets["game"]["arena"])
                self.game_details.pop("arena")

            # Parse officials/referee information
            if "officials" in self.game.get_dict():
                self.officials = Endpoint.DataSet(data=data_sets["game"]["officials"])
                self.game_details.pop("officials")

            # Parse home team data
            if "homeTeam" in self.game.get_dict():
                self.home_team = Endpoint.DataSet(data=data_sets["game"]["homeTeam"])
                self.home_team_player_stats = Endpoint.DataSet(
                    data=data_sets["game"]["homeTeam"]["players"]
                )
                home_team_stats = self.home_team.get_dict().copy()
                home_team_stats.pop("players")
                self.home_team_stats = Endpoint.DataSet(data=home_team_stats)
                self.game_details.pop("homeTeam")

            # Parse away team data
            if "awayTeam" in self.game.get_dict():
                self.away_team = Endpoint.DataSet(data=data_sets["game"]["awayTeam"])
                self.away_team_player_stats = Endpoint.DataSet(
                    data=data_sets["game"]["awayTeam"]["players"]
                )
                away_team_stats = self.away_team.get_dict().copy()
                away_team_stats.pop("players")
                self.away_team_stats = Endpoint.DataSet(data=away_team_stats)
                self.game_details.pop("awayTeam")

            self.game_details = Endpoint.DataSet(data=self.game_details)


class LiveRealTimeDataIntegrator:
    """
    Comprehensive live real-time data integration system
    Built upon existing NBA API infrastructure
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.live_games: Dict[str, LiveGameState] = {}
        self.live_plays: Dict[str, List[LivePlayByPlay]] = {}
        self.live_boxscores: Dict[str, List[LiveBoxScore]] = {}
        
        # Initialize existing components
        self.nba_connector = None
        self.odds_integrator = None
        self._initialize_connectors()
    
    def _initialize_connectors(self):
        """Initialize existing NBA API and odds connectors"""
        try:
            # Use existing NBA API connector with proper config

            # Create config for the connector
            config = APIConnectorConfig(
                default_league_id="00",  # NBA
                max_retries=3,
                base_retry_delay_sec=2.0
            )

            self.nba_connector = BasketballDataConnector(config)
            self.logger.info("✅ NBA API connector initialized")

            # Use existing odds integrator
            self.odds_integrator = create_odds_prediction_integrator()
            self.logger.info("✅ Odds integrator initialized")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize connectors: {e}")
            # Set to None so we can handle gracefully
            self.nba_connector = None
            self.odds_integrator = None
    
    async def get_live_scoreboard(self, date: str = None) -> List[LiveGameState]:
        """Get live scoreboard using existing NBA API infrastructure"""
        try:
            if not date:
                date = datetime.now().strftime("%Y-%m-%d")
            
            self.logger.info(f"🔴 Fetching live scoreboard for {date}")
            
            # Use existing NBA API connector
            games_data = await self.nba_connector.get_games_by_date(date)
            
            live_games = []
            
            if games_data is not None and not games_data.empty:
                for _, game_row in games_data.iterrows():
                    live_game = self._process_scoreboard_game(game_row)
                    if live_game:
                        live_games.append(live_game)
                        # Cache the game state
                        self.live_games[live_game.titan_clash_id] = live_game
            
            self.logger.info(f"✅ Retrieved {len(live_games)} live games")
            return live_games
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get live scoreboard: {e}")
            return []
    
    def _process_scoreboard_game(self, game_row: pd.Series) -> Optional[LiveGameState]:
        """Process game row into LiveGameState"""
        try:
            # Extract game information
            game_id = str(game_row.get('GAME_ID', ''))
            titan_clash_id = f"{game_row.get('VISITOR_TEAM_ABBREVIATION', 'AWAY')}_vs_{game_row.get('HOME_TEAM_ABBREVIATION', 'HOME')}_{game_id}"
            
            live_game = LiveGameState(
                titan_clash_id=titan_clash_id,
                game_id=game_id,
                league="NBA",  # Determine from data if available
                home_team=game_row.get('HOME_TEAM_ABBREVIATION', ''),
                away_team=game_row.get('VISITOR_TEAM_ABBREVIATION', ''),
                home_team_id=str(game_row.get('HOME_TEAM_ID', '')),
                away_team_id=str(game_row.get('VISITOR_TEAM_ID', '')),
                home_score=int(game_row.get('PTS_home', 0)),
                away_score=int(game_row.get('PTS_away', 0)),
                period=int(game_row.get('PERIOD', 1)),
                time_remaining=str(game_row.get('TIME_REMAINING', '12:00')),
                game_status=str(game_row.get('GAME_STATUS_TEXT', 'SCHEDULED')),
                last_updated=datetime.utcnow()
            )
            
            return live_game
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process game row: {e}")
            return None
    
    async def get_live_play_by_play(self, titan_clash_id: str, last_n_plays: int = 20) -> List[LivePlayByPlay]:
        """Get live play-by-play data using existing infrastructure"""
        try:
            self.logger.info(f"📺 Fetching live play-by-play for {titan_clash_id}")
            
            # Extract game_id from titan_clash_id
            game_id = self._extract_game_id_from_titan_clash_id(titan_clash_id)
            
            if not game_id:
                self.logger.warning(f"⚠️ Could not extract game_id from {titan_clash_id}")
                return []
            
            # Use existing NBA API connector for play-by-play
            pbp_data = await self.nba_connector.get_play_by_play_data(game_id)
            
            live_plays = []
            
            if pbp_data is not None and not pbp_data.empty:
                for _, play_row in pbp_data.tail(last_n_plays).iterrows():
                    live_play = self._process_play_by_play_row(play_row, titan_clash_id)
                    if live_play:
                        live_plays.append(live_play)
                
                # Cache the plays
                self.live_plays[titan_clash_id] = live_plays
            
            self.logger.info(f"✅ Retrieved {len(live_plays)} live plays")
            return live_plays
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get live play-by-play: {e}")
            return []
    
    def _process_play_by_play_row(self, play_row: pd.Series, titan_clash_id: str) -> Optional[LivePlayByPlay]:
        """Process play-by-play row into LivePlayByPlay (handles both live API and traditional API formats)"""
        try:
            # Handle live NBA API format
            if 'actionNumber' in play_row:
                play_id = f"{titan_clash_id}_action_{play_row.get('actionNumber', 0)}"

                live_play = LivePlayByPlay(
                    play_id=play_id,
                    titan_clash_id=titan_clash_id,
                    period=int(play_row.get('period', 1)),
                    time_remaining=str(play_row.get('clock', 'PT12M00.00S')).replace('PT', '').replace('S', '').replace('M', ':'),
                    play_type=str(play_row.get('actionType', 'UNKNOWN')),
                    description=str(play_row.get('description', '')),
                    home_score=int(play_row.get('scoreHome', 0)),
                    away_score=int(play_row.get('scoreAway', 0)),
                    player_id=str(play_row.get('personId', '')) if play_row.get('personId') else None,
                    team=str(play_row.get('teamTricode', '')) if play_row.get('teamTricode') else None,
                    impact_score=self._calculate_live_play_impact(play_row),
                    momentum_change=self._calculate_live_momentum_change(play_row),
                    timestamp=datetime.utcnow()
                )
            else:
                # Handle traditional NBA API format
                play_id = f"{titan_clash_id}_play_{play_row.get('EVENTNUM', 0)}"

                live_play = LivePlayByPlay(
                    play_id=play_id,
                    titan_clash_id=titan_clash_id,
                    period=int(play_row.get('PERIOD', 1)),
                    time_remaining=str(play_row.get('PCTIMESTRING', '12:00')),
                    play_type=str(play_row.get('EVENTMSGTYPE', 'UNKNOWN')),
                    description=str(play_row.get('HOMEDESCRIPTION', '') or play_row.get('VISITORDESCRIPTION', '') or play_row.get('NEUTRALDESCRIPTION', '')),
                    home_score=int(play_row.get('SCORE', '0-0').split('-')[1] if '-' in str(play_row.get('SCORE', '0-0')) else 0),
                    away_score=int(play_row.get('SCORE', '0-0').split('-')[0] if '-' in str(play_row.get('SCORE', '0-0')) else 0),
                    player_id=str(play_row.get('PLAYER1_ID', '')) if play_row.get('PLAYER1_ID') else None,
                    team=str(play_row.get('PLAYER1_TEAM_ABBREVIATION', '')) if play_row.get('PLAYER1_TEAM_ABBREVIATION') else None,
                    impact_score=self._calculate_play_impact(play_row),
                    momentum_change=self._calculate_momentum_change(play_row),
                    timestamp=datetime.utcnow()
                )

            return live_play

        except Exception as e:
            self.logger.error(f"❌ Failed to process play row: {e}")
            return None
    
    async def get_live_boxscore(self, titan_clash_id: str) -> List[LiveBoxScore]:
        """Get live box score data using existing infrastructure"""
        try:
            self.logger.info(f"📊 Fetching live box score for {titan_clash_id}")
            
            game_id = self._extract_game_id_from_titan_clash_id(titan_clash_id)
            
            if not game_id:
                return []
            
            # Use existing NBA API connector for box score
            boxscore_data = await self.nba_connector.get_box_score_traditional(game_id)
            
            live_boxscores = []
            
            if boxscore_data is not None and not boxscore_data.empty:
                for _, player_row in boxscore_data.iterrows():
                    live_boxscore = self._process_boxscore_row(player_row, titan_clash_id)
                    if live_boxscore:
                        live_boxscores.append(live_boxscore)
                
                # Cache the box scores
                self.live_boxscores[titan_clash_id] = live_boxscores
            
            self.logger.info(f"✅ Retrieved {len(live_boxscores)} player box scores")
            return live_boxscores
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get live box score: {e}")
            return []
    
    def _process_boxscore_row(self, player_row: pd.Series, titan_clash_id: str) -> Optional[LiveBoxScore]:
        """Process box score row into LiveBoxScore"""
        try:
            live_boxscore = LiveBoxScore(
                titan_clash_id=titan_clash_id,
                player_id=str(player_row.get('PLAYER_ID', '')),
                player_name=str(player_row.get('PLAYER_NAME', '')),
                team=str(player_row.get('TEAM_ABBREVIATION', '')),
                minutes=float(player_row.get('MIN', 0) or 0),
                points=int(player_row.get('PTS', 0) or 0),
                rebounds=int(player_row.get('REB', 0) or 0),
                assists=int(player_row.get('AST', 0) or 0),
                steals=int(player_row.get('STL', 0) or 0),
                blocks=int(player_row.get('BLK', 0) or 0),
                turnovers=int(player_row.get('TOV', 0) or 0),
                field_goals_made=int(player_row.get('FGM', 0) or 0),
                field_goals_attempted=int(player_row.get('FGA', 0) or 0),
                three_pointers_made=int(player_row.get('FG3M', 0) or 0),
                three_pointers_attempted=int(player_row.get('FG3A', 0) or 0),
                free_throws_made=int(player_row.get('FTM', 0) or 0),
                free_throws_attempted=int(player_row.get('FTA', 0) or 0),
                plus_minus=float(player_row.get('PLUS_MINUS', 0) or 0),
                last_updated=datetime.utcnow()
            )
            
            return live_boxscore
            
        except Exception as e:
            self.logger.error(f"❌ Failed to process boxscore row: {e}")
            return None

    async def get_enhanced_live_boxscore(self, titan_clash_id: str) -> Dict[str, Any]:
        """Get enhanced live boxscore using NBA Live API for ultra-low latency"""
        try:
            if not NBA_LIVE_API_AVAILABLE:
                self.logger.warning("NBA Live API not available - using standard boxscore")
                return await self.get_live_boxscore(titan_clash_id)

            # Convert titan_clash_id to NBA game_id format if needed
            game_id = self._convert_to_nba_game_id(titan_clash_id)

            self.logger.info(f"🚀 Getting enhanced live boxscore for {game_id}")

            # Get live boxscore from NBA Live API
            live_boxscore = NBALiveBoxScore(game_id=game_id)

            enhanced_data = {
                'game_details': live_boxscore.game_details.get_dict() if hasattr(live_boxscore, 'game_details') else {},
                'arena': live_boxscore.arena.get_dict() if hasattr(live_boxscore, 'arena') else {},
                'officials': live_boxscore.officials.get_dict() if hasattr(live_boxscore, 'officials') else [],
                'home_team': {
                    'team_info': live_boxscore.home_team.get_dict() if hasattr(live_boxscore, 'home_team') else {},
                    'team_stats': live_boxscore.home_team_stats.get_dict() if hasattr(live_boxscore, 'home_team_stats') else {},
                    'player_stats': live_boxscore.home_team_player_stats.get_dict() if hasattr(live_boxscore, 'home_team_player_stats') else []
                },
                'away_team': {
                    'team_info': live_boxscore.away_team.get_dict() if hasattr(live_boxscore, 'away_team') else {},
                    'team_stats': live_boxscore.away_team_stats.get_dict() if hasattr(live_boxscore, 'away_team_stats') else {},
                    'player_stats': live_boxscore.away_team_player_stats.get_dict() if hasattr(live_boxscore, 'away_team_player_stats') else []
                },
                'advanced_metrics': self._extract_advanced_metrics(live_boxscore),
                'contextual_data': self._extract_contextual_data(live_boxscore),
                'timestamp': datetime.utcnow().isoformat(),
                'data_source': 'NBA_LIVE_API'
            }

            self.logger.info(f"✅ Enhanced live boxscore retrieved with {len(enhanced_data.get('officials', []))} officials")
            return enhanced_data

        except Exception as e:
            self.logger.error(f"❌ Failed to get enhanced live boxscore: {e}")
            # Fallback to standard boxscore
            return await self.get_live_boxscore(titan_clash_id)

    def _convert_to_nba_game_id(self, titan_clash_id: str) -> str:
        """Convert titan_clash_id to NBA game_id format"""
        # If already in NBA format (10 digits), return as-is
        if len(titan_clash_id) == 10 and titan_clash_id.isdigit():
            return titan_clash_id

        # Extract game ID from titan_clash_id if it contains NBA game ID
        # This is a placeholder - implement based on your ID mapping system
        return titan_clash_id.replace('titan_', '').replace('clash_', '')

    def _extract_advanced_metrics(self, live_boxscore) -> Dict[str, Any]:
        """Extract advanced metrics from NBA Live API response"""
        try:
            metrics = {}

            # Extract team-level advanced metrics
            for team_type in ['home', 'away']:
                team_stats = getattr(live_boxscore, f'{team_type}_team_stats', None)
                if team_stats:
                    team_data = team_stats.get_dict()
                    metrics[f'{team_type}_team'] = {
                        'true_shooting_percentage': team_data.get('trueShootingPercentage', 0),
                        'effective_field_goal_percentage': team_data.get('fieldGoalsEffectiveAdjusted', 0),
                        'pace': team_data.get('possessions', 0) / 48 * 48 if team_data.get('possessions') else 0,
                        'offensive_rating': team_data.get('points', 0) / team_data.get('possessions', 1) * 100 if team_data.get('possessions') else 0,
                        'defensive_rating': team_data.get('pointsAgainst', 0) / team_data.get('possessions', 1) * 100 if team_data.get('possessions') else 0,
                        'assist_turnover_ratio': team_data.get('assistsTurnoverRatio', 0),
                        'bench_points': team_data.get('benchPoints', 0),
                        'fast_break_points': team_data.get('pointsFastBreak', 0),
                        'points_in_paint': team_data.get('pointsInThePaint', 0),
                        'second_chance_points': team_data.get('pointsSecondChance', 0)
                    }

            return metrics

        except Exception as e:
            self.logger.error(f"Error extracting advanced metrics: {e}")
            return {}

    def _extract_contextual_data(self, live_boxscore) -> Dict[str, Any]:
        """Extract contextual data from NBA Live API response"""
        try:
            contextual = {}

            # Arena/venue information
            if hasattr(live_boxscore, 'arena'):
                arena_data = live_boxscore.arena.get_dict()
                contextual['venue'] = {
                    'arena_id': arena_data.get('arenaId'),
                    'arena_name': arena_data.get('arenaName'),
                    'city': arena_data.get('arenaCity'),
                    'state': arena_data.get('arenaState'),
                    'timezone': arena_data.get('arenaTimezone'),
                    'capacity': arena_data.get('capacity', 20000)  # Default NBA capacity
                }

            # Officials/referee information
            if hasattr(live_boxscore, 'officials'):
                officials_data = live_boxscore.officials.get_dict()
                contextual['officials'] = []
                if isinstance(officials_data, list):
                    for official in officials_data:
                        contextual['officials'].append({
                            'person_id': official.get('personId'),
                            'name': official.get('name'),
                            'jersey_num': official.get('jerseyNum'),
                            'assignment': official.get('assignment')
                        })

            # Game timing and status
            if hasattr(live_boxscore, 'game_details'):
                game_data = live_boxscore.game_details.get_dict()
                contextual['game_context'] = {
                    'attendance': game_data.get('attendance', 0),
                    'sellout': game_data.get('sellout') == '1',
                    'duration': game_data.get('duration', 0),
                    'regulation_periods': game_data.get('regulationPeriods', 4),
                    'current_period': game_data.get('period', 1),
                    'game_clock': game_data.get('gameClock', 'PT12M00.00S')
                }

            return contextual

        except Exception as e:
            self.logger.error(f"Error extracting contextual data: {e}")
            return {}

    async def get_comprehensive_live_data(self, titan_clash_id: str) -> Dict[str, Any]:
        """Get comprehensive live data for a game"""
        try:
            self.logger.info(f"🔄 Fetching comprehensive live data for {titan_clash_id}")
            
            # Get all live data components
            game_state = self.live_games.get(titan_clash_id)
            play_by_play = await self.get_live_play_by_play(titan_clash_id)

            # Use enhanced boxscore if NBA Live API is available
            if NBA_LIVE_API_AVAILABLE:
                enhanced_boxscore = await self.get_enhanced_live_boxscore(titan_clash_id)
                boxscore_data = enhanced_boxscore
                data_source = "NBA_LIVE_API_ENHANCED"
            else:
                boxscore = await self.get_live_boxscore(titan_clash_id)
                boxscore_data = [box.__dict__ for box in boxscore]
                data_source = "STANDARD_API"
            
            # Get live odds if available
            odds_data = None
            if self.odds_integrator:
                try:
                    league = game_state.league if game_state else "NBA"
                    odds_data = await self.odds_integrator.get_live_odds_for_game(titan_clash_id, league)
                except Exception as e:
                    self.logger.warning(f"⚠️ Could not get odds for {titan_clash_id}: {e}")
            
            comprehensive_data = {
                'titan_clash_id': titan_clash_id,
                'game_state': game_state.__dict__ if game_state else None,
                'play_by_play': [play.__dict__ for play in play_by_play],
                'boxscore': boxscore_data,
                'odds_data': odds_data,
                'last_updated': datetime.utcnow().isoformat(),
                'data_source': data_source,
                'data_completeness': {
                    'game_state': game_state is not None,
                    'play_by_play': len(play_by_play) > 0,
                    'boxscore': bool(boxscore_data),
                    'odds': odds_data is not None,
                    'enhanced_metrics': NBA_LIVE_API_AVAILABLE,
                    'officials_data': NBA_LIVE_API_AVAILABLE and isinstance(boxscore_data, dict) and 'officials' in boxscore_data,
                    'venue_data': NBA_LIVE_API_AVAILABLE and isinstance(boxscore_data, dict) and 'arena' in boxscore_data
                },
                'capabilities': {
                    'ultra_low_latency': NBA_LIVE_API_AVAILABLE,
                    'advanced_metrics': NBA_LIVE_API_AVAILABLE,
                    'referee_tracking': NBA_LIVE_API_AVAILABLE,
                    'venue_context': NBA_LIVE_API_AVAILABLE,
                    'true_shooting_percentage': NBA_LIVE_API_AVAILABLE,
                    'effective_field_goal_percentage': NBA_LIVE_API_AVAILABLE,
                    'plus_minus_tracking': NBA_LIVE_API_AVAILABLE
                }
            }
            
            # Log enhanced capabilities
            if NBA_LIVE_API_AVAILABLE:
                officials_count = len(boxscore_data.get('officials', [])) if isinstance(boxscore_data, dict) else 0
                self.logger.info(f"✅ Enhanced live data retrieved for {titan_clash_id} - {len(play_by_play)} plays, {officials_count} officials, advanced metrics enabled")
            else:
                boxscore_count = len(boxscore_data) if isinstance(boxscore_data, list) else 0
                self.logger.info(f"✅ Standard live data retrieved for {titan_clash_id} - {len(play_by_play)} plays, {boxscore_count} players")

            return comprehensive_data
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get comprehensive live data: {e}")
            return {}
    
    def _extract_game_id_from_titan_clash_id(self, titan_clash_id: str) -> Optional[str]:
        """Extract NBA API game_id from titan_clash_id"""
        try:
            # titan_clash_id format: "TEAM1_vs_TEAM2_GAME_ID"
            parts = titan_clash_id.split('_')
            if len(parts) >= 4:
                return parts[-1]  # Last part should be game_id
            return None
        except:
            return None
    
    def _calculate_play_impact(self, play_row: pd.Series) -> float:
        """Calculate impact score for a play"""
        # Simple impact calculation - can be enhanced
        event_type = play_row.get('EVENTMSGTYPE', 0)
        
        impact_scores = {
            1: 2.0,   # Made shot
            2: -1.0,  # Missed shot
            3: 1.5,   # Free throw
            4: 1.0,   # Rebound
            5: 2.5,   # Turnover
            6: 1.5,   # Foul
            8: 1.0,   # Substitution
            10: 0.5,  # Jump ball
            12: 3.0,  # Start period
            13: 3.0   # End period
        }
        
        return impact_scores.get(event_type, 0.0)
    
    def _calculate_momentum_change(self, play_row: pd.Series) -> float:
        """Calculate momentum change for a play (traditional API format)"""
        # Simple momentum calculation - can be enhanced
        event_type = play_row.get('EVENTMSGTYPE', 0)

        if event_type == 1:  # Made shot
            return 1.0
        elif event_type == 2:  # Missed shot
            return -0.5
        elif event_type == 5:  # Turnover
            return -2.0
        else:
            return 0.0

    def _calculate_live_play_impact(self, play_row: pd.Series) -> float:
        """Calculate impact score for a play (live API format)"""
        action_type = play_row.get('actionType', '')
        shot_result = play_row.get('shotResult', '')

        # Live API impact scoring
        if action_type == 'shot' and shot_result == 'Made':
            return 2.5
        elif action_type == 'shot' and shot_result == 'Missed':
            return -1.0
        elif action_type == 'turnover':
            return -2.0
        elif action_type == 'steal':
            return 2.0
        elif action_type == 'block':
            return 1.5
        elif action_type == 'rebound':
            return 1.0
        elif action_type == 'assist':
            return 1.5
        else:
            return 0.5

    def _calculate_live_momentum_change(self, play_row: pd.Series) -> float:
        """Calculate momentum change for a play (live API format)"""
        action_type = play_row.get('actionType', '')
        shot_result = play_row.get('shotResult', '')

        # Live API momentum calculation
        if action_type == 'shot' and shot_result == 'Made':
            return 1.5
        elif action_type == 'shot' and shot_result == 'Missed':
            return -0.5
        elif action_type == 'turnover':
            return -2.5
        elif action_type == 'steal':
            return 2.0
        elif action_type == 'block':
            return 1.8
        elif action_type == 'foul':
            return -1.0
        else:
            return 0.0

# Factory function for easy import
def create_live_realtime_data_integrator() -> LiveRealTimeDataIntegrator:
    """Create and return a LiveRealTimeDataIntegrator instance"""
    return LiveRealTimeDataIntegrator()
