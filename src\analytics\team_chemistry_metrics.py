#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Team Chemistry Metrics
=================================================

Advanced team chemistry analysis system that evaluates player synergies,
lineup effectiveness, and team cohesion metrics for both NBA and WNBA.
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import sqlite3
import json

logger = logging.getLogger("TeamChemistryMetrics")

@dataclass
class PlayerSynergy:
    """Player synergy metrics between two players"""
    player1_id: str
    player2_id: str
    synergy_score: float = 0.0
    games_together: int = 0
    minutes_together: float = 0.0
    plus_minus_together: float = 0.0
    offensive_synergy: float = 0.0
    defensive_synergy: float = 0.0
    chemistry_rating: str = "Unknown"

@dataclass
class LineupChemistry:
    """Chemistry metrics for a specific lineup"""
    lineup_id: str
    player_ids: List[str]
    chemistry_score: float = 0.0
    games_played: int = 0
    minutes_played: float = 0.0
    net_rating: float = 0.0
    offensive_rating: float = 0.0
    defensive_rating: float = 0.0
    synergy_factors: Dict[str, float] = field(default_factory=dict)

@dataclass
class TeamChemistryProfile:
    """Overall team chemistry profile"""
    team_id: str
    season: str
    overall_chemistry: float = 0.0
    best_lineup: Optional[LineupChemistry] = None
    worst_lineup: Optional[LineupChemistry] = None
    chemistry_trends: List[float] = field(default_factory=list)
    key_synergies: List[PlayerSynergy] = field(default_factory=list)
    chemistry_factors: Dict[str, float] = field(default_factory=dict)

class TeamChemistryMetrics:
    """
    Advanced team chemistry analysis system
    """
    
    def __init__(self, db_path: str = "data/basketball_data.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("TeamChemistryMetrics")
        
        # Chemistry calculation weights
        self.chemistry_weights = {
            'plus_minus': 0.3,
            'assist_ratio': 0.25,
            'turnover_ratio': 0.2,
            'defensive_stops': 0.15,
            'offensive_flow': 0.1
        }
    
    def calculate_player_synergy(self, player1_id: str, player2_id: str, 
                               season: str = "2023-24") -> PlayerSynergy:
        """
        Calculate synergy metrics between two players
        """
        try:
            # Mock data - replace with real database queries
            mock_synergy_data = {
                'games_together': 45,
                'minutes_together': 1250.5,
                'plus_minus_together': 8.7,
                'assists_to_each_other': 3.2,
                'turnovers_together': 1.8,
                'defensive_stops_together': 2.1
            }
            
            # Calculate synergy components
            plus_minus_factor = min(1.0, max(0.0, (mock_synergy_data['plus_minus_together'] + 20) / 40))
            assist_factor = min(1.0, mock_synergy_data['assists_to_each_other'] / 5.0)
            turnover_factor = max(0.0, 1.0 - (mock_synergy_data['turnovers_together'] / 3.0))
            defensive_factor = min(1.0, mock_synergy_data['defensive_stops_together'] / 3.0)
            
            # Calculate overall synergy score
            synergy_score = (
                plus_minus_factor * self.chemistry_weights['plus_minus'] +
                assist_factor * self.chemistry_weights['assist_ratio'] +
                turnover_factor * self.chemistry_weights['turnover_ratio'] +
                defensive_factor * self.chemistry_weights['defensive_stops']
            )
            
            # Determine chemistry rating
            if synergy_score >= 0.8:
                chemistry_rating = "Excellent"
            elif synergy_score >= 0.6:
                chemistry_rating = "Good"
            elif synergy_score >= 0.4:
                chemistry_rating = "Average"
            elif synergy_score >= 0.2:
                chemistry_rating = "Poor"
            else:
                chemistry_rating = "Very Poor"
            
            return PlayerSynergy(
                player1_id=player1_id,
                player2_id=player2_id,
                synergy_score=synergy_score,
                games_together=mock_synergy_data['games_together'],
                minutes_together=mock_synergy_data['minutes_together'],
                plus_minus_together=mock_synergy_data['plus_minus_together'],
                offensive_synergy=assist_factor,
                defensive_synergy=defensive_factor,
                chemistry_rating=chemistry_rating
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating synergy for {player1_id}-{player2_id}: {e}")
            return PlayerSynergy(player1_id=player1_id, player2_id=player2_id)
    
    def analyze_lineup_chemistry(self, player_ids: List[str], 
                               season: str = "2023-24") -> LineupChemistry:
        """
        Analyze chemistry for a specific lineup
        """
        try:
            lineup_id = "_".join(sorted(player_ids))
            
            # Mock lineup data - replace with real database queries
            mock_lineup_data = {
                'games_played': 25,
                'minutes_played': 450.0,
                'points_for': 285,
                'points_against': 270,
                'assists': 65,
                'turnovers': 35,
                'defensive_stops': 45
            }
            
            # Calculate ratings
            possessions = mock_lineup_data['minutes_played'] / 48 * 100  # Estimate possessions
            offensive_rating = (mock_lineup_data['points_for'] / possessions) * 100 if possessions > 0 else 0
            defensive_rating = (mock_lineup_data['points_against'] / possessions) * 100 if possessions > 0 else 0
            net_rating = offensive_rating - defensive_rating
            
            # Calculate chemistry factors
            assist_rate = mock_lineup_data['assists'] / max(1, mock_lineup_data['points_for'] / 2)
            turnover_rate = mock_lineup_data['turnovers'] / possessions if possessions > 0 else 0
            defensive_efficiency = mock_lineup_data['defensive_stops'] / possessions if possessions > 0 else 0
            
            # Calculate overall chemistry score
            chemistry_score = (
                min(1.0, max(0.0, (net_rating + 20) / 40)) * 0.4 +
                min(1.0, assist_rate / 0.6) * 0.3 +
                max(0.0, 1.0 - (turnover_rate / 0.15)) * 0.2 +
                min(1.0, defensive_efficiency / 0.25) * 0.1
            )
            
            synergy_factors = {
                'ball_movement': assist_rate,
                'turnover_control': 1.0 - turnover_rate,
                'defensive_coordination': defensive_efficiency,
                'offensive_flow': offensive_rating / 110.0,
                'net_impact': (net_rating + 20) / 40
            }
            
            return LineupChemistry(
                lineup_id=lineup_id,
                player_ids=player_ids,
                chemistry_score=chemistry_score,
                games_played=mock_lineup_data['games_played'],
                minutes_played=mock_lineup_data['minutes_played'],
                net_rating=net_rating,
                offensive_rating=offensive_rating,
                defensive_rating=defensive_rating,
                synergy_factors=synergy_factors
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing lineup chemistry: {e}")
            return LineupChemistry(lineup_id="_".join(sorted(player_ids)), player_ids=player_ids)
    
    def get_team_chemistry_profile(self, team_id: str, 
                                 season: str = "2023-24") -> TeamChemistryProfile:
        """
        Get comprehensive team chemistry profile
        """
        try:
            # Mock team chemistry data
            mock_lineups = [
                ['player1', 'player2', 'player3', 'player4', 'player5'],
                ['player1', 'player2', 'player3', 'player6', 'player7'],
                ['player2', 'player3', 'player4', 'player8', 'player9']
            ]
            
            # Analyze all lineups
            lineup_chemistries = []
            for lineup in mock_lineups:
                chemistry = self.analyze_lineup_chemistry(lineup, season)
                lineup_chemistries.append(chemistry)
            
            # Find best and worst lineups
            best_lineup = max(lineup_chemistries, key=lambda x: x.chemistry_score)
            worst_lineup = min(lineup_chemistries, key=lambda x: x.chemistry_score)
            
            # Calculate overall team chemistry
            overall_chemistry = np.mean([lc.chemistry_score for lc in lineup_chemistries])
            
            # Generate key synergies
            key_synergies = [
                self.calculate_player_synergy('player1', 'player2', season),
                self.calculate_player_synergy('player2', 'player3', season),
                self.calculate_player_synergy('player3', 'player4', season)
            ]
            
            # Chemistry factors
            chemistry_factors = {
                'lineup_consistency': 0.75,
                'role_clarity': 0.82,
                'communication': 0.78,
                'trust_factor': 0.85,
                'adaptability': 0.73
            }
            
            return TeamChemistryProfile(
                team_id=team_id,
                season=season,
                overall_chemistry=overall_chemistry,
                best_lineup=best_lineup,
                worst_lineup=worst_lineup,
                chemistry_trends=[0.65, 0.68, 0.72, 0.75, 0.78],  # Mock trend data
                key_synergies=key_synergies,
                chemistry_factors=chemistry_factors
            )
            
        except Exception as e:
            self.logger.error(f"Error getting team chemistry profile for {team_id}: {e}")
            return TeamChemistryProfile(team_id=team_id, season=season)
    
    def predict_lineup_success(self, player_ids: List[str], 
                             opponent_strength: float = 0.5) -> Dict[str, float]:
        """
        Predict success probability for a lineup against opponent
        """
        try:
            lineup_chemistry = self.analyze_lineup_chemistry(player_ids)
            
            # Base success probability from chemistry
            base_probability = lineup_chemistry.chemistry_score
            
            # Adjust for opponent strength
            opponent_adjustment = 1.0 - (opponent_strength * 0.3)
            adjusted_probability = base_probability * opponent_adjustment
            
            # Calculate specific outcome probabilities
            win_probability = min(0.95, max(0.05, adjusted_probability))
            cover_spread_probability = win_probability * 0.85
            over_total_probability = 0.5 + (lineup_chemistry.offensive_rating - 110) / 200
            
            return {
                'win_probability': win_probability,
                'cover_spread_probability': cover_spread_probability,
                'over_total_probability': max(0.1, min(0.9, over_total_probability)),
                'chemistry_confidence': lineup_chemistry.chemistry_score
            }
            
        except Exception as e:
            self.logger.error(f"Error predicting lineup success: {e}")
            return {
                'win_probability': 0.5,
                'cover_spread_probability': 0.5,
                'over_total_probability': 0.5,
                'chemistry_confidence': 0.5
            }
