#!/usr/bin/env python3
"""
Quick Phase 2 verification after Batch 1 cleanup
"""

import os
import re
from pathlib import Path

def quick_mock_count():
    """Quick count of remaining mock implementations"""
    project_root = Path(".")
    
    mock_count = 0
    fallback_count = 0
    files_with_issues = 0
    
    production_dirs = ['src/', 'backend/', 'vault_oracle/']
    
    for prod_dir in production_dirs:
        dir_path = project_root / prod_dir
        if dir_path.exists():
            for file_path in dir_path.rglob("*.py"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    file_mocks = len(re.findall(r'mock|Mock', content))
                    file_fallbacks = len(re.findall(r'fallback|Fallback', content))
                    
                    if file_mocks > 0 or file_fallbacks > 0:
                        files_with_issues += 1
                        mock_count += file_mocks
                        fallback_count += file_fallbacks
                        
                except Exception:
                    pass
    
    print(f"🔍 QUICK PHASE 2 CHECK AFTER BATCH 1 CLEANUP")
    print(f"=" * 50)
    print(f"Files with mock/fallback issues: {files_with_issues}")
    print(f"Total mock implementations: {mock_count}")
    print(f"Total fallback implementations: {fallback_count}")
    print(f"Total issues: {mock_count + fallback_count}")
    print(f"=" * 50)
    
    if files_with_issues < 200:
        print("✅ SIGNIFICANT IMPROVEMENT! Ready for Batch 2 cleanup")
    else:
        print("🔄 More cleanup needed")

if __name__ == "__main__":
    quick_mock_count()
