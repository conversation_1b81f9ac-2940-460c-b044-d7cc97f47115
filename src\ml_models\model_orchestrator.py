import asyncio
import logging
import os
import time
import json
import hashlib
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional, Tuple, Callable, Coroutine
from enum import Enum
import configparser
from ..predictions.enhanced_prediction_engine import EnhancedPredictionEngine
from ..predictions.advanced_player_performance import AdvancedPlayerPerformance
from ..predictions.professional_market_integration import ProfessionalMarketIntegration
from ..neural_cortex.ultimate_prediction_engine import UltimatePredictionEngine

#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Elite ML Models Orchestrator
Central coordinator for all prediction engines and ML models

Features:
- Thread-safe async operations
- Real-time alerting integration
- Configurable parameters
- Graceful degradation
- Audit logging
- Metrics endpoint
- Test hooks
- Comprehensive type hints
"""


# Import our existing prediction engines

logger = logging.getLogger(__name__)

# Try to import real alerting system, fallback to mock
try:
    from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
    from firebase_production_system import firebase_manager, FirebaseAlerts
    ALERTING_ENABLED = True
    logger.info("✅ Real alerting system available for model orchestrator")
except ImportError:
    ALERTING_ENABLED = False
    logger.warning("⚠️ Real alerting system unavailable, using mock alerts")

    # Fallback alerting class
    class ExpertMessagingOrchestrator:
        def send_alert(self, subject, message, recipients=None):
            logger.info(f"⚠️ MOCK ALERT: {subject} - {message}")

    class firebase_manager:
        @staticmethod
        def send_system_alert(title, message, level="INFO", data=None):
            logger.info(f"⚠️ MOCK FIREBASE: {level} - {title}: {message}")

class PredictionType(Enum):
    """Enumeration of supported prediction types"""
    GAME_WINNER = 'game_winner'
    POINT_SPREAD = 'point_spread'
    TOTAL_POINTS = 'total_points'
    PLAYER_PROP = 'player_prop'
    PLAYER_PERFORMANCE = 'player_performance'
    VALUE_BET = 'value_bet'

@dataclass
class PredictionRequest:
    """
    Elite-grade prediction request structure with validation
    """
    request_id: str
    vault_user_id: Optional[str] = None
    titan_clash_id: str
    prediction_type: str
    target: Optional[str] = None
    stat_type: Optional[str] = None
    line_value: Optional[float] = None
    bet_amount: Optional[float] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)
    priority: int = 3
    test_scenario: Optional[str] = None
    
    def __post_init__(self):
        if not isinstance(self.titan_clash_id, str) or len(self.titan_clash_id) < 5:
            raise ValueError("Invalid titan_clash_id format")
        try:
            PredictionType(self.prediction_type)
        except ValueError:
            raise ValueError(f"Invalid prediction type: {self.prediction_type}")
        if self.prediction_type == PredictionType.PLAYER_PROP.value:
            if not self.target or not self.stat_type or not self.line_value:
                raise ValueError("Player prop requires target, stat_type, and line_value")
        if self.bet_amount is not None and self.bet_amount < 0:
            raise ValueError("Bet amount cannot be negative")

@dataclass
class PredictionResponse:
    """
    Elite unified prediction response with enhanced metadata
    """
    request_id: str
    prediction_type: str
    probability: float
    confidence: float
    recommended_bet: Optional[float]
    value_score: float
    supporting_data: Dict[str, Any]
    engine_used: str
    processing_time_ms: float
    timestamp: datetime
    cache_status: str = 'live'
    engine_version: str = '1.0'
    prediction_quality: float = 0.0
    degradation_mode: bool = False

class EngineStatus(Enum):
    READY = 'ready'
    DEGRADED = 'degraded'
    OVERLOADED = 'overloaded'
    OFFLINE = 'offline'
    COOLING_DOWN = 'cooling_down'

class SystemHealth(Enum):
    OPERATIONAL = 'operational'
    DEGRADED = 'degraded'
    CRITICAL = 'critical'
    MAINTENANCE = 'maintenance'

class MLModelsOrchestrator:
    """Elite Central Coordinator for all ML models and prediction engines"""

    def __init__(self, config_file: str = 'orchestrator.cfg'):
        self.engines = {}
        self.model_status = {}
        self.prediction_cache = {}
        self.engine_health = {}
        self.circuit_breakers = {}
        self.performance_metrics = {}
        self.config = self._load_configuration(config_file)
        self.alert_system = self._initialize_alert_system()
        self.initialize_engines()
        self.cache_lock = asyncio.Lock()
        self.status_lock = asyncio.Lock()
        self.audit_lock = asyncio.Lock()
        self.audit_log = []
        self.audit_log_max = self.config.getint('Audit', 'max_entries', fallback=1000)
        self._init_metrics()

    def _load_configuration(self, config_file: str) -> configparser.ConfigParser:
        config = configparser.ConfigParser()
        defaults = {
            'Cache': {
                'ttl_minutes': '15',
                'size_limit': '1000',
                'hit_sample_rate': '0.1'
            },
            'Engines': {
                'enhanced_version': '2.3.1',
                'player_performance_version': '1.8.4',
                'market_integration_version': '3.2.0',
                'neural_cortex_version': '4.0.0-alpha'
            },
            'Alerting': {
                'engine_failure_recipients': '<EMAIL>',
                'degraded_mode_recipients': '<EMAIL>'
            },
            'Audit': {
                'max_entries': '1000',
                'retention_days': '7'
            },
            'System': {
                'degradation_threshold': '3'
            }
        }
        for section, options in defaults.items():
            if not config.has_section(section):
                config.add_section(section)
            for key, value in options.items():
                config.set(section, key, value)
        if os.path.exists(config_file):
            config.read(config_file)
            logger.info(f"Loaded configuration from {config_file}")
        else:
            logger.warning(f"Config file {config_file} not found, using defaults")
        return config

    def _initialize_alert_system(self) -> Optional[Callable]:
        if not ALERTING_ENABLED:
            logger.info("Alerting system disabled")
            return None
        try:
            alertor = ExpertMessagingOrchestrator()
            logger.info("Expert alerting system initialized")
            return alertor.send_critical_alert
        except Exception as e:
            logger.error(f"Failed to initialize alerting: {str(e)}")
            return None

    def _init_metrics(self) -> None:
        self.performance_metrics = {
            'requests_total': 0,
            'requests_by_type': {pt.value: 0 for pt in PredictionType},
            'cache_hits': 0,
            'cache_misses': 0,
            'engine_errors': 0,
            'degraded_responses': 0,
            'last_alert_sent': None
        }

    def initialize_engines(self) -> None:
        logger.info("⚡ HYPER MEDUSA VAULT: Initializing elite prediction engines")
        engine_registry = {
            'enhanced': {
                'class': EnhancedPredictionEngine,
                'version': self.config.get('Engines', 'enhanced_version')
            },
            'player_performance': {
                'class': AdvancedPlayerPerformance,
                'version': self.config.get('Engines', 'player_performance_version')
            },
            'market_integration': {
                'class': ProfessionalMarketIntegration,
                'version': self.config.get('Engines', 'market_integration_version')
            },
            'neural_cortex': {
                'class': UltimatePredictionEngine,
                'version': self.config.get('Engines', 'neural_cortex_version')
            }
        }
        for engine_name, engine_config in engine_registry.items():
            try:
                test_mode = os.getenv('TEST_MODE', 'false').lower() == 'true'
                self.engines[engine_name] = engine_config['class'](
                    version=engine_config['version'],
                    test_mode=test_mode
                )
                async def set_status():
                    async with self.status_lock:
                        self.model_status[engine_name] = {
                            'status': EngineStatus.READY,
                            'last_used': None,
                            'version': engine_config['version'],
                            'performance': 1.0,
                            'error_count': 0,
                            'request_count': 0
                        }
                        self.circuit_breakers[engine_name] = {
                            'failures': 0, 
                            'last_failure': None
                        }
                asyncio.create_task(set_status())
                logger.info(f"⚙️  Engine initialized: {engine_name} v{engine_config['version']}")
            except Exception as e:
                logger.critical(f"🚨 Engine initialization failed for {engine_name}: {str(e)}")
                async def set_offline():
                    async with self.status_lock:
                        self.model_status[engine_name] = {
                            'status': EngineStatus.OFFLINE,
                            'error': str(e)
                        }
                asyncio.create_task(set_offline())

    async def predict_game_outcome(self, request: PredictionRequest) -> PredictionResponse:
        """Predict game winner with confidence scoring"""

        start_time = datetime.now()

        # Use Enhanced Prediction Engine for game outcomes
        engine = self.engines['enhanced']

        try:
            # Convert request to engine format
            prediction_data = await engine.predict({
                'titan_clash_id': request.titan_clash_id,
                'prediction_type': 'game_winner',
                'timestamp': request.timestamp
            })

            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000

            # Update engine status
            self.model_status['enhanced']['last_used'] = datetime.now()

            response = PredictionResponse(
                request_id=request.request_id,
                prediction_type='game_winner',
                probability=prediction_data.get('probability', 0.5),
                confidence=prediction_data.get('confidence', 0.5),
                recommended_bet=self._calculate_kelly_bet(request, prediction_data),
                value_score=prediction_data.get('value_score', 0.0),
                supporting_data=prediction_data,
                engine_used='Enhanced Prediction Engine',
                processing_time_ms=processing_time,
                timestamp=datetime.now()
            )

            return response

        except Exception as e:
            logger.error(f" Game outcome prediction failed: {e}")
            return self._create_error_response(request, str(e))

    async def predict_player_prop(self, request: PredictionRequest) -> PredictionResponse:
        """Predict player prop with advanced player modeling"""

        start_time = datetime.now()

        # Use Advanced Player Performance engine
        engine = self.engines['player_performance']

        try:
            prediction_data = await engine.predict({
                'hero_id': request.target,
                'stat_type': request.stat_type,
                'line_value': request.line_value,
                'titan_clash_id': request.titan_clash_id
            })

            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.model_status['player_performance']['last_used'] = datetime.now()

            response = PredictionResponse(
                request_id=request.request_id,
                prediction_type='player_prop',
                probability=prediction_data.get('probability', 0.5),
                confidence=prediction_data.get('confidence', 0.5),
                recommended_bet=self._calculate_kelly_bet(request, prediction_data),
                value_score=prediction_data.get('value_score', 0.0),
                supporting_data=prediction_data,
                engine_used='Advanced Player Performance',
                processing_time_ms=processing_time,
                timestamp=datetime.now()
            )

            return response

        except Exception as e:
            logger.error(f" Player prop prediction failed: {e}")
            return self._create_error_response(request, str(e))

    async def detect_value_bets(self, game_ids: List[str]) -> List[Dict[str, Any]]:
        """Detect value betting opportunities across multiple games"""

        engine = self.engines['market_integration']

        try:
            value_opportunities = []

            for titan_clash_id in game_ids:
                game_analysis = await engine.analyze_market_value({
                    'titan_clash_id': titan_clash_id,
                    'analysis_type': 'comprehensive'
                })

                if game_analysis.get('value_score', 0) > 0.6:
                    value_opportunities.append({
                        'titan_clash_id': titan_clash_id,
                        'opportunity_type': game_analysis.get('best_bet_type'),
                        'value_score': game_analysis.get('value_score'),
                        'expected_roi': game_analysis.get('expected_roi'),
                        'confidence': game_analysis.get('confidence'),
                        'recommendation': game_analysis.get('recommendation')
                    })

            self.model_status['market_integration']['last_used'] = datetime.now()
            return value_opportunities

        except Exception as e:
            logger.error(f" Value bet detection failed: {e}")
            return []

    async def get_neural_cortex_analysis(self, request: PredictionRequest) -> Dict[str, Any]:
        """Get advanced neural cortex analysis for complex predictions"""

        engine = self.engines['neural_cortex']

        try:
            neural_analysis = await engine.ultimate_prediction({
                'titan_clash_id': request.titan_clash_id,
                'analysis_depth': 'maximum',
                'include_spatial': True,
                'include_expert_rivalry': True
            })

            self.model_status['neural_cortex']['last_used'] = datetime.now()
            return neural_analysis

        except Exception as e:
            logger.error(f" Neural cortex analysis failed: {e}")
            return {}

    def _calculate_kelly_bet(self, request: PredictionRequest, prediction_data: Dict) -> Optional[float]:
        """Calculate optimal bet size using Kelly criterion"""

        if not request.bet_amount or not prediction_data.get('probability'):
            return None

        probability = prediction_data['probability']
        # Assume standard -110 odds for now (can be enhanced with real odds)
        odds_decimal = 1.91

        # Kelly formula: f = (bp - q) / b
        # where b = odds-1, p = probability, q = 1-p
        b = odds_decimal - 1
        p = probability
        q = 1 - p

        kelly_fraction = (b * p - q) / b

        # Apply conservative scaling (25% of Kelly)
        conservative_kelly = max(0, kelly_fraction * 0.25)

        recommended_bet = request.bet_amount * conservative_kelly

        return round(recommended_bet, 2)

    def _create_error_response(self, request: PredictionRequest, error_msg: str) -> PredictionResponse:
        """Create error response for failed predictions"""

        return PredictionResponse(
            request_id=request.request_id,
            prediction_type=request.prediction_type,
            probability=0.5, # Neutral probability for errors
            confidence=0.0, # Zero confidence for errors
            recommended_bet=None,
            value_score=0.0,
            supporting_data={'error': error_msg},
            engine_used='Error Handler',
            processing_time_ms=0.0,
            timestamp=datetime.now()
        )

    def _handle_engine_error(self, engine_name: str, error_msg: str) -> None:
        status = self.model_status[engine_name]
        # ...existing error count/circuit breaker logic...
        previous_status = status['status']
        # ...status update logic...
        if status['status'] in ['offline', 'degraded'] and status['status'] != previous_status:
            self._send_engine_alert(engine_name, status['status'], error_msg)

    def _send_engine_alert(self, engine_name: str, status, error_msg: str) -> None:
        """Send critical engine alert"""
        if not hasattr(self, 'alert_system') or not self.alert_system:
            return

        alert_message = (
            f"🚨 Engine {engine_name} status changed to {status}\n"
            f"• Error: {error_msg}\n"
            f"• Timestamp: {datetime.utcnow().isoformat()}\n"
            f"• System impact: {self._engine_impact(engine_name)}"
        )
        recipients = None
        if hasattr(self, 'config'):
            recipients = self.config.get('Alerting', 'engine_failure_recipients', fallback=None)
        self._send_alert(
            f"ENGINE {str(status).upper()} - {engine_name}",
            alert_message,
            recipients
        )

    def _engine_impact(self, engine_name: str) -> str:
        # Example impact assessment
        if engine_name == 'neural_cortex':
            return "Fallback to other engines; complex predictions may be degraded."
        return "Requests will be routed to fallback engine."

    def _send_alert(self, subject: str, message: str, recipients: str) -> None:
        # Example: Use your real messaging orchestrator
        try:
            alertor = ExpertMessagingOrchestrator()
            alertor.send_critical_alert(subject, message, recipients)
        except Exception as e:
            logger.error(f"Failed to send alert: {e}")

    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""

        return {
            'engines_available': len(self.engines),
            'engines_status': self.model_status,
            'cache_size': len(self.prediction_cache),
            'system_health': 'operational' if all(
                status['status'] in ['ready', 'mock']
                for status in self.model_status.values()
            ) else 'degraded',
            'last_updated': datetime.now().isoformat()
        }

# Global orchestrator instance
orchestrator = MLModelsOrchestrator()

# Convenience functions for easy import
async def predict_game_outcome(request: PredictionRequest) -> PredictionResponse:
    return await orchestrator.predict_game_outcome(request)

async def predict_player_prop(request: PredictionRequest) -> PredictionResponse:
    return await orchestrator.predict_player_prop(request)

async def detect_value_bets(game_ids: List[str]) -> List[Dict[str, Any]]:
    return await orchestrator.detect_value_bets(game_ids)

def get_system_status() -> Dict[str, Any]:
    return orchestrator.get_system_status()
