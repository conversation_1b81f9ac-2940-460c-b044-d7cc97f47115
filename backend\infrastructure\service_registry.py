"""
🏀 HYPER MEDUSA NEURAL VAULT - Service Registry
==============================================

Central service registry for dependency injection and service discovery.
"""

from typing import Dict, Any, Optional, Type, Callable
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger("ServiceRegistry")

class ServiceStatus(Enum):
    """Service status enumeration"""
    REGISTERED = "registered"
    INITIALIZED = "initialized"
    RUNNING = "running"
    STOPPED = "stopped"
    FAILED = "failed"

@dataclass
class ServiceInfo:
    """Service information"""
    name: str
    service_type: str
    instance: Optional[Any] = None
    factory: Optional[Callable] = None
    status: ServiceStatus = ServiceStatus.REGISTERED
    dependencies: list = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []

class ServiceRegistry:
    """Central service registry"""
    
    def __init__(self):
        self.services: Dict[str, ServiceInfo] = {}
        self.initialized = False
        
    def register_service(self, name: str, service_type: str, 
                        instance: Optional[Any] = None, 
                        factory: Optional[Callable] = None,
                        dependencies: Optional[list] = None):
        """Register a service"""
        self.services[name] = ServiceInfo(
            name=name,
            service_type=service_type,
            instance=instance,
            factory=factory,
            dependencies=dependencies or []
        )
        logger.info(f"📝 Registered service: {name} ({service_type})")
    
    def get_service(self, name: str) -> Optional[Any]:
        """Get service instance"""
        if name not in self.services:
            logger.warning(f"⚠️ Service not found: {name}")
            return None
            
        service_info = self.services[name]
        
        # Initialize if needed
        if service_info.instance is None and service_info.factory:
            try:
                service_info.instance = service_info.factory()
                service_info.status = ServiceStatus.INITIALIZED
                logger.info(f"🔧 Initialized service: {name}")
            except Exception as e:
                service_info.status = ServiceStatus.FAILED
                logger.error(f"❌ Failed to initialize service {name}: {e}")
                return None
        
        return service_info.instance
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all services"""
        return {
            "total_services": len(self.services),
            "by_status": {
                status.value: sum(1 for s in self.services.values() if s.status == status)
                for status in ServiceStatus
            },
            "services": {
                name: {
                    "type": info.service_type,
                    "status": info.status.value,
                    "has_instance": info.instance is not None,
                    "dependencies": info.dependencies
                }
                for name, info in self.services.items()
            }
        }
    
    def initialize_all_services(self):
        """Initialize all registered services"""
        logger.info("🚀 Initializing all services...")
        
        for name, service_info in self.services.items():
            if service_info.instance is None and service_info.factory:
                self.get_service(name)  # This will initialize the service
        
        self.initialized = True
        logger.info("✅ All services initialized")


    def register_war_council(self):
        """Register War Council services"""
        from src.kingdom_architecture.war_council_integration import WarCouncilIntegration
        
        self.register_service(
            name="war_council",
            service_type="advisory_council",
            factory=lambda: WarCouncilIntegration(),
            dependencies=["cognitive_spires"]
        )
        logger.info("⚔️ War Council registered in service registry")

    # Global service registry
service_registry = ServiceRegistry()

def register_service(name: str, service_type: str, **kwargs):
    """Register a service in the global registry"""
    return service_registry.register_service(name, service_type, **kwargs)

def get_service(name: str):
    """Get a service from the global registry"""
    return service_registry.get_service(name)

def get_registry_status():
    """Get global registry status"""
    return service_registry.get_service_status()
