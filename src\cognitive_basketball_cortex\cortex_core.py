import sys
import os
import asyncio
import logging
import json
import time
import random
import numpy as np
import torch
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
import threading
# Neural Basketball Core imports
from src.neural_cortex.neural_basketball_core import (
    NeuralBasketballCore,
    GameState,
    PredictionOutput
)

# Basketball Processors imports
from .basketball_processors import (
    QuantumMetricEngine, SituationalNeuralProcessor, AthleteCognitiveProfiler,
    AdaptiveThreatMatrix, EntangledMemoryVault, TemporalFluxStabilizer,
    NeuralPatternRecognizer, PerformanceOracleEngine, GameStateAnalyzer,
    PredictiveInsightEngine, StrategicSimulator, RealTimeProcessor
)

# Data Integration imports
try:
    from src.data_integration.unified_model_forge import UnifiedModelForge
except ImportError:
    # Will define logger below, so we'll handle this in the class
    UnifiedModelForge = None

#!/usr/bin/env python3
"""
 COGNITIVE BASKETBALL CORTEX - Core Neural Intelligence System
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
 Elite Basketball Intelligence Neural Network with Advanced Analytics

The CognitiveBasketballCortex represents the pinnacle of basketball intelligence,
integrating quantum metrics, neural pattern recognition, threat detection,
and predictive performance modeling for comprehensive basketball analytics.

 CORE CAPABILITIES:
- Quantum Metric Engine for advanced statistical analysis
- Situational Neural Processor for real-time game analysis
- Athlete Cognitive Profiler for player psychology assessment
- Adaptive Threat Matrix for risk detection and mitigation
- Entangled Memory Vault for pattern storage and retrieval
- Neural Pattern Recognition for game flow analysis
- Predictive Performance Modeling for outcome forecasting
- Enterprise Team Analytics for comprehensive team assessment
- Professional Impact Assessment for strategic evaluation
- Predictive Clutch Engine for pressure situation analysis

 Neural Excellence in Basketball Intelligence Analysis
 Elite-Level Cognitive Processing for Professional Sports Analytics
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure logger
logger = logging.getLogger("cognitive_basketball_cortex")
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter(
        "%(asctime)s %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

logger.info(" MEDUSA VAULT: Cognitive Basketball Cortex Core initializing...")

# Add Neural Basketball Core integration - REAL IMPLEMENTATION
NEURAL_CORE_AVAILABLE = True
logger.info("✅ MEDUSA VAULT: Real Neural Basketball Core imported successfully")




class ThreatLevel(Enum):
    """Threat severity classification"""
    LOW = "LOW"
    WARNING = "WARNING"
    CRITICAL = "CRITICAL"
    EMERGENCY = "EMERGENCY"

class PlayerPosition(Enum):
    """Basketball player positions"""
    PG = "Point Guard"
    SG = "Shooting Guard"
    SF = "Small Forward"
    PF = "Power Forward"
    C = "Center"

@dataclass
class CognitiveProfile:
    """Player cognitive assessment profile"""
    decision_speed: float = 50.0 # 0-100 scale
    stress_management: float = 50.0
    fatigue_resistance: float = 50.0
    focus_stability: float = 50.0
    pattern_recognition: float = 50.0
    adaptability: float = 50.0
    leadership_quotient: float = 50.0
    clutch_performance: float = 50.0
    quantum_metric_calculations: bool = True
    neural_pattern_recognition: bool = True
    adaptive_threat_detection: bool = True
    temporal_analysis: bool = True
    cognitive_profiling: bool = True
    strategic_simulation: bool = True
    real_time_processing: bool = True
    predictive_insights: bool = True
    entangled_memory_access: bool = True
    performance_oracle: bool = True
    game_state_analysis: bool = True
    situational_awareness: bool = True


@dataclass
class CortexStatus:
    """Track the operational status of the cortex"""
    version: str = "MEDUSA_CORTEX_3.0"
    active_spires: int = 0
    total_spires: int = 12
    initialization_time: Optional[datetime] = None
    last_analysis: Optional[datetime] = None
    confidence_level: float = 0.0
    threat_alerts: List[str] = field(default_factory=list)
    neural_insights: List[Dict] = field(default_factory=list)


@dataclass
class CortexCapabilities:
    """Track the available capabilities of the cortex"""
    version: str = "MEDUSA_CORTEX_3.0"
    quantum_metrics: bool = True
    neural_patterns: bool = True
    situational_analysis: bool = True
    predictive_modeling: bool = True
    threat_detection: bool = True
    enterprise_analytics: bool = True
    impact_assessment: bool = True
    cognitive_cortex: bool = True
    quantum_memory: bool = True
    athlete_profiling: bool = True
    adaptive_threats: bool = True
    clutch_prediction: bool = True
    available_spires: List[str] = field(default_factory=lambda: [
        "oracle_engine", "pattern_analyzer", "threat_detector", "memory_vault",
        "neural_processor", "quantum_analyzer", "game_analyzer", "insight_engine",
        "simulator", "realtime_processor"
    ])


class CognitiveBasketballCortex:
    """
    Central AI Engine for Basketball Intelligence

    Orchestrates 12 specialized cognitive spires to provide comprehensive
    basketball analysis, prediction, and strategic insights.
    """

    def __init__(self):
        self.status = CortexStatus()
        self.capabilities = CortexCapabilities()
        self.spires = {}
        self.is_initialized = False
        self.db_manager = None
        self.quantum_messenger = None
        self.oracle_memory = None

        # Initialize Real Neural Basketball Cores for NBA/WNBA Parity
        try:
            # Use CUDA if available, otherwise CPU
            device = 'cuda' if torch.cuda.is_available() else 'cpu'

            # Initialize dual neural cores for NBA/WNBA parity
            self.neural_core_nba = NeuralBasketballCore(device=device, league='NBA')
            self.neural_core_wnba = NeuralBasketballCore(device=device, league='WNBA')
            self.neural_cores = {
                'NBA': self.neural_core_nba,
                'WNBA': self.neural_core_wnba
            }
            self.neural_core_ready = True
            logger.info(f"✅ MEDUSA VAULT: Dual Neural Basketball Cores initialized (NBA + WNBA) on {device}")
            logger.info(f"✅ NBA Neural Core: {self.neural_core_nba.league} - Expected Total: {self.neural_core_nba.expected_total_points}")
            logger.info(f"✅ WNBA Neural Core: {self.neural_core_wnba.league} - Expected Total: {self.neural_core_wnba.expected_total_points}")
        except Exception as e:
            logger.error(f"❌ Real Neural Basketball Core initialization failed: {e}")
            self.neural_core_nba = None
            self.neural_core_wnba = None
            self.neural_cores = {}
            self.neural_core_ready = False
            raise RuntimeError(f"Failed to initialize Neural Basketball Cores: {e}")

        logger.info(" MEDUSA VAULT: Initializing Cognitive Basketball Cortex v3.0...")

    def get_neural_core(self, league: str = None) -> 'NeuralBasketballCore':
        """Get the appropriate neural core for the specified league"""
        if not self.neural_core_ready:
            raise RuntimeError("Neural cores not initialized")

        if league is None:
            # Default to NBA if no league specified
            league = 'NBA'

        league = league.upper()
        if league not in self.neural_cores:
            raise ValueError(f"Unsupported league: {league}. Supported leagues: NBA, WNBA")

        return self.neural_cores[league]

    async def initialize_cortex(self, db_manager=None, quantum_messenger=None, oracle_memory=None):
        """Initialize the cortex and all its cognitive spires"""
        try:
            self.db_manager = db_manager
            self.quantum_messenger = quantum_messenger
            self.oracle_memory = oracle_memory

            logger.info(" MEDUSA VAULT: Starting cognitive spire initialization sequence...")

            # Initialize all 12 cognitive spires
            await self._initialize_cognitive_spires()

            self.status.initialization_time = datetime.now()
            self.status.confidence_level = 0.95
            self.is_initialized = True

            logger.info(f" Cognitive Basketball Cortex fully operational - {self.status.active_spires}/{self.status.total_spires} spires online")

            return {
                "status": "operational",
                "version": self.status.version,
                "active_spires": self.status.active_spires,
                "capabilities": self.capabilities.__dict__,
                "confidence": self.status.confidence_level
            }

        except Exception as e:
            logger.error(f" Cortex initialization failed: {e}")
            raise

    async def _initialize_cognitive_spires(self):
        """Initialize all 12 specialized basketball processors"""
        # Basketball processors are already imported at module level

        spire_classes = [
            ("quantum_metric", QuantumMetricEngine, " Quantum Metric Engine"),
            ("neural_processor", SituationalNeuralProcessor, " Situational Neural Processor"),
            ("cognitive_profiler", AthleteCognitiveProfiler, " Athlete Cognitive Profiler"),
            ("threat_matrix", AdaptiveThreatMatrix, " Adaptive Threat Matrix"),
            ("memory_vault", EntangledMemoryVault, " Entangled Memory Vault"),
            ("flux_stabilizer", TemporalFluxStabilizer, "⏰ Temporal Flux Stabilizer"),
            ("pattern_recognizer", NeuralPatternRecognizer, "🔍 Neural Pattern Recognizer"),
            ("oracle_engine", PerformanceOracleEngine, " Performance Oracle Engine"),
            ("game_analyzer", GameStateAnalyzer, " Game State Analyzer"),
            ("insight_engine", PredictiveInsightEngine, " Predictive Insight Engine"),
            ("simulator", StrategicSimulator, "⚔️ Strategic Simulator"),
            ("realtime_processor", RealTimeProcessor, " Real-Time Processor")
        ]

        for spire_key, spire_class, spire_name in spire_classes:
            try:
                spire_instance = spire_class()
                if hasattr(spire_instance, 'initialize'):
                    await spire_instance.initialize()
                self.spires[spire_key] = spire_instance
                self.status.active_spires += 1
                logger.info(f"{spire_name} initialized")
            except Exception as e:
                logger.warning(f" TITAN PROCESSING FAILED: initialize {spire_name}: {e}")

    async def analyze_game_state(self, game_data: Dict) -> Dict[str, Any]:
        """Comprehensive game state analysis using all cognitive spires and Neural Basketball Core
        - Leverage for Edge Cases: Route rare/high-stakes/ambiguous scenarios to Cortex for expert review.
        - Explainability First: Always surface Cortex explanations.
        - Hybrid Decision Logic: Check neural vs. expert disagreement.
        - Scenario Tagging: Tag scenarios for War Council.
        - Feedback-Driven Rule Updates: Log feedback for rule improvement.
        - Meta-Learning Collaboration: Log disagreements for meta-learning.
        """
        if not self.is_initialized:
            raise RuntimeError("Cortex not initialized")

        try:
            analysis_start = datetime.now()

            # --- Scenario Tagging ---
            scenario_tags = self._tag_scenario(game_data)

            # --- Leverage for Edge Cases ---
            is_edge_case = self._is_edge_case(game_data, scenario_tags)
            if is_edge_case:
                logger.info("[Cortex] Edge/rare/ambiguous scenario detected. Prioritizing expert review.")

            # Core game analysis
            game_analysis = await self.spires["game_analyzer"].analyze_game(game_data)

            # Quantum metrics calculation
            quantum_metrics = await self.spires["quantum_metric"].calculate_metrics(game_data)

            # Threat detection
            threats = await self.spires["threat_matrix"].detect_threats(game_data)

            # Predictive insights
            predictions = await self.spires["insight_engine"].generate_insights(game_data)

            # Neural pattern analysis
            patterns = await self.spires["pattern_recognizer"].analyze_patterns(game_data)

            # Neural Basketball Core analysis (Enhanced Neural Intelligence)
            neural_context = {
                'league': game_data.get('league', 'NBA'),
                'cortex_analysis': {
                    'quantum_metrics': quantum_metrics,
                    'threats': threats,
                    'patterns': patterns
                },
                'timestamp': analysis_start.isoformat()
            }
            neural_core_analysis = await self.neural_core_predict(game_data, neural_context)

            # --- Hybrid Decision Logic ---
            neural_pred = neural_core_analysis.get('win_probability', 0.5)
            expert_pred = predictions.get('win_probability', 0.5)
            neural_conf = neural_core_analysis.get('confidence_score', 0.5)
            expert_conf = predictions.get('confidence', 0.5)
            disagreement = abs(neural_pred - expert_pred) > 0.3
            escalate = disagreement and (neural_conf > 0.7 and expert_conf > 0.7)
            if escalate:
                logger.warning(f"[Cortex] High-confidence disagreement: neural={neural_pred}, expert={expert_pred}")
                # Log for meta-learning
                self._log_disagreement(game_data, neural_core_analysis, predictions)
                # Optionally escalate for review or blend outputs

            # --- Explainability First ---
            explanation = self._generate_explanation(game_analysis, quantum_metrics, threats, predictions, scenario_tags)

            analysis_result = {
                "analysis_id": f"cortex_{int(analysis_start.timestamp())}",
                "timestamp": analysis_start.isoformat(),
                "game_analysis": game_analysis,
                "quantum_metrics": quantum_metrics,
                "threat_assessment": threats,
                "predictions": predictions,
                "neural_patterns": patterns,
                "neural_core_intelligence": neural_core_analysis, # NEW: Neural Basketball Core
                "confidence": self._calculate_confidence(game_analysis, quantum_metrics, threats),
                "enhanced_confidence": neural_core_analysis.get('neural_confidence', 0.5), # NEW
                "cortex_version": self.status.version,
                "neural_enhancement": True, # NEW: Flag indicating neural core enhancement
                "scenario_tags": scenario_tags,
                "explanation": explanation,
                "edge_case": is_edge_case,
                "disagreement": disagreement
            }

            self.status.last_analysis = analysis_start
            self.status.neural_insights.append(analysis_result)

            logger.info(f" Enhanced Basketball Cortex analysis complete with Neural Core intelligence")
            return analysis_result

        except Exception as e:
            logger.error(f"Game analysis failed: {e}")
            raise

    def _tag_scenario(self, game_data: Dict) -> List[str]:
        """Scenario Tagging: Tag scenarios for War Council (e.g., rest, injury, rivalry)"""
        tags = []
        if game_data.get('is_playoff'):
            tags.append('playoff')
        if game_data.get('rest_days_home', 1) < 2 or game_data.get('rest_days_away', 1) < 2:
            tags.append('rest_disadvantage')
        if game_data.get('injury_reported'):
            tags.append('injury_risk')
        if game_data.get('is_rivalry'):
            tags.append('rivalry_game')
        # Add more tags as needed
        return tags

    def _is_edge_case(self, game_data: Dict, tags: List[str]) -> bool:
        """Detect if scenario is rare/high-stakes/ambiguous"""
        return 'playoff' in tags or 'injury_risk' in tags or 'rivalry_game' in tags or game_data.get('edge_case', False)

    def _generate_explanation(self, game_analysis, quantum_metrics, threats, predictions, tags) -> List[str]:
        """Generate human-readable explanation for transparency"""
        explanation = []
        if tags:
            explanation.append(f"Scenario tags: {', '.join(tags)}")
        if threats:
            explanation.append(f"Threats detected: {len(threats)}")
        if predictions:
            explanation.append(f"Expert prediction: {predictions}")
        return explanation

    def _log_disagreement(self, game_data, neural_core_analysis, predictions):
        """Meta-Learning Collaboration: Log advisor disagreement for improvement"""
        if not hasattr(self, 'disagreement_log'):
            self.disagreement_log = []
        self.disagreement_log.append({
            'game_data': game_data,
            'neural': neural_core_analysis,
            'expert': predictions,
            'timestamp': datetime.now().isoformat()
        })
        logger.info("[Cortex] Disagreement logged for meta-learning.")

    def receive_feedback(self, feedback: Dict[str, Any]):
        """Feedback-Driven Rule Updates: Log feedback for rule improvement"""
        if not hasattr(self, 'feedback_log'):
            self.feedback_log = []
        self.feedback_log.append(feedback)
        logger.info(f"[Cortex] Feedback received: {feedback}")

    async def get_player_cognitive_profile(self, hero_id: str) -> Dict[str, Any]:
        """Generate comprehensive cognitive profile for a player"""
        if not self.is_initialized:
            raise RuntimeError("Cortex not initialized")

        try:
            profile = await self.spires["cognitive_profiler"].create_profile(hero_id)

            # Enhance with historical patterns
            patterns = await self.spires["pattern_recognizer"].get_player_patterns(hero_id)

            # Performance oracle insights
            oracle_insights = await self.spires["oracle_engine"].get_player_insights(hero_id)

            return {
                "hero_id": hero_id,
                "cognitive_profile": profile,
                "behavioral_patterns": patterns,
                "oracle_insights": oracle_insights,
                "profile_confidence": self._calculate_profile_confidence(profile),
                "generated_at": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Player profiling failed for {hero_id}: {e}")
            raise

    async def calculate_advanced_metrics(self, team_stats: Dict) -> Dict[str, Any]:
        """Calculate advanced basketball metrics using quantum processing"""
        if not self.is_initialized:
            raise RuntimeError("Cortex not initialized")

        try:
            # Quantum metric calculations
            quantum_results = await self.spires["quantum_metric"].process_team_stats(team_stats)

            # Temporal analysis for trends
            temporal_analysis = await self.spires["flux_stabilizer"].analyze_trends(team_stats)

            return {
                "team_metrics": quantum_results,
                "temporal_trends": temporal_analysis,
                "calculation_time": datetime.now().isoformat(),
                "metrics_confidence": quantum_results.get("confidence", 0.0)
            }

        except Exception as e:
            logger.error(f"Advanced metrics calculation failed: {e}")
            raise

    async def detect_threats(self, data: Dict) -> List[Dict[str, Any]]:
        """Detect potential threats and vulnerabilities"""
        if not self.is_initialized:
            raise RuntimeError("Cortex not initialized")

        try:
            threats = await self.spires["threat_matrix"].comprehensive_threat_scan(data)
            self.status.threat_alerts.extend([t["message"] for t in threats])
            return threats
        except Exception as e:
            logger.error(f"Threat detection failed: {e}")
            return []

    async def get_cortex_status(self) -> Dict[str, Any]:
        """Get current cortex operational status"""
        return {
            "version": self.status.version,
            "initialized": self.is_initialized,
            "active_spires": self.status.active_spires,
            "total_spires": self.status.total_spires,
            "confidence_level": self.status.confidence_level,
            "initialization_time": self.status.initialization_time.isoformat() if self.status.initialization_time else None,
            "last_analysis": self.status.last_analysis.isoformat() if self.status.last_analysis else None,
            "capabilities": self.capabilities.__dict__,
            "recent_insights": len(self.status.neural_insights),
            "threat_alerts": len(self.status.threat_alerts)
        }

    def _calculate_confidence(self, game_analysis: Dict, quantum_metrics: Dict, threats: List) -> float:
        """Calculate overall confidence in analysis"""
        base_confidence = 0.85

        # Adjust based on data quality
        if game_analysis.get("data_quality", 0) > 0.9:
            base_confidence += 0.1

        # Adjust based on threat level
        high_threats = len([t for t in threats if t.get("severity", "") == "high"])
        if high_threats > 0:
            base_confidence -= 0.05 * high_threats

        return max(0.0, min(1.0, base_confidence))

    def _calculate_profile_confidence(self, profile: Dict) -> float:
        """Calculate confidence in player profile"""
        data_points = profile.get("data_points", 0)
        if data_points > 100:
            return 0.95
        elif data_points > 50:
            return 0.85
        elif data_points > 20:
            return 0.75
        else:
            return 0.65

    async def shutdown(self):
        """Gracefully shutdown the cortex and all spires"""
        logger.info(" MEDUSA VAULT: Shutting down Cognitive Basketball Cortex...")

        for spire_name, spire in self.spires.items():
            try:
                if hasattr(spire, 'shutdown'):
                    await spire.shutdown()
                logger.info(f" {spire_name} spire shutdown complete")
            except Exception as e:
                logger.warning(f" Error shutting down {spire_name}: {e}")

        self.is_initialized = False
        logger.info(" MEDUSA VAULT: Cognitive Basketball Cortex shutdown complete")

    async def neural_core_predict(self, game_data: Dict[str, Any],
                                  neural_context: Dict[str, Any]) -> Dict[str, Any]:
        """Use the Neural Basketball Core for advanced neural predictions"""

        if not self.neural_core_ready or self.neural_core is None:
            logger.warning(" Neural Basketball Core not available, using fallback")
            return self._fallback_neural_prediction(game_data)

        try:
            # Prepare game state for neural core
            game_state = self._prepare_neural_game_state(game_data, neural_context)

            # Get neural prediction with expert basketball intelligence
            neural_prediction = await self.neural_core.predict(game_state)  # Expert neural prediction

            # Convert neural prediction to cortex format
            cortex_prediction = {
                'neural_win_probability': neural_prediction.win_probability,
                'neural_spread': neural_prediction.spread_prediction,
                'neural_total': neural_prediction.total_points,
                'neural_confidence': neural_prediction.confidence_score,
                'quantum_uncertainty': neural_prediction.quantum_uncertainty,
                'basketball_iq_valid': neural_prediction.basketball_iq_validation,
                'decision_trail': neural_prediction.decision_trail,
                'expert_divergence': neural_prediction.expert_divergence_factors,
                'neural_processing_mode': 'graph_neural_networks_and_transformers',
                'neural_status': 'success'
            }

            logger.info(f" Neural Basketball Core prediction: {neural_prediction.confidence_score:.3f} confidence")
            return cortex_prediction

        except Exception as e:
            logger.error(f" Neural Basketball Core prediction failed: {e}")
            return self._fallback_neural_prediction(game_data)

    def _prepare_neural_game_state(self, game_data: Dict[str, Any],
                                   neural_context: Dict[str, Any]) -> 'GameState':
        """Prepare GameState object for Neural Basketball Core"""

        # Create expert basketball intelligence tensors based on real game data
        batch_size = 1
        num_players = 10
        sequence_length = 100
        feature_dim = 64

        # Extract real basketball data for player interaction graph
        home_team = game_data.get('home_team', {})
        away_team = game_data.get('away_team', {})

        # Create basketball-intelligent player interaction graph
        player_graph = self._create_basketball_player_graph(home_team, away_team, batch_size, num_players)

        # Create basketball-intelligent play sequence from game flow
        play_sequence = self._create_basketball_play_sequence(game_data, neural_context, batch_size, sequence_length, feature_dim)

        # Game context
        game_context_data = {
            'league': neural_context.get('league', 'NBA'),
            'quarter': game_data.get('quarter', 1),
            'score_difference': game_data.get('score_diff', 0),
            'time_remaining': game_data.get('time_remaining', 48.0),
            'playoff_implications': game_data.get('playoff_game', False),
            'rivalry_game': game_data.get('rivalry', False),
            'national_tv': game_data.get('national_tv', False),
            'season_importance': game_data.get('season_importance', 0.5)
        }

        # Create GameState object
        game_state = GameState(
            player_graph=player_graph,
            play_sequence=play_sequence,
            game_context=game_context_data, # Use the prepared dictionary
            crowd_sentiment=game_data.get('crowd_sentiment'),
            referee_bias=game_data.get('referee_bias')
        )

        return game_state

    def _create_basketball_player_graph(self, home_team: Dict, away_team: Dict,
                                       batch_size: int, num_players: int) -> torch.Tensor:
        """Create basketball-intelligent player interaction graph based on real team data"""
        # Initialize player interaction matrix
        player_graph = torch.zeros(batch_size, num_players, num_players)

        # Extract player stats and create interaction weights
        home_players = home_team.get('players', [])
        away_players = away_team.get('players', [])

        for i in range(min(5, len(home_players))):  # Home team starters
            for j in range(min(5, len(home_players))):
                if i != j:
                    # Calculate interaction based on assist rates, chemistry, etc.
                    player_i = home_players[i] if i < len(home_players) else {}
                    player_j = home_players[j] if j < len(home_players) else {}

                    # Basketball intelligence: assist rate + chemistry factors
                    assist_rate_i = player_i.get('assist_rate', 0.15)
                    chemistry = player_i.get('chemistry_with', {}).get(player_j.get('id', ''), 0.5)
                    interaction_strength = (assist_rate_i + chemistry) / 2

                    player_graph[0, i, j] = interaction_strength

        for i in range(5, min(10, 5 + len(away_players))):  # Away team starters
            for j in range(5, min(10, 5 + len(away_players))):
                if i != j:
                    away_idx_i = i - 5
                    away_idx_j = j - 5
                    if away_idx_i < len(away_players) and away_idx_j < len(away_players):
                        player_i = away_players[away_idx_i]
                        player_j = away_players[away_idx_j]

                        assist_rate_i = player_i.get('assist_rate', 0.15)
                        chemistry = player_i.get('chemistry_with', {}).get(player_j.get('id', ''), 0.5)
                        interaction_strength = (assist_rate_i + chemistry) / 2

                        player_graph[0, i, j] = interaction_strength

        # Normalize as probabilities
        player_graph = torch.softmax(player_graph, dim=-1)
        return player_graph

    def _create_basketball_play_sequence(self, game_data: Dict, neural_context: Dict,
                                        batch_size: int, sequence_length: int, feature_dim: int) -> torch.Tensor:
        """Create basketball-intelligent play sequence based on game flow and context"""
        play_sequence = torch.zeros(batch_size, sequence_length, feature_dim)

        # Extract basketball context
        quarter = game_data.get('quarter', 1)
        score_diff = game_data.get('score_diff', 0)
        time_remaining = game_data.get('time_remaining', 48.0)
        pace = game_data.get('pace', 100.0)

        # Basketball intelligence features for each time step
        for t in range(sequence_length):
            # Time-based features (0-15)
            play_sequence[0, t, 0] = quarter / 4.0  # Normalized quarter
            play_sequence[0, t, 1] = time_remaining / 48.0  # Normalized time
            play_sequence[0, t, 2] = min(abs(score_diff) / 20.0, 1.0)  # Normalized score difference
            play_sequence[0, t, 3] = pace / 120.0  # Normalized pace

            # Momentum features (16-31)
            momentum_shift = game_data.get('momentum_shifts', [])
            recent_momentum = momentum_shift[-1] if momentum_shift else 0.5
            play_sequence[0, t, 16] = recent_momentum

            # Fatigue features (32-47)
            home_fatigue = game_data.get('home_fatigue', 0.3)
            away_fatigue = game_data.get('away_fatigue', 0.3)
            play_sequence[0, t, 32] = home_fatigue
            play_sequence[0, t, 33] = away_fatigue

            # Strategic features (48-63)
            offensive_efficiency = game_data.get('offensive_efficiency', 110.0) / 130.0
            defensive_efficiency = game_data.get('defensive_efficiency', 105.0) / 130.0
            play_sequence[0, t, 48] = offensive_efficiency
            play_sequence[0, t, 49] = defensive_efficiency

            # Add some basketball-intelligent noise for remaining features
            if feature_dim > 50:
                play_sequence[0, t, 50:] = torch.randn(feature_dim - 50) * 0.1

        return play_sequence

    def _fallback_neural_prediction(self, game_data: Dict[str, Any]) -> Dict[str, Any]:
        """Intelligent fallback prediction when Neural Basketball Core is unavailable"""
        try:
            # Try to get real model prediction before basic fallback
            model_forge = UnifiedModelForge()

            # Get real prediction from trained models (synchronous call)
            real_prediction = model_forge.predict_game_outcome_sync(game_data, game_data.get('league', 'NBA'))

            if real_prediction and real_prediction.get('success', False):
                return {
                    'neural_win_probability': real_prediction.get('home_win_probability', 0.52),
                    'neural_spread': real_prediction.get('predicted_spread', -2.0),
                    'neural_total': real_prediction.get('predicted_total', 220.0),
                    'neural_confidence': real_prediction.get('confidence', 0.65),
                    'quantum_uncertainty': 1.0 - real_prediction.get('confidence', 0.65),
                    'basketball_iq_valid': True,
                    'decision_trail': ['Real model fallback - Neural Basketball Core unavailable', 'Using trained basketball models'],
                    'expert_divergence': ['Real model consensus'],
                    'neural_processing_mode': 'real_model_fallback',
                    'neural_status': 'real_model_active'
                }

        except Exception as e:
            logger.warning(f"Real model fallback failed in cortex: {e}")

        # Basic intelligent fallback with basketball logic
        league = game_data.get('league', 'NBA')
        home_advantage = 0.54 if league == 'NBA' else 0.52

        return {
            'neural_win_probability': home_advantage,
            'neural_spread': -2.0 if league == 'NBA' else -1.5,
            'neural_total': 220.0 if league == 'NBA' else 165.0,
            'neural_confidence': 0.45,
            'quantum_uncertainty': 0.55,
            'basketball_iq_valid': False,
            'decision_trail': ['Intelligent fallback mode - Neural Basketball Core unavailable', 'Using basketball analytics'],
            'expert_divergence': ['System unavailable - using basketball intelligence'],
            'neural_processing_mode': 'intelligent_fallback',
            'neural_status': 'fallback_with_basketball_logic'
        }
