import asyncio
import logging
import json
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, List, Optional, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import os
import sys
from firebase_production_system import firebase_manager, FirebaseAlerts
from vault_oracle.core.oracle_focus import oracle_focus
from dotenv import load_dotenv

try:
    from vault_oracle.interfaces.divine_messenger import DivineMessenger
    DIVINE_MESSENGER_AVAILABLE = True
except ImportError:
    DIVINE_MESSENGER_AVAILABLE = False
    DivineMessenger = None

#!/usr/bin/env python3
"""
Expert Messaging Orchestrator v3.0
==================================
Unified expert-level messaging system that integrates all notification channels:
- Quantum Firebase Service (real-time notifications)
- Divine Messenger (quantum-entangled messages)
- Alert System (HYPER MEDUSA NEURAL VAULT analytics alerts)
- Push Notifications (FCM integration)

This orchestrator provides a single interface for all messaging needs across
the HOOPS_PANTHEON_BOOK_OFFICIAL platform with quantum security and basketball intelligence.
"""




# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# Configure expert logging
logging.basicConfig(
    level=logging.INFO,
    format="📡 %(asctime)s │ %(levelname)s │ %(name)s │ %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger("ExpertMessagingOrchestrator")

# Import messaging components
try:
    from firebase_production_system import (
        QuantumMessenger, QuantumMessage, QuantumMessengerConfig, VaultRoutes
    )
    logger.info(" MEDUSA VAULT: Successfully imported messaging components")
    FIREBASE_PRODUCTION_AVAILABLE = True
    logger.info(" MEDUSA VAULT: Firebase Production System availabl]" \
    "")
except ImportError as e:
    logger.warning(f" Could not import some messaging components: {e}")
    
    # Fallback classes if imports fail
    class VaultRoutes(Enum):
        """Fallback VaultRoutes enum"""
        SNAKE_SIRENS = "/oracle-visions"
        HEROIC_QUESTS = "/hero-visions"
        MOIRAI_SIMULACRUM = "/moirai-visions"
        CHOSEN_ONES_REGISTRY = "/chosen-ones"
        QUANTUM_ENTANGLEMENT = "/quantum-visions"
        MARKET_GAZE = "/market-visions"
        TITAN_CODEX = "/titan-codex"
        ORACLE_SIMULACRUM = "/quantum-coliseum"    
    QuantumMessenger = None
    QuantumMessage = None
    QuantumMessengerConfig = None
    FIREBASE_PRODUCTION_AVAILABLE = False
    logger.warning(f" Firebase Production System not available: {e}")
    firebase_manager = None
    FirebaseAlerts = None

# Import basketball analytics
try:
    _ORACLE_FOCUS_AVAILABLE = True
except ImportError:
    _ORACLE_FOCUS_AVAILABLE = False
    def oracle_focus(func):
        return func

# Import environment variables
load_dotenv()

class MessagePriority(Enum):
    """Message priority levels for routing and delivery"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4
    EMERGENCY = 5

class MessageType(Enum):
    """Types of messages supported by the orchestrator"""
    GAME_ALERT = "game_alert"
    PLAYER_ALERT = "player_alert"
    PREDICTION_ALERT = "prediction_alert"
    SYSTEM_NOTIFICATION = "system_notification"
    QUANTUM_STATE = "quantum_state"
    BASKETBALL_ANALYTICS = "basketball_analytics"
    EMERGENCY_ALERT = "emergency_alert"
    FCM_PUSH = "fcm_push"

class DeliveryChannel(Enum):
    """Available delivery channels"""
    QUANTUM_MESSENGER = "quantum_messenger"
    FIREBASE_SERVICE = "firebase_service"
    FCM_PUSH = "fcm_push"
    MNEMOSYNE_LOG = "database_log"
    WEBHOOK = "webhook"
    EMAIL = "email" # Future implementation

@dataclass
class MessageRecipient:
    """Message recipient information"""
    vault_user_id: str
    channels: List[DeliveryChannel] = field(default_factory=list)
    fcm_tokens: List[str] = field(default_factory=list)
    preferences: Dict[str, Any] = field(default_factory=dict)
    timezone: str = "UTC"

@dataclass
class DeliveryResult:
    """Result of a message delivery attempt"""
    channel: DeliveryChannel
    success: bool
    message_id: str
    error: Optional[str] = None
    delivery_time: Optional[datetime] = None
    response_data: Optional[Dict[str, Any]] = None

@dataclass
class UnifiedMessage:
    """Unified message structure for all channels"""
    message_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    message_type: MessageType = MessageType.SYSTEM_NOTIFICATION
    priority: MessagePriority = MessagePriority.NORMAL
    
    # Content
    title: str = ""
    body: str = ""
    content: Dict[str, Any] = field(default_factory=dict)
    
    # Recipients and routing
    recipients: List[MessageRecipient] = field(default_factory=list)
    channels: List[DeliveryChannel] = field(default_factory=list)
    
    # Basketball context
    titan_clash_id: Optional[str] = None
    hero_id: Optional[str] = None
    mythic_roster_id: Optional[str] = None
    league: Optional[str] = None
    
    # Metadata
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    expires_at: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)
    
    # Delivery tracking
    delivery_status: Dict[str, str] = field(default_factory=dict)
    retry_count: int = 0
    max_retries: int = 3

class ExpertMessagingOrchestrator:
    """
    Expert-level messaging orchestrator that unifies all notification systems

    SINGLETON PATTERN: Only one instance can exist to prevent system hangs.
    """

    _instance = None
    _initialized = False

    def __new__(cls, config: Optional[Dict[str, Any]] = None):
        """Enforce singleton pattern to prevent multiple instances"""
        if cls._instance is None:
            cls._instance = super(ExpertMessagingOrchestrator, cls).__new__(cls)
            logger.info(" MEDUSA VAULT: Creating singleton ExpertMessagingOrchestrator instance")
        else:
            logger.info(" MEDUSA VAULT: Returning existing ExpertMessagingOrchestrator singleton")
        return cls._instance

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        # Only initialize once (singleton pattern)
        if self._initialized:
            return

        self.config = config or {}
        # Lazy-initialize async objects to avoid event loop issues
        self._message_queue = None
        self._shutdown_event = None
        self.delivery_history: List[DeliveryResult] = []
        self.active_channels: Dict[DeliveryChannel, bool] = {}

        # Initialize messaging services
        self.quantum_messenger = None
        self.firebase_service = None
        self.quantum_firebase = None

        # Performance tracking
        self.message_count = 0
        self.successful_deliveries = 0
        self.failed_deliveries = 0

        # Task management
        self.queue_processor_task = None
        self.is_running = False

        # Mark as initialized
        ExpertMessagingOrchestrator._initialized = True

        logger.info(" MEDUSA VAULT: Expert Messaging Orchestrator initialized")

    @property
    def message_queue(self) -> asyncio.Queue:
        """Lazy-initialize message queue when first accessed"""
        if self._message_queue is None:
            try:
                self._message_queue = asyncio.Queue()
            except RuntimeError:
                # No event loop running, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                self._message_queue = asyncio.Queue()
        return self._message_queue

    @property
    def shutdown_event(self) -> asyncio.Event:
        """Lazy-initialize shutdown event when first accessed"""
        if self._shutdown_event is None:
            try:
                self._shutdown_event = asyncio.Event()
            except RuntimeError:
                # No event loop running, create a new one
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                self._shutdown_event = asyncio.Event()
        return self._shutdown_event

    @oracle_focus
    async def initialize_services(self) -> None:
        """Initialize all messaging services"""
        logger.info(" MEDUSA VAULT: 🔧 Initializing messaging services...")

        try:
            # Initialize Quantum Messenger
            await self._initialize_quantum_messenger()

            # Initialize Firebase services
            await self._initialize_firebase_services()

            # Initialize FCM service
            await self._initialize_fcm_service()

            # Start message processing task (non-blocking)
            self.is_running = True
            self.queue_processor_task = asyncio.create_task(self._process_message_queue())

            logger.info(" MEDUSA VAULT: All messaging services initialized successfully")
            logger.info(" MEDUSA VAULT: Message queue processor started as background task")

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize messaging services: {e}")
            self.is_running = False
            raise
    
    async def _initialize_quantum_messenger(self) -> None:
        """Initialize the Quantum Messenger service"""
        try:
            config = QuantumMessengerConfig(
                encryption_key=os.getenv("QUANTUM_MESSENGER_KEY", "default_key"),
                signature_secret=os.getenv("QUANTUM_SIGNATURE_SECRET", "default_secret"),
                max_retries=3,
                timeout_seconds=30.0,
                enable_metrics=True
            )            
            self.quantum_messenger = QuantumMessenger(config)
            self.active_channels[DeliveryChannel.QUANTUM_MESSENGER] = True
            logger.info(" MEDUSA VAULT: ⚛️ Quantum Messenger initialized")
        
        except Exception as e:
            logger.warning(f" Quantum Messenger initialization failed: {e}")
            self.active_channels[DeliveryChannel.QUANTUM_MESSENGER] = False
    
    async def _initialize_firebase_services(self) -> None:
        """Initialize Firebase production services"""
        try:
            if FIREBASE_PRODUCTION_AVAILABLE and firebase_manager:
                # Check if Firebase is already initialized
                if firebase_manager.initialized:
                    self.active_channels[DeliveryChannel.FIREBASE_SERVICE] = True
                    logger.info(" MEDUSA VAULT: Firebase Production Service connected")
                else:
                    logger.warning(" Firebase Production Service available but not initialized")
                    self.active_channels[DeliveryChannel.FIREBASE_SERVICE] = False
            else:
                logger.warning(" Firebase Production Service not available")
                self.active_channels[DeliveryChannel.FIREBASE_SERVICE] = False
        except Exception as e:
            logger.warning(f" Firebase services initialization failed: {e}")
            self.active_channels[DeliveryChannel.FIREBASE_SERVICE] = False

    async def _initialize_fcm_service(self) -> None:
        """Initialize FCM push notification service using Firebase production system"""
        try:
            if FIREBASE_PRODUCTION_AVAILABLE and firebase_manager:
                # Use Firebase production system for FCM
                if firebase_manager.initialized:
                    self.active_channels[DeliveryChannel.FCM_PUSH] = True
                    logger.info(" MEDUSA VAULT: 📱 FCM Push Service initialized via Firebase Production System")
                else:
                    logger.warning(" Firebase Production System available but not initialized")
                    self.active_channels[DeliveryChannel.FCM_PUSH] = False
            else:
                logger.warning(" Firebase Production System not available, FCM disabled")
                self.active_channels[DeliveryChannel.FCM_PUSH] = False
        
        except Exception as e:
            logger.warning(f" FCM service initialization failed: {e}")
            self.active_channels[DeliveryChannel.FCM_PUSH] = False
    
    @oracle_focus
    async def send_unified_message(self, message: UnifiedMessage) -> List[DeliveryResult]:
        """Send a unified message through all specified channels"""
        logger.info(f"📤 Sending unified message: {message.message_id}")
        
        results = []
        self.message_count += 1
        
        # Determine channels to use
        channels = message.channels if message.channels else self._determine_optimal_channels(message)
        
        # Send through each channel
        for channel in channels:
            if not self.active_channels.get(channel, False):
                logger.warning(f" Channel {channel.value} is not active, skipping")
                results.append(DeliveryResult(
                    channel=channel,
                    success=False,
                    message_id=message.message_id,
                    error="Channel not active"
                ))
                continue
            
            try:
                result = await self._deliver_to_channel(message, channel)
                results.append(result)
                
                if result.success:
                    self.successful_deliveries += 1
                else:
                    self.failed_deliveries += 1
            
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: deliver to {channel.value}: {e}")
                results.append(DeliveryResult(
                    channel=channel,
                    success=False,
                    message_id=message.message_id,
                    error=str(e)
                ))
                self.failed_deliveries += 1
        
        # Store delivery history
        self.delivery_history.extend(results)
        
        # Update message delivery status
        message.delivery_status = {
            result.channel.value: "success" if result.success else "failed"
            for result in results
        }
        
        logger.info(f" Message {message.message_id} delivery complete: "
                    f"{sum(1 for r in results if r.success)}/{len(results)} successful")
        
        return results
    
    async def _deliver_to_channel(self, message: UnifiedMessage, channel: DeliveryChannel) -> DeliveryResult:
        """Deliver message to a specific channel"""
        start_time = datetime.now(timezone.utc)
        
        try:
            if channel == DeliveryChannel.QUANTUM_MESSENGER:
                return await self._deliver_quantum_message(message)
            
            elif channel == DeliveryChannel.FIREBASE_SERVICE:
                return await self._deliver_firebase_message(message)
            
            elif channel == DeliveryChannel.FCM_PUSH:
                return await self._deliver_fcm_message(message)
            
            elif channel == DeliveryChannel.MNEMOSYNE_LOG:
                return await self._deliver_database_log(message)
            
            else:
                raise ValueError(f"Unsupported channel: {channel}")
        
        except Exception as e:
            return DeliveryResult(
                channel=channel,
                success=False,
                message_id=message.message_id,
                error=str(e),
                delivery_time=datetime.now(timezone.utc)
            )
    
    async def _deliver_quantum_message(self, message: UnifiedMessage) -> DeliveryResult:
        """Deliver message through Quantum Messenger"""
        if not self.quantum_messenger:
            raise ValueError("Quantum Messenger not initialized")
        
        # Convert to QuantumMessage
        quantum_msg = QuantumMessage(
            route=self._get_quantum_route(message.message_type),
            content=message.content,
            timestamp=message.timestamp,
            priority=message.priority.value,
            quantum_signature=None # Will be generated
        )
        
        async with self.quantum_messenger.quantum_session():
            result, status = await self.quantum_messenger.entangle_vision(quantum_msg)
            
            return DeliveryResult(
                channel=DeliveryChannel.QUANTUM_MESSENGER,
                success=status == "success",
                message_id=message.message_id,
                delivery_time=datetime.now(timezone.utc),
                response_data={"result": result, "status": status}
            )
    
    async def _deliver_firebase_message(self, message: UnifiedMessage) -> DeliveryResult:
        """Deliver message through Firebase service"""
        if not self.quantum_firebase:
            raise ValueError("Firebase service not initialized")
        
        # Store basketball prediction or quantum state
        if message.message_type in [MessageType.BASKETBALL_ANALYTICS, MessageType.PREDICTION_ALERT]:
            doc_id = await self.quantum_firebase.store_basketball_prediction({
                "message_id": message.message_id,
                "title": message.title,
                "body": message.body,
                "content": message.content,
                "titan_clash_id": message.titan_clash_id,
                "hero_id": message.hero_id,
                "timestamp": message.timestamp.isoformat(),
                "priority": message.priority.value
            })
        else:
            doc_id = await self.quantum_firebase.store_quantum_state(
                message.message_id,
                {
                    "title": message.title,
                    "body": message.body,
                    "content": message.content,
                    "timestamp": message.timestamp.isoformat()
                }
            )
        
        return DeliveryResult(
            channel=DeliveryChannel.FIREBASE_SERVICE,
            success=bool(doc_id),
            message_id=message.message_id,
            delivery_time=datetime.now(timezone.utc),
            response_data={"document_id": doc_id}
        )
    
    async def _deliver_fcm_message(self, message: UnifiedMessage) -> DeliveryResult:
        """Deliver FCM push notification"""
        if not self.firebase_service:
            raise ValueError("FCM service not initialized")
        
        # Collect FCM tokens from recipients
        fcm_tokens = []
        for recipient in message.recipients:
            fcm_tokens.extend(recipient.fcm_tokens)
        
        if not fcm_tokens:
            return DeliveryResult(
                channel=DeliveryChannel.FCM_PUSH,
                success=False,
                message_id=message.message_id,
                error="No FCM tokens available"
            )
        
        # Send to all tokens
        success_count = 0
        for token in fcm_tokens:
            try:
                await self.firebase_service.send_notification(
                    token=token,
                    title=message.title,
                    body=message.body,
                    data=message.content
                )
                success_count += 1
            except Exception as e:
                logger.warning(f" TITAN PROCESSING FAILED: send FCM to token {token[:10]}...: {e}")
        
        return DeliveryResult(
            channel=DeliveryChannel.FCM_PUSH,
            success=success_count > 0,
            message_id=message.message_id,
            delivery_time=datetime.now(timezone.utc),
            response_data={"tokens_sent": success_count, "total_tokens": len(fcm_tokens)}
        )
    
    async def _deliver_database_log(self, message: UnifiedMessage) -> DeliveryResult:
        """Log message to database"""
        # This would integrate with your database system
        # For now, we'll log to file as a placeholder
        
        log_entry = {
            "message_id": message.message_id,
            "message_type": message.message_type.value,
            "priority": message.priority.value,
            "title": message.title,
            "body": message.body,
            "content": message.content,
            "timestamp": message.timestamp.isoformat(),
            "titan_clash_id": message.titan_clash_id,
            "hero_id": message.hero_id
        }
        
        # Log to file (replace with database integration)
        log_file = Path(project_root) / "logs" / "messaging.log"
        log_file.parent.mkdir(exist_ok=True)
        
        with open(log_file, "a") as f:
            f.write(f"{json.dumps(log_entry)}\n")
        
        return DeliveryResult(
            channel=DeliveryChannel.MNEMOSYNE_LOG,
            success=True,
            message_id=message.message_id,
            delivery_time=datetime.now(timezone.utc)
        )
    
    def _determine_optimal_channels(self, message: UnifiedMessage) -> List[DeliveryChannel]:
        """Determine optimal delivery channels based on message properties"""
        channels = []
        
        # Always log to database
        channels.append(DeliveryChannel.MNEMOSYNE_LOG)
        
        # High priority messages use all available channels
        if message.priority in [MessagePriority.CRITICAL, MessagePriority.EMERGENCY]:
            channels.extend([
                DeliveryChannel.QUANTUM_MESSENGER,
                DeliveryChannel.FIREBASE_SERVICE,
                DeliveryChannel.FCM_PUSH
            ])
        
        # Basketball analytics use specialized channels
        elif message.message_type in [MessageType.BASKETBALL_ANALYTICS, MessageType.PREDICTION_ALERT]:
            channels.extend([
                DeliveryChannel.QUANTUM_MESSENGER,
                DeliveryChannel.FIREBASE_SERVICE
            ])
        
        # System notifications use FCM
        elif message.message_type == MessageType.SYSTEM_NOTIFICATION:
            channels.append(DeliveryChannel.FCM_PUSH)
        
        # Default to quantum messenger for other types
        else:
            channels.append(DeliveryChannel.QUANTUM_MESSENGER)
        
        # Filter to only active channels
        return [ch for ch in channels if self.active_channels.get(ch, False)]
    
    def _get_quantum_route(self, message_type: MessageType) -> VaultRoutes:
        """Map message type to quantum route"""
        route_mapping = {
            MessageType.GAME_ALERT: VaultRoutes.SNAKE_SIRENS,
            MessageType.PLAYER_ALERT: VaultRoutes.HEROIC_QUESTS,
            MessageType.PREDICTION_ALERT: VaultRoutes.MOIRAI_SIMULACRUM,
            MessageType.BASKETBALL_ANALYTICS: VaultRoutes.CHOSEN_ONES_REGISTRY,
            MessageType.QUANTUM_STATE: VaultRoutes.QUANTUM_ENTANGLEMENT,
            MessageType.EMERGENCY_ALERT: VaultRoutes.SNAKE_SIRENS,
        }
        
        return route_mapping.get(message_type, VaultRoutes.SNAKE_SIRENS)
    
    async def _process_message_queue(self) -> None:
        """Process messages from the queue with proper shutdown handling"""
        logger.info(" MEDUSA VAULT: Starting message queue processor")

        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Get message from queue with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(),
                    timeout=1.0
                )

                # Process the message
                await self.send_unified_message(message)

                # Mark task as done
                self.message_queue.task_done()

            except asyncio.TimeoutError:
                # No messages to process, check shutdown and continue
                if self.shutdown_event.is_set():
                    break
                continue
            except Exception as e:
                logger.error(f" Error processing message queue: {e}")
                await asyncio.sleep(1)

                # Check if we should shutdown
                if self.shutdown_event.is_set():
                    break

        logger.info(" MEDUSA VAULT: Message queue processor stopped")

    async def shutdown(self) -> None:
        """Shutdown the messaging orchestrator gracefully"""
        logger.info(" MEDUSA VAULT: Shutting down messaging orchestrator...")

        # Signal shutdown
        self.is_running = False
        self.shutdown_event.set()

        # Wait for queue processor to finish
        if self.queue_processor_task and not self.queue_processor_task.done():
            try:
                await asyncio.wait_for(self.queue_processor_task, timeout=5.0)
            except asyncio.TimeoutError:
                logger.warning(" MEDUSA VAULT: Queue processor shutdown timeout, cancelling...")
                self.queue_processor_task.cancel()

        logger.info(" MEDUSA VAULT: Messaging orchestrator shutdown complete")

    @oracle_focus
    async def queue_message(self, message: UnifiedMessage) -> None:
        """Queue a message for delivery"""
        await self.message_queue.put(message)
    
    @oracle_focus
    async def send_basketball_alert(
        self, 
        alert_type: str,
        title: str,
        body: str,
        titan_clash_id: Optional[str] = None,
        hero_id: Optional[str] = None,
        mythic_roster_id: Optional[str] = None,
        priority: MessagePriority = MessagePriority.NORMAL,
        recipients: Optional[List[MessageRecipient]] = None
    ) -> List[DeliveryResult]:
        """Send a basketball-specific alert with enhanced context"""
        
        # Determine message type based on alert_type
        type_mapping = {
            "injury": MessageType.PLAYER_ALERT,
            "trade": MessageType.PLAYER_ALERT,
            "game_start": MessageType.GAME_ALERT,
            "game_end": MessageType.GAME_ALERT,
            "prediction": MessageType.PREDICTION_ALERT,
            "analytics": MessageType.BASKETBALL_ANALYTICS
        }
        
        message_type = type_mapping.get(alert_type.lower(), MessageType.GAME_ALERT)
        
        # Enhance content with basketball context
        basketball_content = {
            "alert_type": alert_type,
            "sport": "basketball",
            "context": {
                "titan_clash_id": titan_clash_id,
                "hero_id": hero_id,
                "mythic_roster_id": mythic_roster_id,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        }
        
        # Create unified message
        message = UnifiedMessage(
            message_type=message_type,
            priority=priority,
            title=f" {title}",
            body=body,
            content=basketball_content,
            recipients=recipients or [],
            titan_clash_id=titan_clash_id,
            hero_id=hero_id,
            mythic_roster_id=mythic_roster_id,
            tags=["basketball", alert_type, "nba"]
        )
        
        return await self.send_unified_message(message)
    
    @oracle_focus
    async def send_alert(
        self,
        alert_type: str,
        title: str,
        body: str,
        **kwargs
    ) -> List[DeliveryResult]:
        """
        Generic alert method that delegates to send_basketball_alert.
        Provides compatibility for temporal flux stabilizer and other systems.
        """
        return await self.send_basketball_alert(
            alert_type=alert_type,
            title=title,
            body=body,
            **kwargs
        )
    
    @oracle_focus
    async def send_emergency_broadcast(
        self,
        title: str,
        body: str,
        titan_clash_id: Optional[str] = None,
        force_all_channels: bool = True
    ) -> List[DeliveryResult]:
        """Send emergency broadcast to all available channels"""
        
        channels = list(DeliveryChannel) if force_all_channels else []
        
        message = UnifiedMessage(
            message_type=MessageType.EMERGENCY_ALERT,
            priority=MessagePriority.EMERGENCY,
            title=f"🚨 EMERGENCY: {title}",
            body=body,
            content={
                "emergency": True,
                "broadcast": True,
                "titan_clash_id": titan_clash_id,
                "alert_level": "CRITICAL"
            },
            channels=channels,
            titan_clash_id=titan_clash_id,
            tags=["emergency", "broadcast", "critical"]
        )
        
        return await self.send_unified_message(message)
    
    @oracle_focus
    async def send_prediction_update(
        self,
        prediction_data: Dict[str, Any],
        confidence: float,
        titan_clash_id: str,
        model_name: str = "quantum_oracle"
    ) -> List[DeliveryResult]:
        """Send prediction update with quantum analytics"""
        
        title = f" Prediction Update: Game {titan_clash_id}"
        body = f"New prediction from {model_name} (confidence: {confidence:.1%})"
        
        message = UnifiedMessage(
            message_type=MessageType.PREDICTION_ALERT,
            priority=MessagePriority.HIGH if confidence > 0.8 else MessagePriority.NORMAL,
            title=title,
            body=body,
            content={
                "prediction_data": prediction_data,
                "confidence": confidence,
                "model_name": model_name,
                "quantum_enhanced": True
            },
            channels=[DeliveryChannel.QUANTUM_MESSENGER, DeliveryChannel.FIREBASE_SERVICE],
            titan_clash_id=titan_clash_id,
            tags=["prediction", "analytics", "quantum", model_name]
        )
        
        return await self.send_unified_message(message)
    
    async def get_delivery_analytics(self) -> Dict[str, Any]:
        """Get comprehensive delivery analytics"""
        
        # Calculate success rates by channel
        channel_stats = {}
        for result in self.delivery_history[-1000:]: # Last 1000 deliveries
            channel = result.channel.value
            if channel not in channel_stats:
                channel_stats[channel] = {"success": 0, "total": 0}
            
            channel_stats[channel]["total"] += 1
            if result.success:
                channel_stats[channel]["success"] += 1
        
        # Calculate success rates
        for channel in channel_stats:
            total = channel_stats[channel]["total"]
            success = channel_stats[channel]["success"]
            channel_stats[channel]["success_rate"] = (success / total * 100) if total > 0 else 0
        
        # Recent performance (last hour)
        recent_cutoff = datetime.now(timezone.utc) - timedelta(hours=1)
        recent_deliveries = [
            r for r in self.delivery_history 
            if r.delivery_time and r.delivery_time > recent_cutoff
        ]
        
        return {
            "total_messages_sent": self.message_count,
            "successful_deliveries": self.successful_deliveries,
            "failed_deliveries": self.failed_deliveries,
            "overall_success_rate": (
                self.successful_deliveries / max(1, self.message_count) * 100
            ),
            "channel_statistics": channel_stats,
            "active_channels": {
                channel.value: status for channel, status in self.active_channels.items()
            },
            "recent_performance": {
                "last_hour_deliveries": len(recent_deliveries),
                "last_hour_success_rate": (
                    sum(1 for r in recent_deliveries if r.success) / 
                    max(1, len(recent_deliveries)) * 100
                )
            },
            "queue_status": {
                "pending_messages": self.message_queue.qsize(),
                "queue_healthy": self.message_queue.qsize() < 100
            }
        }
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive health check of the messaging system"""
        
        health_status = {
            "overall_status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "services": {}
        }
        
        # Check Quantum Messenger
        if self.quantum_messenger:
            try:
                # Simple connectivity test
                health_status["services"]["quantum_messenger"] = {
                    "status": "healthy",
                    "active": self.active_channels.get(DeliveryChannel.QUANTUM_MESSENGER, False)
                }
            except Exception as e:
                health_status["services"]["quantum_messenger"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "active": False
                }
                health_status["overall_status"] = "degraded"
        
        # Check Firebase services
        if self.quantum_firebase:
            try:
                firebase_status = self.quantum_firebase.get_connection_status()
                health_status["services"]["firebase"] = {
                    "status": "healthy" if firebase_status.get("connected") else "unhealthy",
                    "details": firebase_status,
                    "active": self.active_channels.get(DeliveryChannel.FIREBASE_SERVICE, False)
                }
            except Exception as e:
                health_status["services"]["firebase"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "active": False
                }
                health_status["overall_status"] = "degraded"
        
        # Check FCM service
        if self.firebase_service:
            try:
                health_status["services"]["fcm"] = {
                    "status": "healthy",
                    "active": self.active_channels.get(DeliveryChannel.FCM_PUSH, False)
                }
            except Exception as e:
                health_status["services"]["fcm"] = {
                    "status": "unhealthy",
                    "error": str(e),
                    "active": False
                }
                health_status["overall_status"] = "degraded"
        
        # Check if any critical services are down
        critical_services_down = sum(
            1 for service in health_status["services"].values() 
            if not service.get("active", False)
        )
        
        if critical_services_down >= len(health_status["services"]):
            health_status["overall_status"] = "critical"
        elif critical_services_down > 0:
            health_status["overall_status"] = "degraded"
        
        return health_status

# Factory function for creating the orchestrator
def create_expert_messaging_orchestrator(config: Optional[Dict[str, Any]] = None) -> ExpertMessagingOrchestrator:
    """Create and configure the expert messaging orchestrator"""
    return ExpertMessagingOrchestrator(config)

# Global orchestrator instance
_global_orchestrator: Optional[ExpertMessagingOrchestrator] = None

async def get_messaging_orchestrator() -> ExpertMessagingOrchestrator:
    """Get the global messaging orchestrator instance"""
    global _global_orchestrator

    if _global_orchestrator is None:
        _global_orchestrator = create_expert_messaging_orchestrator()
        try:
            await _global_orchestrator.initialize_services()
        except Exception as e:
            logger.warning(f" MEDUSA VAULT: Failed to initialize messaging services: {e}")
            # Return orchestrator anyway for basic functionality

    return _global_orchestrator

def get_messaging_orchestrator_sync() -> ExpertMessagingOrchestrator:
    """Get messaging orchestrator without async initialization (for sync contexts)"""
    global _global_orchestrator

    if _global_orchestrator is None:
        _global_orchestrator = create_expert_messaging_orchestrator()
        logger.info(" MEDUSA VAULT: Created messaging orchestrator (services not initialized)")

    return _global_orchestrator

# Export main components
__all__ = [
    'ExpertMessagingOrchestrator',
    'UnifiedMessage',
    'MessageRecipient', 
    'DeliveryResult',
    'MessageType',
    'MessagePriority',
    'DeliveryChannel',
    'create_expert_messaging_orchestrator',
    'get_messaging_orchestrator'
]
