import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from pydantic import BaseModel, Field
from enum import Enum
from backend.middleware.auth_middleware import get_expert_auth_context, ExpertAuthContext
from backend.middleware.tier_enforcement import require_tier
from backend.middleware.feature_flags import UserTier
from backend.services.subscription_management_service import (
from backend.database.models import UserModel as User

    subscription_service, 
    BillingCycle, 
    SubscriptionStatus,
    SubscriptionPlan
)

"""
🔐 HYPER MEDUSA NEURAL VAULT - Subscription & Billing Router
===========================================================
Comprehensive SaaS subscription management and billing API endpoints.

Features:
- Subscription creation and management
- Tier upgrades and downgrades
- Billing and payment processing
- Revenue analytics and reporting
- Customer lifecycle management
"""

# Configure logging
logger = logging.getLogger("hyper_medusa_neural_vault.subscription_billing")

# Router configuration
router = APIRouter(
    prefix="/api/v1/subscriptions",
    tags=["💼 SaaS Subscription & Billing"],
    responses={
        404: {"description": "Subscription not found"},
        402: {"description": "Payment required"},
        500: {"description": "Internal server error"}
    }
)

# ===================================================================
# PYDANTIC MODELS
# ===================================================================

class SubscriptionCreateRequest(BaseModel):
    """Request model for creating a subscription"""
    tier: UserTier = Field(..., description="Subscription tier")
    billing_cycle: BillingCycle = Field(default=BillingCycle.MONTHLY, description="Billing cycle")
    payment_method: str = Field(default="stripe", description="Payment method")
    promo_code: Optional[str] = Field(None, description="Promotional code")

class SubscriptionUpgradeRequest(BaseModel):
    """Request model for upgrading a subscription"""
    new_tier: UserTier = Field(..., description="New subscription tier")
    billing_cycle: Optional[BillingCycle] = Field(None, description="New billing cycle")

class SubscriptionCancelRequest(BaseModel):
    """Request model for cancelling a subscription"""
    immediate: bool = Field(default=False, description="Cancel immediately or at period end")
    reason: Optional[str] = Field(None, description="Cancellation reason")

class SubscriptionResponse(BaseModel):
    """Response model for subscription operations"""
    success: bool
    subscription_id: Optional[str] = None
    tier: Optional[str] = None
    status: Optional[str] = None
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None

class PlanResponse(BaseModel):
    """Response model for subscription plans"""
    tier: str
    name: str
    description: str
    monthly_price: float
    quarterly_price: float
    annual_price: float
    features: List[str]
    limits: Dict[str, Any]
    trial_days: int

# ===================================================================
# SUBSCRIPTION MANAGEMENT ENDPOINTS
# ===================================================================

@router.get("/plans", response_model=List[PlanResponse])
async def get_subscription_plans():
    """Get all available subscription plans"""
    try:
        plans = []
        for tier, plan in subscription_service.subscription_plans.items():
            plans.append(PlanResponse(
                tier=tier.value,
                name=plan.name,
                description=plan.description,
                monthly_price=float(plan.monthly_price),
                quarterly_price=float(plan.quarterly_price),
                annual_price=float(plan.annual_price),
                features=plan.features,
                limits=plan.limits,
                trial_days=plan.trial_days
            ))
        
        return plans
        
    except Exception as e:
        logger.error(f"❌ Failed to get subscription plans: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve subscription plans")

@router.post("/create", response_model=SubscriptionResponse)
async def create_subscription(
    request: SubscriptionCreateRequest,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Create a new subscription for the authenticated user"""
    try:
        result = await subscription_service.create_subscription(
            user_id=auth_context.user_id,
            tier=request.tier,
            billing_cycle=request.billing_cycle,
            payment_method=request.payment_method
        )
        
        if result["success"]:
            return SubscriptionResponse(
                success=True,
                subscription_id=result["subscription_id"],
                tier=result["tier"],
                status=result["status"],
                message=f"Successfully created {request.tier.value} subscription",
                data=result
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to create subscription: {result.get('error', 'Unknown error')}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to create subscription for user {auth_context.user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to create subscription")

@router.get("/status", response_model=Dict[str, Any])
async def get_subscription_status(
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Get current subscription status for the authenticated user"""
    try:
        status = await subscription_service.get_subscription_status(auth_context.user_id)
        return status
        
    except Exception as e:
        logger.error(f"❌ Failed to get subscription status for user {auth_context.user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve subscription status")

@router.post("/upgrade", response_model=SubscriptionResponse)
async def upgrade_subscription(
    request: SubscriptionUpgradeRequest,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Upgrade user subscription to a higher tier"""
    try:
        result = await subscription_service.upgrade_subscription(
            user_id=auth_context.user_id,
            new_tier=request.new_tier,
            billing_cycle=request.billing_cycle
        )
        
        if result["success"]:
            return SubscriptionResponse(
                success=True,
                tier=result["new_tier"],
                message=f"Successfully upgraded to {request.new_tier.value}",
                data=result
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to upgrade subscription: {result.get('error', 'Unknown error')}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to upgrade subscription for user {auth_context.user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to upgrade subscription")

@router.post("/cancel", response_model=SubscriptionResponse)
async def cancel_subscription(
    request: SubscriptionCancelRequest,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Cancel user subscription"""
    try:
        result = await subscription_service.cancel_subscription(
            user_id=auth_context.user_id,
            immediate=request.immediate
        )
        
        if result["success"]:
            return SubscriptionResponse(
                success=True,
                message="Subscription cancelled successfully",
                data=result
            )
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to cancel subscription: {result.get('error', 'Unknown error')}"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to cancel subscription for user {auth_context.user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to cancel subscription")

# ===================================================================
# BILLING AND PAYMENT ENDPOINTS
# ===================================================================

@router.get("/billing/history")
async def get_billing_history(
    limit: int = Query(default=10, ge=1, le=100, description="Number of records to return"),
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Get billing history for the authenticated user"""
    try:
        # Filter payment history for the user
        user_payments = [
            {
                "payment_id": payment.payment_id,
                "amount": float(payment.amount),
                "currency": payment.currency,
                "status": payment.status.value,
                "billing_cycle": payment.billing_cycle.value,
                "payment_method": payment.payment_method,
                "created_at": payment.created_at.isoformat() if payment.created_at else None,
                "processed_at": payment.processed_at.isoformat() if payment.processed_at else None
            }
            for payment in subscription_service.payment_history
            if payment.user_id == auth_context.user_id
        ]
        
        # Sort by creation date and limit
        user_payments.sort(key=lambda x: x["created_at"] or "", reverse=True)
        
        return {
            "payments": user_payments[:limit],
            "total_count": len(user_payments)
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get billing history for user {auth_context.user_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve billing history")

@router.post("/billing/process")
@require_tier(UserTier.ENTERPRISE, feature="admin_billing")
async def process_billing_cycle(
    background_tasks: BackgroundTasks,
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Process billing cycle for all subscriptions (Enterprise admin only)"""
    try:
        # Add billing processing to background tasks
        background_tasks.add_task(subscription_service.process_billing_cycle)
        
        return {
            "success": True,
            "message": "Billing cycle processing started",
            "initiated_by": auth_context.user_id,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to initiate billing cycle processing: {e}")
        raise HTTPException(status_code=500, detail="Failed to process billing cycle")

# ===================================================================
# ANALYTICS AND REPORTING ENDPOINTS
# ===================================================================

@router.get("/analytics/revenue")
@require_tier(UserTier.ENTERPRISE, feature="revenue_analytics")
async def get_revenue_analytics(
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Get comprehensive revenue analytics (Enterprise only)"""
    try:
        analytics = await subscription_service.get_revenue_analytics()
        
        return {
            "success": True,
            "analytics": analytics,
            "generated_at": datetime.now().isoformat(),
            "generated_by": auth_context.user_id
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get revenue analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve revenue analytics")

@router.get("/analytics/customers")
@require_tier(UserTier.PRO, feature="customer_analytics")
async def get_customer_analytics(
    auth_context: ExpertAuthContext = Depends(get_expert_auth_context)
):
    """Get customer analytics (Pro+ only)"""
    try:
        analytics = await subscription_service.get_revenue_analytics()
        
        # Return only customer-related metrics for Pro tier
        customer_data = {
            "customer_metrics": analytics.get("customer_metrics", {}),
            "conversion_metrics": analytics.get("conversion_metrics", {}),
            "tier_distribution": analytics.get("customer_metrics", {}).get("tier_distribution", {})
        }
        
        return {
            "success": True,
            "customer_analytics": customer_data,
            "generated_at": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"❌ Failed to get customer analytics: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve customer analytics")
