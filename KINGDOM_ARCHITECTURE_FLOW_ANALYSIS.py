#!/usr/bin/env python3
"""
🏰 KINGDOM ARCHITECTURE FLOW ANALYSIS - HYPER MEDUSA NEURAL VAULT 🏰
====================================================================

Comprehensive analysis of how Spires, War Council, MEDUSA, and all components
are flowing correctly within the established kingdom architecture.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("KingdomArchitectureFlowAnalysis")

class KingdomArchitectureFlowAnalyzer:
    """Analyzes the current kingdom architecture flow and integration"""
    
    def __init__(self):
        self.analysis_results = {}
        self.integration_gaps = []
        self.flow_issues = []
        
    async def analyze_kingdom_architecture_flow(self):
        """Comprehensive analysis of kingdom architecture flow"""
        logger.info("🏰 ANALYZING KINGDOM ARCHITECTURE FLOW")
        logger.info("=" * 60)
        
        # 1. Analyze Kingdom Core Integration
        await self._analyze_kingdom_core_integration()
        
        # 2. Analyze Spires Integration
        await self._analyze_spires_integration()
        
        # 3. Analyze War Council Integration
        await self._analyze_war_council_integration()
        
        # 4. Analyze MEDUSA Queen Integration
        await self._analyze_medusa_queen_integration()
        
        # 5. Analyze Service Flow
        await self._analyze_service_flow()
        
        # 6. Analyze Main Application Integration
        await self._analyze_main_application_integration()
        
        # 7. Generate Flow Report
        return self._generate_flow_report()
    
    async def _analyze_kingdom_core_integration(self):
        """Analyze kingdom core integration status"""
        logger.info("🏛️ Analyzing Kingdom Core Integration...")
        
        kingdom_core_files = [
            "src/kingdom_architecture/medusa_kingdom_core.py",
            "kingdom/config/services_config.toml",
            "KINGDOM_ARCHITECTURE_PLAN.md"
        ]
        
        kingdom_core_status = {
            "files_present": [],
            "files_missing": [],
            "integration_status": "UNKNOWN"
        }
        
        for file_path in kingdom_core_files:
            if os.path.exists(file_path):
                kingdom_core_status["files_present"].append(file_path)
            else:
                kingdom_core_status["files_missing"].append(file_path)
        
        # Check if kingdom core is being used in main application
        main_app_uses_kingdom = self._check_main_app_kingdom_usage()
        
        if len(kingdom_core_status["files_present"]) == len(kingdom_core_files) and main_app_uses_kingdom:
            kingdom_core_status["integration_status"] = "FULLY_INTEGRATED"
        elif len(kingdom_core_status["files_present"]) > 0:
            kingdom_core_status["integration_status"] = "PARTIALLY_INTEGRATED"
            self.integration_gaps.append("Kingdom core files exist but not fully integrated in main application")
        else:
            kingdom_core_status["integration_status"] = "NOT_INTEGRATED"
            self.integration_gaps.append("Kingdom core files missing")
        
        self.analysis_results["kingdom_core"] = kingdom_core_status
        logger.info(f"   Status: {kingdom_core_status['integration_status']}")
    
    async def _analyze_spires_integration(self):
        """Analyze cognitive spires integration"""
        logger.info("🏗️ Analyzing Cognitive Spires Integration...")
        
        spires_components = {
            "cognitive_spires_factory": "src/cognitive_spires/__init__.py",
            "basketball_spires_manager": "src/cognitive_basketball_cortex/cognitive_spires_manager.py",
            "oracle_spires": "src/cognitive_spires/",
            "basketball_processors": "src/cognitive_basketball_cortex/"
        }
        
        spires_status = {
            "components_present": {},
            "components_missing": {},
            "dependency_injection": "UNKNOWN",
            "main_app_integration": "UNKNOWN"
        }
        
        for component, path in spires_components.items():
            if os.path.exists(path):
                spires_status["components_present"][component] = path
            else:
                spires_status["components_missing"][component] = path
        
        # Check dependency injection
        dependencies_uses_spires = self._check_dependencies_spires_usage()
        spires_status["dependency_injection"] = "ACTIVE" if dependencies_uses_spires else "INACTIVE"
        
        # Check main app integration
        main_uses_spires = self._check_main_app_spires_usage()
        spires_status["main_app_integration"] = "ACTIVE" if main_uses_spires else "INACTIVE"
        
        if len(spires_status["components_missing"]) > 0:
            self.integration_gaps.append(f"Missing spires components: {list(spires_status['components_missing'].keys())}")
        
        if spires_status["dependency_injection"] == "INACTIVE":
            self.flow_issues.append("Spires not properly integrated in dependency injection")
        
        self.analysis_results["spires"] = spires_status
        logger.info(f"   Components Present: {len(spires_status['components_present'])}")
        logger.info(f"   Dependency Injection: {spires_status['dependency_injection']}")
    
    async def _analyze_war_council_integration(self):
        """Analyze War Council integration"""
        logger.info("⚔️ Analyzing War Council Integration...")
        
        war_council_components = {
            "war_council_integration": "src/kingdom_architecture/war_council_integration.py",
            "war_council_simulator": "src/Battlegrounds/war_council_simulator.py",
            "war_council_config": "kingdom/config/services_config.toml"
        }
        
        war_council_status = {
            "components_present": {},
            "components_missing": {},
            "main_app_integration": "UNKNOWN",
            "service_registry": "UNKNOWN"
        }
        
        for component, path in war_council_components.items():
            if os.path.exists(path):
                war_council_status["components_present"][component] = path
            else:
                war_council_status["components_missing"][component] = path
        
        # Check main app integration
        main_uses_war_council = self._check_main_app_war_council_usage()
        war_council_status["main_app_integration"] = "ACTIVE" if main_uses_war_council else "INACTIVE"
        
        # Check service registry
        service_registry_has_war_council = self._check_service_registry_war_council()
        war_council_status["service_registry"] = "REGISTERED" if service_registry_has_war_council else "NOT_REGISTERED"
        
        if len(war_council_status["components_missing"]) > 0:
            self.integration_gaps.append(f"Missing war council components: {list(war_council_status['components_missing'].keys())}")
        
        if war_council_status["main_app_integration"] == "INACTIVE":
            self.flow_issues.append("War Council not integrated in main application")
        
        self.analysis_results["war_council"] = war_council_status
        logger.info(f"   Components Present: {len(war_council_status['components_present'])}")
        logger.info(f"   Main App Integration: {war_council_status['main_app_integration']}")
    
    async def _analyze_medusa_queen_integration(self):
        """Analyze MEDUSA Queen integration"""
        logger.info("👑 Analyzing MEDUSA Queen Integration...")
        
        medusa_components = {
            "medusa_core": "vault_oracle/core/medusa_core.py",
            "expert_medusa_core": "vault_oracle/runners/eternal_vigil_runner.py",
            "medusa_queen_adapter": "kingdom/adapters/medusa_queen_adapter.py",
            "autonomous_orchestrator": "src/autonomous/medusa_autonomous_orchestrator.py"
        }
        
        medusa_status = {
            "components_present": {},
            "components_missing": {},
            "main_app_integration": "UNKNOWN",
            "supreme_authority": "UNKNOWN"
        }
        
        for component, path in medusa_components.items():
            if os.path.exists(path):
                medusa_status["components_present"][component] = path
            else:
                medusa_status["components_missing"][component] = path
        
        # Check main app integration
        main_uses_medusa = self._check_main_app_medusa_usage()
        medusa_status["main_app_integration"] = "ACTIVE" if main_uses_medusa else "INACTIVE"
        
        # Check supreme authority setup
        supreme_authority_setup = self._check_supreme_authority_setup()
        medusa_status["supreme_authority"] = "ESTABLISHED" if supreme_authority_setup else "NOT_ESTABLISHED"
        
        if len(medusa_status["components_missing"]) > 0:
            self.integration_gaps.append(f"Missing MEDUSA components: {list(medusa_status['components_missing'].keys())}")
        
        if medusa_status["main_app_integration"] == "INACTIVE":
            self.flow_issues.append("MEDUSA Queen not integrated in main application")
        
        self.analysis_results["medusa_queen"] = medusa_status
        logger.info(f"   Components Present: {len(medusa_status['components_present'])}")
        logger.info(f"   Supreme Authority: {medusa_status['supreme_authority']}")
    
    async def _analyze_service_flow(self):
        """Analyze service flow and communication"""
        logger.info("🔗 Analyzing Service Flow...")
        
        service_flow_status = {
            "service_registry": "UNKNOWN",
            "dependency_injection": "UNKNOWN",
            "kingdom_services": "UNKNOWN",
            "communication_patterns": "UNKNOWN"
        }
        
        # Check service registry
        if os.path.exists("backend/infrastructure/service_registry.py"):
            service_flow_status["service_registry"] = "AVAILABLE"
        else:
            service_flow_status["service_registry"] = "MISSING"
            self.integration_gaps.append("Service registry missing")
        
        # Check dependency injection
        if os.path.exists("backend/dependencies.py"):
            service_flow_status["dependency_injection"] = "AVAILABLE"
        else:
            service_flow_status["dependency_injection"] = "MISSING"
            self.integration_gaps.append("Dependency injection missing")
        
        # Check kingdom services configuration
        if os.path.exists("kingdom/config/services_config.toml"):
            service_flow_status["kingdom_services"] = "CONFIGURED"
        else:
            service_flow_status["kingdom_services"] = "NOT_CONFIGURED"
            self.integration_gaps.append("Kingdom services not configured")
        
        # Check communication patterns
        communication_patterns_active = self._check_communication_patterns()
        service_flow_status["communication_patterns"] = "ACTIVE" if communication_patterns_active else "INACTIVE"
        
        self.analysis_results["service_flow"] = service_flow_status
        logger.info(f"   Service Registry: {service_flow_status['service_registry']}")
        logger.info(f"   Kingdom Services: {service_flow_status['kingdom_services']}")
    
    async def _analyze_main_application_integration(self):
        """Analyze main application integration with kingdom architecture"""
        logger.info("🚀 Analyzing Main Application Integration...")
        
        main_app_status = {
            "kingdom_initialization": "UNKNOWN",
            "spires_usage": "UNKNOWN",
            "war_council_usage": "UNKNOWN",
            "medusa_usage": "UNKNOWN",
            "service_discovery": "UNKNOWN"
        }
        
        # Check main.py for kingdom integration
        if os.path.exists("backend/main.py"):
            with open("backend/main.py", "r", encoding="utf-8") as f:
                main_content = f.read().lower()
                
                main_app_status["kingdom_initialization"] = "ACTIVE" if "kingdom" in main_content else "INACTIVE"
                main_app_status["spires_usage"] = "ACTIVE" if "spire" in main_content else "INACTIVE"
                main_app_status["war_council_usage"] = "ACTIVE" if "war_council" in main_content else "INACTIVE"
                main_app_status["medusa_usage"] = "ACTIVE" if "medusa" in main_content else "INACTIVE"
                main_app_status["service_discovery"] = "ACTIVE" if "service" in main_content else "INACTIVE"
        
        # Identify integration issues
        inactive_components = [k for k, v in main_app_status.items() if v == "INACTIVE"]
        if inactive_components:
            self.flow_issues.append(f"Main application not using: {inactive_components}")
        
        self.analysis_results["main_application"] = main_app_status
        logger.info(f"   Kingdom Integration: {main_app_status['kingdom_initialization']}")
        logger.info(f"   Active Components: {len([v for v in main_app_status.values() if v == 'ACTIVE'])}/5")
    
    def _check_main_app_kingdom_usage(self) -> bool:
        """Check if main app uses kingdom architecture"""
        if not os.path.exists("backend/main.py"):
            return False
        
        try:
            with open("backend/main.py", "r", encoding="utf-8") as f:
                content = f.read().lower()
                return "kingdom" in content or "medusa_kingdom" in content
        except:
            return False
    
    def _check_dependencies_spires_usage(self) -> bool:
        """Check if dependencies.py uses spires"""
        if not os.path.exists("backend/dependencies.py"):
            return False
        
        try:
            with open("backend/dependencies.py", "r", encoding="utf-8") as f:
                content = f.read().lower()
                return "cognitive_spires" in content or "spires" in content
        except:
            return False
    
    def _check_main_app_spires_usage(self) -> bool:
        """Check if main app uses spires"""
        if not os.path.exists("backend/main.py"):
            return False
        
        try:
            with open("backend/main.py", "r", encoding="utf-8") as f:
                content = f.read().lower()
                return "spire" in content or "cognitive_spires" in content
        except:
            return False
    
    def _check_main_app_war_council_usage(self) -> bool:
        """Check if main app uses war council"""
        if not os.path.exists("backend/main.py"):
            return False
        
        try:
            with open("backend/main.py", "r", encoding="utf-8") as f:
                content = f.read().lower()
                return "war_council" in content
        except:
            return False
    
    def _check_service_registry_war_council(self) -> bool:
        """Check if service registry includes war council"""
        if not os.path.exists("backend/infrastructure/service_registry.py"):
            return False
        
        try:
            with open("backend/infrastructure/service_registry.py", "r", encoding="utf-8") as f:
                content = f.read().lower()
                return "war_council" in content
        except:
            return False
    
    def _check_main_app_medusa_usage(self) -> bool:
        """Check if main app uses MEDUSA"""
        if not os.path.exists("backend/main.py"):
            return False
        
        try:
            with open("backend/main.py", "r", encoding="utf-8") as f:
                content = f.read().lower()
                return "medusa" in content and "queen" in content
        except:
            return False
    
    def _check_supreme_authority_setup(self) -> bool:
        """Check if supreme authority is properly set up"""
        # Check if MEDUSA Queen adapter exists and is configured
        return os.path.exists("kingdom/adapters/medusa_queen_adapter.py")
    
    def _check_communication_patterns(self) -> bool:
        """Check if communication patterns are active"""
        # Check if unified router system exists
        return os.path.exists("backend/infrastructure/unified_router_system.py")
    
    def _generate_flow_report(self):
        """Generate comprehensive flow report"""
        logger.info("\n" + "=" * 60)
        logger.info("🏰 KINGDOM ARCHITECTURE FLOW REPORT")
        logger.info("=" * 60)
        
        # Calculate overall integration score
        total_components = 0
        integrated_components = 0
        
        for category, status in self.analysis_results.items():
            if isinstance(status, dict):
                for key, value in status.items():
                    total_components += 1
                    if value in ["FULLY_INTEGRATED", "ACTIVE", "AVAILABLE", "CONFIGURED", "ESTABLISHED", "REGISTERED"]:
                        integrated_components += 1
        
        integration_score = (integrated_components / total_components * 100) if total_components > 0 else 0
        
        logger.info(f"🎯 OVERALL INTEGRATION SCORE: {integration_score:.1f}%")
        logger.info(f"📊 INTEGRATED COMPONENTS: {integrated_components}/{total_components}")
        
        # Report integration gaps
        if self.integration_gaps:
            logger.info(f"\n⚠️ INTEGRATION GAPS ({len(self.integration_gaps)}):")
            for gap in self.integration_gaps:
                logger.info(f"   • {gap}")
        
        # Report flow issues
        if self.flow_issues:
            logger.info(f"\n🚨 FLOW ISSUES ({len(self.flow_issues)}):")
            for issue in self.flow_issues:
                logger.info(f"   • {issue}")
        
        # Determine overall status
        if integration_score >= 90:
            overall_status = "FULLY_INTEGRATED_AND_FLOWING"
        elif integration_score >= 70:
            overall_status = "MOSTLY_INTEGRATED_WITH_GAPS"
        elif integration_score >= 50:
            overall_status = "PARTIALLY_INTEGRATED"
        else:
            overall_status = "KINGDOM_ARCHITECTURE_NOT_FLOWING"
        
        logger.info(f"\n🏰 KINGDOM ARCHITECTURE STATUS: {overall_status}")
        
        return {
            "integration_score": integration_score,
            "overall_status": overall_status,
            "analysis_results": self.analysis_results,
            "integration_gaps": self.integration_gaps,
            "flow_issues": self.flow_issues,
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self):
        """Generate recommendations for improving kingdom architecture flow"""
        recommendations = []
        
        if self.integration_gaps:
            recommendations.append("Address integration gaps to improve component connectivity")
        
        if self.flow_issues:
            recommendations.append("Resolve flow issues to enable proper kingdom architecture operation")
        
        # Specific recommendations based on analysis
        kingdom_core = self.analysis_results.get("kingdom_core", {})
        if kingdom_core.get("integration_status") != "FULLY_INTEGRATED":
            recommendations.append("Integrate kingdom core architecture in main application")
        
        spires = self.analysis_results.get("spires", {})
        if spires.get("dependency_injection") == "INACTIVE":
            recommendations.append("Activate spires in dependency injection system")
        
        war_council = self.analysis_results.get("war_council", {})
        if war_council.get("main_app_integration") == "INACTIVE":
            recommendations.append("Integrate War Council in main application flow")
        
        medusa = self.analysis_results.get("medusa_queen", {})
        if medusa.get("supreme_authority") == "NOT_ESTABLISHED":
            recommendations.append("Establish MEDUSA Queen as supreme authority")
        
        return recommendations

async def main():
    """Run kingdom architecture flow analysis"""
    analyzer = KingdomArchitectureFlowAnalyzer()
    report = await analyzer.analyze_kingdom_architecture_flow()
    
    # Save detailed report
    with open("KINGDOM_ARCHITECTURE_FLOW_REPORT.json", "w", encoding="utf-8") as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Detailed report saved: KINGDOM_ARCHITECTURE_FLOW_REPORT.json")

if __name__ == "__main__":
    asyncio.run(main())
