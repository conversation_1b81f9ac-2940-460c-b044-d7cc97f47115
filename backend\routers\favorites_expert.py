import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from enum import Enum
from fastapi import APIRouter, HTTPException, Query, Request, Depends, BackgroundTasks
from pydantic import BaseModel, Field, validator
import redis.asyncio as redis
from backend.auth.dependencies import get_expert_context
from backend.auth.dependencies import User
from src.cognitive_spires.schemas.ChosenOnesRegistry import FavoriteItem, LiveWatchStatus
from backend.database.models import UserModel as User


"""
HYPER MEDUSA NEURAL VAULT™ - Expert Favorites Router
====================================================
Enterprise-grade user favorites with intelligent recommendations
Version: 1.0.0 | Classification: EXPERT
"""




# Configure expert logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("HYPER_MEDUSA_FAVORITES")

# Expert router with versioned API
router = APIRouter(
    prefix="/api/v1/favorites",
    tags=["HYPER MEDUSA - Favorites Intelligence"],
    responses={
        404: {"description": "Favorites not found"},
        500: {"description": "Neural vault processing error"}
    }
)

class FavoriteType(str, Enum):
    """Expert favorite type classifications"""
    PLAYER = "player"
    TEAM = "team"
    GAME = "game"
    PROP_BET = "prop_bet"
    PARLAY = "parlay"
    MARKET = "market"
    STRATEGY = "strategy"
    MODEL = "model"

class WatchStatus(str, Enum):
    """Watch status classifications"""
    ACTIVE = "active"
    PAUSED = "paused"
    TRIGGERED = "triggered"
    EXPIRED = "expired"
    COMPLETED = "completed"

class PriorityLevel(str, Enum):
    """Priority level classifications"""
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    MONITORING = "monitoring"

class ExpertFavorite(BaseModel):
    """Expert favorite model"""
    favorite_id: str = Field(..., description="Unique favorite identifier")
    vault_user_id: str = Field(..., description="User identifier")
    favorite_type: FavoriteType = Field(..., description="Type of favorite")
    target_id: str = Field(..., description="Target entity ID")
    label: str = Field(..., description="User-defined label")

    # Enhanced metadata
    description: Optional[str] = Field(None, description="Detailed description")
    tags: List[str] = Field(default_factory=list, description="User tags")
    priority: PriorityLevel = Field(PriorityLevel.MEDIUM, description="Priority level")
    watch_status: WatchStatus = Field(WatchStatus.ACTIVE, description="Watch status")

    # Neural insights
    neural_score: float = Field(0.0, description="Neural relevance score")
    performance_metrics: Dict[str, float] = Field(default_factory=dict, description="Performance tracking")
    ai_insights: List[str] = Field(default_factory=list, description="AI-generated insights")
    recommendations: List[str] = Field(default_factory=list, description="Smart recommendations")

    # Alert settings
    alert_enabled: bool = Field(True, description="Alert notifications enabled")
    alert_conditions: Dict[str, Any] = Field(default_factory=dict, description="Alert trigger conditions")
    alert_frequency: str = Field("real_time", description="Alert frequency setting")

    # Tracking data
    view_count: int = Field(0, description="Number of views")
    last_viewed: Optional[datetime] = Field(None, description="Last viewed timestamp")
    chronicle_timestamp: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    last_prophecy_update: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")

    # Performance tracking
    success_rate: float = Field(0.0, description="Success rate if applicable")
    roi: float = Field(0.0, description="Return on investment if applicable")
    win_streak: int = Field(0, description="Current win streak")
    total_profit: float = Field(0.0, description="Total profit if applicable")

class ExpertFavoriteRequest(BaseModel):
    """Expert favorite request model"""
    favorite_type: FavoriteType = Field(..., description="Type of favorite")
    target_id: str = Field(..., description="Target entity ID")
    label: str = Field(..., description="User label")
    description: Optional[str] = Field(None, description="Description")
    tags: List[str] = Field(default_factory=list, description="Tags")
    priority: PriorityLevel = Field(PriorityLevel.MEDIUM, description="Priority")
    alert_enabled: bool = Field(True, description="Enable alerts")
    alert_conditions: Dict[str, Any] = Field(default_factory=dict, description="Alert conditions")

class FavoritesAnalytics(BaseModel):
    """Favorites analytics model"""
    total_favorites: int = Field(..., description="Total favorites count")
    active_watches: int = Field(..., description="Active watch count")
    top_categories: List[str] = Field(..., description="Top favorite categories")
    performance_summary: Dict[str, float] = Field(..., description="Performance summary")
    alert_stats: Dict[str, int] = Field(..., description="Alert statistics")
    neural_insights: List[str] = Field(..., description="AI insights")
    recommendations: List[str] = Field(..., description="Smart recommendations")

def get_user_id():
    """Get current user ID - replace with real auth logic"""
    return "expert_user_001"

@router.get(
    "/",
    response_model=List[ExpertFavorite],
    summary="📌 Get all user favorites with neural insights",
    description="Retrieve all user favorites enhanced with HYPER MEDUSA neural analysis"
)
async def get_favorites(
    favorite_type: Optional[FavoriteType] = Query(None, description="Filter by type"),
    priority: Optional[PriorityLevel] = Query(None, description="Filter by priority"),
    watch_status: Optional[WatchStatus] = Query(None, description="Filter by status"),
    limit: int = Query(100, ge=1, le=500, description="Results limit"),
    vault_user_id: str = Depends(get_user_id),
    ctx=Depends(get_expert_context)
):
    """Get all user favorites with neural insights"""
    try:
        # Get favorites from database
        favorites_data = await ctx.prediction_service.get_user_favorites({
            "vault_user_id": vault_user_id,
            "favorite_type": favorite_type.value if favorite_type else None,
            "priority": priority.value if priority else None,
            "watch_status": watch_status.value if watch_status else None,
            "limit": limit
        })

        # Enhance with neural insights
        enhanced_favorites = []
        for fav_data in favorites_data:
            # Get neural insights for this favorite
            neural_data = await ctx.prediction_service.get_favorite_insights({
                "favorite_id": fav_data.get("favorite_id"),
                "target_id": fav_data.get("target_id"),
                "favorite_type": fav_data.get("favorite_type")
            })

            enhanced_favorite = ExpertFavorite(
                favorite_id=fav_data.get("favorite_id", ""),
                vault_user_id=vault_user_id,
                favorite_type=FavoriteType(fav_data.get("favorite_type", "player")),
                target_id=fav_data.get("target_id", ""),
                label=fav_data.get("label", ""),
                description=fav_data.get("description"),
                tags=fav_data.get("tags", []),
                priority=PriorityLevel(fav_data.get("priority", "medium")),
                watch_status=WatchStatus(fav_data.get("watch_status", "active")),
                neural_score=neural_data.get("neural_score", 0.0),
                performance_metrics=neural_data.get("performance_metrics", {}),
                ai_insights=neural_data.get("ai_insights", []),
                recommendations=neural_data.get("recommendations", []),
                alert_enabled=fav_data.get("alert_enabled", True),
                alert_conditions=fav_data.get("alert_conditions", {}),
                alert_frequency=fav_data.get("alert_frequency", "real_time"),
                view_count=fav_data.get("view_count", 0),
                last_viewed=fav_data.get("last_viewed"),
                chronicle_timestamp=fav_data.get("chronicle_timestamp", datetime.utcnow()),
                last_prophecy_update=fav_data.get("last_prophecy_update", datetime.utcnow()),
                success_rate=neural_data.get("success_rate", 0.0),
                roi=neural_data.get("roi", 0.0),
                win_streak=neural_data.get("win_streak", 0),
                total_profit=neural_data.get("total_profit", 0.0)
            )
            enhanced_favorites.append(enhanced_favorite)

        logger.info(f"HYPER MEDUSA: Retrieved {len(enhanced_favorites)} favorites for user {vault_user_id}")
        return enhanced_favorites

    except Exception as e:
        logger.error(f"Get favorites error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Favorites retrieval error: {str(e)}")

@router.post(
    "/add",
    response_model=ExpertFavorite,
    summary="➕ Add new favorite with neural enhancement",
    description="Add a new favorite with intelligent categorization and alert setup"
)
async def add_favorite(
    favorite_request: ExpertFavoriteRequest,
    background_tasks: BackgroundTasks,
    vault_user_id: str = Depends(get_user_id),
    ctx=Depends(get_expert_context)
):
    """Add a new favorite with neural enhancement"""
    try:
        # Create favorite ID
        favorite_id = f"fav_{vault_user_id}_{favorite_request.target_id}_{datetime.utcnow().strftime('%Y%m%d%H%M%S')}"

        # Add to database
        favorite_data = await ctx.prediction_service.add_favorite({
            "favorite_id": favorite_id,
            "vault_user_id": vault_user_id,
            "favorite_type": favorite_request.favorite_type.value,
            "target_id": favorite_request.target_id,
            "label": favorite_request.label,
            "description": favorite_request.description,
            "tags": favorite_request.tags,
            "priority": favorite_request.priority.value,
            "alert_enabled": favorite_request.alert_enabled,
            "alert_conditions": favorite_request.alert_conditions
        })

        # Get neural insights
        neural_data = await ctx.prediction_service.get_favorite_insights({
            "favorite_id": favorite_id,
            "target_id": favorite_request.target_id,
            "favorite_type": favorite_request.favorite_type.value
        })

        # Create enhanced favorite
        enhanced_favorite = ExpertFavorite(
            favorite_id=favorite_id,
            vault_user_id=vault_user_id,
            favorite_type=favorite_request.favorite_type,
            target_id=favorite_request.target_id,
            label=favorite_request.label,
            description=favorite_request.description,
            tags=favorite_request.tags,
            priority=favorite_request.priority,
            watch_status=WatchStatus.ACTIVE,
            neural_score=neural_data.get("neural_score", 0.0),
            performance_metrics=neural_data.get("performance_metrics", {}),
            ai_insights=neural_data.get("ai_insights", []),
            recommendations=neural_data.get("recommendations", []),
            alert_enabled=favorite_request.alert_enabled,
            alert_conditions=favorite_request.alert_conditions
        )

        # Setup alerts in background
        if favorite_request.alert_enabled:
            background_tasks.add_task(
                setup_favorite_alerts,
                favorite_id,
                favorite_request.alert_conditions
            )

        logger.info(f"HYPER MEDUSA: Added favorite {favorite_id} for user {vault_user_id}")
        return enhanced_favorite

    except Exception as e:
        logger.error(f"Add favorite error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"TITAN PROCESSING FAILED: add favorite: {str(e)}")

@router.put(
    "/{favorite_id}",
    response_model=ExpertFavorite,
    summary="✏️ Update favorite with neural re-analysis",
    description="Update favorite settings and trigger neural re-analysis"
)
async def update_favorite(
    favorite_id: str,
    favorite_request: ExpertFavoriteRequest,
    vault_user_id: str = Depends(get_user_id),
    ctx=Depends(get_expert_context)
):
    """Update favorite with neural re-analysis"""
    try:
        # Update in database
        updated_data = await ctx.prediction_service.update_favorite({
            "favorite_id": favorite_id,
            "vault_user_id": vault_user_id,
            "label": favorite_request.label,
            "description": favorite_request.description,
            "tags": favorite_request.tags,
            "priority": favorite_request.priority.value,
            "alert_enabled": favorite_request.alert_enabled,
            "alert_conditions": favorite_request.alert_conditions
        })

        # Get refreshed neural insights
        neural_data = await ctx.prediction_service.get_favorite_insights({
            "favorite_id": favorite_id,
            "target_id": favorite_request.target_id,
            "favorite_type": favorite_request.favorite_type.value
        })

        enhanced_favorite = ExpertFavorite(
            favorite_id=favorite_id,
            vault_user_id=vault_user_id,
            favorite_type=favorite_request.favorite_type,
            target_id=favorite_request.target_id,
            label=favorite_request.label,
            description=favorite_request.description,
            tags=favorite_request.tags,
            priority=favorite_request.priority,
            watch_status=WatchStatus(updated_data.get("watch_status", "active")),
            neural_score=neural_data.get("neural_score", 0.0),
            performance_metrics=neural_data.get("performance_metrics", {}),
            ai_insights=neural_data.get("ai_insights", []),
            recommendations=neural_data.get("recommendations", []),
            alert_enabled=favorite_request.alert_enabled,
            alert_conditions=favorite_request.alert_conditions,
            view_count=updated_data.get("view_count", 0),
            last_viewed=updated_data.get("last_viewed"),
            chronicle_timestamp=updated_data.get("chronicle_timestamp", datetime.utcnow()),
            last_prophecy_update=datetime.utcnow()
        )

        logger.info(f"HYPER MEDUSA: Updated favorite {favorite_id}")
        return enhanced_favorite

    except Exception as e:
        logger.error(f"Update favorite error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"TITAN PROCESSING FAILED: update favorite: {str(e)}")

@router.delete(
    "/{favorite_id}",
    summary="🗑️ Remove favorite",
    description="Remove favorite and associated alerts"
)
async def remove_favorite(
    favorite_id: str,
    vault_user_id: str = Depends(get_user_id),
    ctx=Depends(get_expert_context)
):
    """Remove favorite and associated alerts"""
    try:
        # Remove from database and cleanup alerts
        result = await ctx.prediction_service.remove_favorite({
            "favorite_id": favorite_id,
            "vault_user_id": vault_user_id
        })

        logger.info(f"HYPER MEDUSA: Removed favorite {favorite_id}")
        return {
            "status": "success",
            "message": f"Favorite {favorite_id} removed successfully",
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.error(f"Remove favorite error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"TITAN PROCESSING FAILED: remove favorite: {str(e)}")

@router.get(
    "/analytics",
    response_model=FavoritesAnalytics,
    summary=" Get favorites analytics",
    description="Comprehensive analytics on user favorites and performance"
)
async def get_favorites_analytics(
    vault_user_id: str = Depends(get_user_id),
    ctx=Depends(get_expert_context)
):
    """Get comprehensive favorites analytics"""
    try:
        analytics_data = await ctx.prediction_service.get_favorites_analytics({
            "vault_user_id": vault_user_id
        })

        analytics = FavoritesAnalytics(
            total_favorites=analytics_data.get("total_favorites", 0),
            active_watches=analytics_data.get("active_watches", 0),
            top_categories=analytics_data.get("top_categories", []),
            performance_summary=analytics_data.get("performance_summary", {}),
            alert_stats=analytics_data.get("alert_stats", {}),
            neural_insights=analytics_data.get("neural_insights", []),
            recommendations=analytics_data.get("recommendations", [])
        )

        logger.info(f"HYPER MEDUSA: Generated favorites analytics for user {vault_user_id}")
        return analytics

    except Exception as e:
        logger.error(f"Favorites analytics error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analytics error: {str(e)}")

@router.get(
    "/recommendations",
    response_model=List[Dict[str, Any]],
    summary=" Get smart recommendations",
    description="AI-powered recommendations based on user favorites and behavior"
)
async def get_smart_recommendations(
    limit: int = Query(10, ge=1, le=50, description="Recommendations limit"),
    vault_user_id: str = Depends(get_user_id),
    ctx=Depends(get_expert_context)
):
    """Get AI-powered smart recommendations"""
    try:
        recommendations_data = await ctx.prediction_service.get_smart_recommendations({
            "vault_user_id": vault_user_id,
            "limit": limit
        })

        recommendations = []
        for rec_data in recommendations_data:
            recommendation = {
                "recommendation_id": rec_data.get("recommendation_id", ""),
                "type": rec_data.get("type", ""),
                "target_id": rec_data.get("target_id", ""),
                "title": rec_data.get("title", ""),
                "description": rec_data.get("description", ""),
                "confidence": rec_data.get("confidence", 0.0),
                "reasoning": rec_data.get("reasoning", []),
                "potential_value": rec_data.get("potential_value", 0.0),
                "risk_level": rec_data.get("risk_level", "medium"),
                "neural_insights": rec_data.get("neural_insights", []),
                "action_url": rec_data.get("action_url", ""),
                "expires_at": rec_data.get("expires_at")
            }
            recommendations.append(recommendation)

        logger.info(f"HYPER MEDUSA: Generated {len(recommendations)} smart recommendations")
        return recommendations

    except Exception as e:
        logger.error(f"Smart recommendations error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Recommendations error: {str(e)}")

@router.get(
    "/health",
    summary="🏥 Favorites service health check",
    description="Health status of HYPER MEDUSA favorites neural vault"
)
async def health_check(ctx=Depends(get_expert_context)):
    """Health check for favorites service"""
    try:
        health_status = await ctx.prediction_service.health_check()

        return {
            "status": "HYPER MEDUSA NEURAL VAULT ONLINE",
            "service": "Favorites Intelligence Expert Router",
            "version": "1.0.0",
            "timestamp": datetime.utcnow().isoformat(),
            "prediction_service": health_status.get("status", "unknown"),
            "neural_networks": "operational",
            "alert_system": "active",
            "recommendations": "generating"
        }

    except Exception as e:
        logger.error(f"Health check error: {str(e)}")
        return {
            "status": "DEGRADED",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }

async def setup_favorite_alerts(favorite_id: str, alert_conditions: Dict[str, Any]):
    """Background task to setup favorite alerts"""
    try:
        logger.info(f"HYPER MEDUSA: Setting up alerts for favorite {favorite_id}")
        # Implementation would setup alert monitoring
    except Exception as e:
        logger.error(f"Alert setup error: {str(e)}")
