import asyncio
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from fastapi import FastAPI
from contextlib import asynccontextmanager
import os
import sys
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator, UnifiedMessage, MessageType, MessagePriority, DeliveryChannel, MessageRecipient

#!/usr/bin/env python3
"""
Expert Messaging Service Integration
===================================
Integration layer for the Expert Messaging Orchestrator with the main NBA application.
Provides basketball-aware messaging capabilities for the entire platform.
"""


# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", "..", "..")) # Adjust path to actual project root
if project_root not in sys.path:
 sys.path.insert(0, project_root)

# Configure logger for the module
logger = logging.getLogger(__name__)
if not logger.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="𓄿 %(asctime)s 𓃬 %(levelname)s 𓄢 %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)],
    )
    logger = logging.getLogger(__name__) # Re-get logger after basicConfig
    logger.info(" MEDUSA VAULT: ExpertMessagingService logger initialized.")

# --- Production Imports ---
# Expert messaging orchestrator and related classes imported successfully
logger.info("🏀 MEDUSA VAULT: ExpertMessagingOrchestrator and related enums imported.")


class BasketballMessagingService:
    """Basketball-aware messaging service for the NBA application"""
    
    def __init__(self):
        self.orchestrator: Optional[ExpertMessagingOrchestrator] = None
        self._initialized = False
        self._subscribers: Dict[str, List[MessageRecipient]] = {}
        
    async def initialize(self) -> None:
        """Initialize the messaging service"""
        if self._initialized:
            return
        
        try:
            self.orchestrator = ExpertMessagingOrchestrator()
            await self.orchestrator.initialize_services()
            self._initialized = True
            logger.info(" MEDUSA VAULT: Basketball Messaging Service initialized successfully")
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize Basketball Messaging Service: {e}", exc_info=True)
            raise
        
    async def shutdown(self) -> None:
        """Shutdown the messaging service"""
        if self.orchestrator:
            # Add any cleanup logic here
            logger.info(" MEDUSA VAULT: Basketball Messaging Service shutdown")
        
    # Game Event Messaging
    async def notify_game_start(
        self,
        titan_clash_id: str,
        home_team: str,
        away_team: str,
        venue: str = ""
    ) -> bool:
        """Notify when a game starts"""
        if not self._initialized:
            await self.initialize()
        
        try:
            title = f" Game Starting: {away_team} @ {home_team}"
            body = f"The game between {away_team} and {home_team} is about to begin"
            if venue:
                body += f" at {venue}"
            
            results = await self.orchestrator.send_basketball_alert(
                alert_type="game_start",
                title=title,
                body=body,
                titan_clash_id=titan_clash_id,
                priority=MessagePriority.HIGH,
                recipients=self._get_game_subscribers(titan_clash_id)
            )
            
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send game start notification: {e}", exc_info=True)
            return False
        
    async def notify_game_end(
        self,
        titan_clash_id: str,
        home_team: str,
        away_team: str,
        final_score: str
    ) -> bool:
        """Notify when a game ends"""
        if not self._initialized:
            await self.initialize()
        
        try:
            title = f"🏁 Game Final: {away_team} @ {home_team}"
            body = f"Final Score: {final_score}"
            
            results = await self.orchestrator.send_basketball_alert(
                alert_type="game_end",
                title=title,
                body=body,
                titan_clash_id=titan_clash_id,
                priority=MessagePriority.HIGH,
                recipients=self._get_game_subscribers(titan_clash_id)
            )
            
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send game end notification: {e}", exc_info=True)
            return False
        
    # Player Event Messaging
    async def notify_player_injury(
        self,
        hero_id: str,
        player_name: str,
        injury_details: str,
        severity: str = "unknown"
    ) -> bool:
        """Notify about player injury"""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Determine priority based on severity
            priority_map = {
                "minor": MessagePriority.NORMAL,
                "moderate": MessagePriority.HIGH,
                "major": MessagePriority.CRITICAL,
                "season-ending": MessagePriority.EMERGENCY
            }
            priority = priority_map.get(severity.lower(), MessagePriority.HIGH)
            
            title = f"🏥 Player Injury Update: {player_name}"
            body = f"{player_name} - {injury_details}"
            
            results = await self.orchestrator.send_basketball_alert(
                alert_type="injury",
                title=title,
                body=body,
                hero_id=hero_id,
                priority=priority,
                recipients=self._get_player_subscribers(hero_id)
            )
            
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send injury notification: {e}", exc_info=True)
            return False
        
    async def notify_trade_alert(
        self,
        player_ids: List[str],
        teams_involved: List[str],
        trade_details: str
    ) -> bool:
        """Notify about player trades"""
        if not self._initialized:
            await self.initialize()
        
        try:
            title = f" Trade Alert: {' & '.join(teams_involved)}"
            body = trade_details
            
            # Send notification for each player involved
            all_successful = True
            for hero_id in player_ids:
                results = await self.orchestrator.send_basketball_alert(
                    alert_type="trade",
                    title=title,
                    body=body,
                    hero_id=hero_id,
                    priority=MessagePriority.HIGH,
                    recipients=self._get_player_subscribers(hero_id)
                )
                
                if not any(result.success for result in results):
                    all_successful = False
            
            return all_successful
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send trade notification: {e}", exc_info=True)
            return False
        
    # Prediction and Analytics Messaging
    async def notify_prediction_update(
        self,
        titan_clash_id: str,
        prediction_type: str,
        confidence: float,
        prediction_data: Dict[str, Any],
        model_name: str = "quantum_oracle"
    ) -> bool:
        """Send prediction update notifications with basketball intelligence"""
        if not self._initialized:
            await self.initialize()

        try:
            # Basketball intelligence: enhance prediction data with type context
            enhanced_prediction_data = {
                **prediction_data,
                "prediction_type": prediction_type,
                "basketball_context": {
                    "prediction_category": prediction_type,
                    "confidence_tier": "high" if confidence > 0.8 else "medium" if confidence > 0.6 else "low",
                    "model_expertise": model_name,
                    "titan_clash_context": titan_clash_id
                }
            }

            results = await self.orchestrator.send_prediction_update(
                prediction_data=enhanced_prediction_data,
                confidence=confidence,
                titan_clash_id=titan_clash_id,
                model_name=model_name
            )
            
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send prediction update: {e}", exc_info=True)
            return False
        
    async def notify_analytics_insight(
        self,
        insight_type: str,
        title: str,
        description: str,
        data: Dict[str, Any], # Not used in orchestrator call, but kept for full signature
        titan_clash_id: Optional[str] = None,
        hero_id: Optional[str] = None
    ) -> bool:
        """Send basketball analytics insights"""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Basketball intelligence: enhance title and description with insight type and data
            enhanced_title = f"🏀 {title} ({insight_type})"
            enhanced_description = f"{description}\n\n📊 Basketball Intelligence Insight Type: {insight_type}"

            # Add key metrics from data if available
            if data:
                key_metrics = []
                for key, value in data.items():
                    if isinstance(value, (int, float)):
                        key_metrics.append(f"{key}: {value}")
                if key_metrics:
                    enhanced_description += f"\n🔢 Key Metrics: {', '.join(key_metrics[:3])}"

            results = await self.orchestrator.send_basketball_alert(
                alert_type="analytics",
                title=enhanced_title,
                body=enhanced_description,
                titan_clash_id=titan_clash_id,
                hero_id=hero_id,
                priority=MessagePriority.NORMAL,
                recipients=self._get_analytics_subscribers()
            )
            
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send analytics insight: {e}", exc_info=True)
            return False
        
    # Emergency and System Messaging
    async def send_emergency_alert(
        self,
        title: str,
        message: str,
        titan_clash_id: Optional[str] = None
    ) -> bool:
        """Send emergency broadcast"""
        if not self._initialized:
            await self.initialize()
        
        try:
            results = await self.orchestrator.send_emergency_broadcast(
                title=title,
                body=message,
                titan_clash_id=titan_clash_id,
                force_all_channels=True
            )
            
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send emergency alert: {e}", exc_info=True)
            return False
        
    async def send_system_notification(
        self,
        title: str,
        message: str,
        recipients: Optional[List[MessageRecipient]] = None
    ) -> bool:
        """Send system notification"""
        if not self._initialized:
            await self.initialize()
        
        try:
            unified_message = UnifiedMessage(
                message_type=MessageType.SYSTEM_NOTIFICATION,
                priority=MessagePriority.NORMAL,
                title=title,
                body=message,
                recipients=recipients or [],
                channels=[DeliveryChannel.FCM_PUSH, DeliveryChannel.MNEMOSYME_LOG]
            )
            
            results = await self.orchestrator.send_unified_message(unified_message)
            return any(result.success for result in results)
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: send system notification: {e}", exc_info=True)
            return False
        
    # Subscription Management
    def subscribe_to_game(self, titan_clash_id: str, recipient: MessageRecipient) -> None:
        """Subscribe a recipient to game notifications"""
        if titan_clash_id not in self._subscribers:
            self._subscribers[titan_clash_id] = []
        self._subscribers[titan_clash_id].append(recipient)
        logger.info(f"📢 Subscribed {recipient.vault_user_id} to game {titan_clash_id}")
        
    def subscribe_to_player(self, hero_id: str, recipient: MessageRecipient) -> None:
        """Subscribe a recipient to player notifications"""
        key = f"player_{hero_id}"
        if key not in self._subscribers:
            self._subscribers[key] = []
        self._subscribers[key].append(recipient)
        logger.info(f"📢 Subscribed {recipient.vault_user_id} to player {hero_id}")
        
    def subscribe_to_analytics(self, recipient: MessageRecipient) -> None:
        """Subscribe a recipient to analytics notifications"""
        key = "analytics"
        if key not in self._subscribers:
            self._subscribers[key] = []
        self._subscribers[key].append(recipient)
        logger.info(f"📢 Subscribed {recipient.vault_user_id} to analytics")
        
    def _get_game_subscribers(self, titan_clash_id: str) -> List[MessageRecipient]:
        """Get subscribers for a specific game"""
        return self._subscribers.get(titan_clash_id, [])
        
    def _get_player_subscribers(self, hero_id: str) -> List[MessageRecipient]:
        """Get subscribers for a specific player"""
        return self._subscribers.get(f"player_{hero_id}", [])
        
    def _get_analytics_subscribers(self) -> List[MessageRecipient]:
        """Get analytics subscribers"""
        return self._subscribers.get("analytics", [])
        
    # Health and Status
    async def get_service_status(self) -> Dict[str, Any]:
        """Get comprehensive service status"""
        if not self._initialized:
            return {"status": "not_initialized"}
        
        try:
            health_check_result = await self.orchestrator.health_check()
            analytics_result = await self.orchestrator.get_delivery_analytics()
            
            return {
                "status": "healthy" if self._initialized else "unhealthy",
                "initialized": self._initialized,
                "health_check": health_check_result,
                "analytics": analytics_result,
                "subscribers": {
                    "total_subscriptions": sum(len(subs) for subs in self._subscribers.values()),
                    "subscription_breakdown": {
                        key: len(subs) for key, subs in self._subscribers.items()
                    }
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        
        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: get service status: {e}", exc_info=True)
            return {"status": "error", "error": str(e)}


# Global service instance
basketball_messaging_service = BasketballMessagingService()

# FastAPI integration
@asynccontextmanager
async def messaging_lifespan(app: FastAPI):
    """FastAPI lifespan manager for messaging service"""
    try:
        await basketball_messaging_service.initialize()
        logger.info(f"🏀 MEDUSA VAULT: Basketball Messaging Service started for {app.title}")
        yield
    finally:
        await basketball_messaging_service.shutdown()
        logger.info("🏀 MEDUSA VAULT: Basketball Messaging Service stopped")

# Convenience functions for easy integration
async def notify_game_event(event_type: str, **kwargs) -> bool:
    """Convenience function to notify game events"""
    if event_type == "start":
        return await basketball_messaging_service.notify_game_start(**kwargs)
    elif event_type == "end":
        return await basketball_messaging_service.notify_game_end(**kwargs)
    else:
        logger.warning(f"Unknown game event type: {event_type}")
        return False

async def notify_player_event(event_type: str, **kwargs) -> bool:
    """Convenience function to notify player events"""
    if event_type == "injury":
        return await basketball_messaging_service.notify_player_injury(**kwargs)
    elif event_type == "trade":
        return await basketball_messaging_service.notify_trade_alert(**kwargs)
    else:
        logger.warning(f"Unknown player event type: {event_type}")
        return False

# Export main components
__all__ = [
    'BasketballMessagingService',
    'basketball_messaging_service',
    'messaging_lifespan',
    'notify_game_event',
    'notify_player_event'
]

# Example Usage (for testing this module directly)
if __name__ == "__main__":
    async def main_demo():
        logger.info("--- Basketball Messaging Service Demonstration ---")

        # Initialize the service (done by FastAPI lifespan normally, but manually for demo)
        await basketball_messaging_service.initialize()
        
        # Create basketball intelligence recipients for demonstration
        game_recipient = MessageRecipient(vault_user_id="basketball_game_analyst", email="<EMAIL>")
        player_recipient = MessageRecipient(vault_user_id="basketball_player_scout", phone="555-NBA-SCOUT")
        analytics_recipient = MessageRecipient(vault_user_id="basketball_analytics_expert")

        # Subscribe them to basketball intelligence feeds
        basketball_messaging_service.subscribe_to_game("game_123", game_recipient)
        basketball_messaging_service.subscribe_to_player("hero_lebron", player_recipient)
        basketball_messaging_service.subscribe_to_analytics(analytics_recipient)

        # 1. Notify Game Start
        logger.info("\n--- Notifying Game Start ---")
        game_start_success = await basketball_messaging_service.notify_game_start(
            titan_clash_id="game_123",
            home_team="LAL",
            away_team="BOS",
            venue="Crypto.com Arena"
        )
        logger.info(f"Game start notification success: {game_start_success}")

        # 2. Notify Player Injury
        logger.info("\n--- Notifying Player Injury ---")
        injury_success = await basketball_messaging_service.notify_player_injury(
            hero_id="hero_lebron",
            player_name="LeBron James",
            injury_details="Ankle sprain, expected to miss 2 weeks.",
            severity="moderate"
        )
        logger.info(f"Player injury notification success: {injury_success}")

        # 3. Notify Prediction Update
        logger.info("\n--- Notifying Prediction Update ---")
        prediction_update_success = await basketball_messaging_service.notify_prediction_update(
            titan_clash_id="game_123",
            prediction_type="game_outcome",
            confidence=0.85,
            prediction_data={"winner": "LAL", "score": "115-108"},
            model_name="QuantumOracle_v3"
        )
        logger.info(f"Prediction update notification success: {prediction_update_success}")

        # 4. Notify Analytics Insight
        logger.info("\n--- Notifying Analytics Insight ---")
        analytics_insight_success = await basketball_messaging_service.notify_analytics_insight(
            insight_type="team_chemistry",
            title="Team Chemistry Anomaly Detected",
            description="LAL's chemistry score dropped by 15% in last 3 games.",
            data={"team_id": "LAL", "score_change": -0.15}
        )
        logger.info(f"Analytics insight notification success: {analytics_insight_success}")

        # 5. Send Emergency Alert
        logger.info("\n--- Sending Emergency Alert ---")
        emergency_alert_success = await basketball_messaging_service.send_emergency_alert(
            title="SYSTEM CRITICAL FAILURE",
            message="Primary data pipeline offline. Immediate action required!",
            titan_clash_id="system_wide_issue"
        )
        logger.info(f"Emergency alert success: {emergency_alert_success}")

        # 6. Get Service Status
        logger.info("\n--- Getting Service Status ---")
        status = await basketball_messaging_service.get_service_status()
        logger.info(f"Service Status: {status}")

        # Shutdown the service (done by FastAPI lifespan normally, but manually for demo)
        await basketball_messaging_service.shutdown()
        logger.info("--- Basketball Messaging Service Demonstration Complete ---")

    # Run the main asynchronous demo function
    asyncio.run(main_demo())
