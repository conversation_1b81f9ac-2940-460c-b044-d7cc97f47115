import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from sklearn.ensemble import VotingClassifier, VotingRegressor
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestRegressor
import warnings
from src.features.feature_feedback import FeatureFeedback
# Lazy import to avoid circular dependency
# from src.features.feature_alchemist import SelfLearningFeatureAlchemist

"""
OlympianCouncil_Expert.py
=========================

Expert-level ensemble coordination and decision-making system.
Acts as the supreme council coordinating all cognitive spires for 
optimal NBA prediction and betting strategy decisions.

Author: Cognitive Spires Expert System
"""


warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class SpireContribution:
    """Individual spire contribution to council decision"""
    spire_name: str
    prediction: Dict[str, Any]
    confidence: float
    weight: float
    processing_time: float
    status: str # 'success', 'error', 'timeout'


@dataclass
class CouncilDecision:
    """Final council decision with consensus and dissent analysis"""
    consensus_prediction: Dict[str, Any]
    confidence_score: float
    unanimity_ratio: float
    dissent_factors: List[str]
    contributing_spires: List[str]
    execution_strategy: str
    risk_assessment: Dict[str, float]


@dataclass
class EnsembleWeights:
    """Dynamic weights for each spire based on performance"""
    chronos: float = 0.15
    nike: float = 0.20
    athena: float = 0.18
    metis: float = 0.17
    ares: float = 0.12
    fate_forge: float = 0.08
    fate_weaver: float = 0.06
    hephaestus: float = 0.04


class OlympianCouncil_Expert:
    """
    Expert-level ensemble orchestration and decision-making system.
    
    Features:
    - Advanced spire coordination and weighting
    - Real-time consensus building algorithms
    - Dynamic ensemble optimization
    - Risk-adjusted decision synthesis
    - Performance-based weight adaptation
    - Dissent analysis and edge case detection
    """
    
    def __init__(self, enable_ensemble_optimization: bool = True):
        self.enable_ensemble_optimization = enable_ensemble_optimization
        self.ensemble_weights = EnsembleWeights()
        self.performance_history = {}
        self.spire_reliability = {}
        self.consensus_threshold = 0.75
        self.max_processing_time = 30.0 # seconds
        
        # Initialize ensemble models
        self._initialize_ensemble_models()
        logger.info(" MEDUSA VAULT: OlympianCouncil_Expert initialized with advanced ensemble coordination")
    
    def _initialize_ensemble_models(self):
        """Initialize ensemble coordination models"""
        try:
            # Consensus building model
            self.consensus_model = LogisticRegression(random_state=42)
            
            # Weight optimization model
            self.weight_optimizer = RandomForestRegressor(n_estimators=100, random_state=42)
            
            # Risk assessment model (NOTE: This model needs to be fitted with data during
            # the council's operation or explicitly trained for meaningful use.
            # For this syntax fix, we ensure it's instantiated correctly.)
            self.risk_assessor = VotingRegressor([
                ('rf', RandomForestRegressor(n_estimators=50, random_state=42)),
                ('lr', LogisticRegression(random_state=42))
            ])
            
            # Initialize spire reliability tracking
            spire_names = ['chronos', 'nike', 'athena', 'metis', 'ares', 
                           'fate_forge', 'fate_weaver', 'hephaestus']
            for spire in spire_names:
                self.spire_reliability[spire] = {
                    'success_rate': 0.85,
                    'avg_confidence': 0.75,
                    'avg_processing_time': 2.0,
                    'accuracy_trend': 0.02 # positive trend
                }
            
        
        except Exception as e:
            logger.error(f" Ensemble model initialization failed: {e}")
    
    def predict(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main prediction interface for council decision-making.
        
        Args:
            input_data: Dictionary containing game context and analysis requirements
            
        Returns:
            Dictionary with council decision and ensemble analysis
        """
        # Lazy import to avoid circular dependency
        try:
            from src.features.feature_alchemist import SelfLearningFeatureAlchemist
            feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)
        except ImportError:
            feature_alchemist = None
        try:
            start_time = datetime.now()
            
            # Extract input parameters
            game_context = input_data.get('game_context', {})
            spire_predictions = input_data.get('spire_predictions', {})
            decision_type = input_data.get('decision_type', 'game_prediction')
            
            # Coordinate spire contributions
            contributions = self._coordinate_spire_contributions(spire_predictions, game_context)
            
            # Build consensus
            council_decision = self._build_consensus(contributions, decision_type)
            
            # Optimize ensemble weights
            if self.enable_ensemble_optimization:
                self._optimize_ensemble_weights(contributions, council_decision)
            
            # Generate execution strategy
            strategy = self._generate_execution_strategy(council_decision, game_context)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Return a comprehensive dictionary to match the type hint
            result = {
                'council_decision': council_decision.__dict__,
                'spire_contributions': [c.__dict__ for c in contributions],
                'ensemble_weights': self.ensemble_weights.__dict__,
                'execution_strategy': strategy,
                'meta_analysis': self._generate_meta_analysis(contributions),
                'processing_time_seconds': processing_time,
                'decision_timestamp': datetime.now().isoformat(),
                'council_version': '2.0_expert'
            }
            confidence = council_decision.confidence_score if hasattr(council_decision, 'confidence_score') else 1.0
            # --- Feedback wiring: send feedback if confidence is low ---
            if confidence < 0.3:
                feedback = FeatureFeedback(self.__class__.__name__, input_data, confidence, message="Low confidence. Requesting feature improvement.")
                feature_alchemist.receive_feedback(feedback)
            return result
        except Exception as e:
            logger.error(f" OlympianCouncil prediction failed: {e}")
            return self._get_fallback_decision() # Return comprehensive fallback dict
    
    def _coordinate_spire_contributions(self, spire_predictions: Dict[str, Any], 
                                        game_context: Dict) -> List[SpireContribution]:
        """Coordinate and validate contributions from all spires"""
        contributions = []
        
        try:
            # Map spire predictions to standardized contributions
            spire_mapping = {
                'chronos_prediction': ('chronos', self.ensemble_weights.chronos),
                'nike_prediction': ('nike', self.ensemble_weights.nike),
                'athena_prediction': ('athena', self.ensemble_weights.athena),
                'metis_prediction': ('metis', self.ensemble_weights.metis),
                'ares_prediction': ('ares', self.ensemble_weights.ares),
                'fate_forge_prediction': ('fate_forge', self.ensemble_weights.fate_forge),
                'fate_weaver_prediction': ('fate_weaver', self.ensemble_weights.fate_weaver),
                'hephaestus_prediction': ('hephaestus', self.ensemble_weights.hephaestus)
            }
            
            for pred_key, (spire_name, weight) in spire_mapping.items():
                if pred_key in spire_predictions:
                    prediction_data = spire_predictions[pred_key]
                    
                    # Extract contribution details
                    contribution = SpireContribution(
                        spire_name=spire_name,
                        prediction=prediction_data,
                        confidence=self._extract_confidence(prediction_data),
                        weight=weight,
                        processing_time=prediction_data.get('processing_time_seconds', 2.0),
                        status='success'
                    )
                    contributions.append(contribution)
                
                else:
                    # Missing spire - create placeholder
                    contribution = SpireContribution(
                        spire_name=spire_name,
                        prediction=self._get_fallback_spire_prediction(spire_name),
                        confidence=0.5,
                        weight=weight * 0.5, # Reduced weight for fallback
                        processing_time=0.1,
                        status='fallback'
                    )
                    contributions.append(contribution)
            
            # Validate and adjust weights
            self._validate_contributions(contributions)
            
            return contributions
        
        except Exception as e:
            logger.error(f" Spire coordination failed: {e}")
            return self._get_fallback_contributions()
    
    def _extract_confidence(self, prediction_data: Dict) -> float:
        """Extract confidence score from spire prediction"""
        # Try multiple possible confidence keys
        confidence_keys = ['confidence_score', 'confidence', 'certainty', 'probability']
        
        for key in confidence_keys:
            if key in prediction_data:
                confidence = prediction_data[key]
                if isinstance(confidence, (int, float)):
                    return max(0.0, min(1.0, float(confidence)))
        
        # Fallback: estimate confidence from prediction structure
        if 'predictions' in prediction_data:
            return 0.75
        elif 'analysis' in prediction_data:
            return 0.65
        else:
            return 0.5
    
    def _build_consensus(self, contributions: List[SpireContribution], 
                         decision_type: str) -> CouncilDecision:
        """Build consensus from spire contributions"""
        try:
            # Filter valid contributions
            valid_contributions = [c for c in contributions if c.status in ['success', 'fallback']]
            
            if not valid_contributions:
                return self._get_emergency_decision()
            
            # Calculate weighted predictions
            consensus_prediction = self._calculate_weighted_consensus(valid_contributions)
            
            # Analyze consensus strength
            unanimity_ratio = self._calculate_unanimity(valid_contributions)
            confidence_score = self._calculate_consensus_confidence(valid_contributions)
            
            # Identify dissent factors
            dissent_factors = self._analyze_dissent(valid_contributions)
            
            # Assess risks
            risk_assessment = self._assess_consensus_risks(valid_contributions, consensus_prediction)
            
            # Determine execution strategy
            if confidence_score > 0.8 and unanimity_ratio > 0.75:
                execution_strategy = 'high_confidence_execute'
            elif confidence_score > 0.65 and unanimity_ratio > 0.6:
                execution_strategy = 'moderate_confidence_execute'
            elif confidence_score > 0.5:
                execution_strategy = 'cautious_execute'
            else:
                execution_strategy = 'hold_analyze_further'
            
            return CouncilDecision(
                consensus_prediction=consensus_prediction,
                confidence_score=confidence_score,
                unanimity_ratio=unanimity_ratio,
                dissent_factors=dissent_factors,
                contributing_spires=[c.spire_name for c in valid_contributions],
                execution_strategy=execution_strategy,
                risk_assessment=risk_assessment
            )
        
        except Exception as e:
            logger.error(f" Consensus building failed: {e}")
            return self._get_emergency_decision()
    
    def _calculate_weighted_consensus(self, contributions: List[SpireContribution]) -> Dict[str, Any]:
        """Calculate weighted consensus prediction"""
        consensus = {
            'win_probability': 0.0,
            'spread_prediction': 0.0,
            'total_prediction': 0.0,
            'confidence_weighted_avg': 0.0,
            'key_factors': [],
            'prediction_range': {'min': 0.0, 'max': 1.0, 'std': 0.0} # Added std default
        }
        
        total_weight = 0.0
        all_factors = []
        predictions = []
        
        for contrib in contributions:
            # Adjust weight by confidence and reliability
            adjusted_weight = contrib.weight * contrib.confidence * self._get_spire_reliability(contrib.spire_name)
            total_weight += adjusted_weight
            
            # Extract numeric predictions
            pred = contrib.prediction
            win_prob = self._extract_numeric_value(pred, ['win_probability', 'probability', 'likelihood'], 0.5)
            spread = self._extract_numeric_value(pred, ['spread_prediction', 'spread', 'point_spread'], 0.0)
            total = self._extract_numeric_value(pred, ['total_prediction', 'total', 'over_under'], 220.0)
            
            # Weight and accumulate
            consensus['win_probability'] += win_prob * adjusted_weight
            consensus['spread_prediction'] += spread * adjusted_weight
            consensus['total_prediction'] += total * adjusted_weight
            
            predictions.append(win_prob)
            
            # Collect factors
            factors = pred.get('key_factors', pred.get('factors', []))
            if isinstance(factors, list):
                all_factors.extend(factors)
            
        # Normalize by total weight
        if total_weight > 0:
            consensus['win_probability'] /= total_weight
            consensus['spread_prediction'] /= total_weight
            consensus['total_prediction'] /= total_weight
        
        # Calculate prediction range
        if predictions:
            consensus['prediction_range'] = {
                'min': min(predictions),
                'max': max(predictions),
                'std': np.std(predictions) if len(predictions) > 1 else 0.0
            }
        
        # Select top factors
        factor_counts = {}
        for factor in all_factors:
            if isinstance(factor, str):
                factor_counts[factor] = factor_counts.get(factor, 0) + 1
        
        consensus['key_factors'] = sorted(factor_counts.keys(), 
                                          key=lambda f: factor_counts[f], 
                                          reverse=True)[:5]
        
        return consensus
    
    def _calculate_unanimity(self, contributions: List[SpireContribution]) -> float:
        """Calculate unanimity ratio among spire predictions"""
        if len(contributions) < 2:
            return 1.0
        
        # Extract win probabilities
        win_probs = []
        for contrib in contributions:
            win_prob = self._extract_numeric_value(
                contrib.prediction, 
                ['win_probability', 'probability', 'likelihood'], 
                0.5
            )
            win_probs.append(win_prob)
        
        # Calculate agreement (inverse of variance)
        if len(win_probs) > 1:
            variance = np.var(win_probs)
            # Convert variance to unanimity (0 variance = 1.0 unanimity)
            unanimity = 1.0 / (1.0 + variance * 10) # Scale factor
            return min(1.0, max(0.0, unanimity))
        
        return 1.0
    
    def _calculate_consensus_confidence(self, contributions: List[SpireContribution]) -> float:
        """Calculate overall confidence in consensus"""
        if not contributions:
            return 0.0
        
        # Weight confidences by spire weights and reliability
        weighted_confidence = 0.0
        total_weight = 0.0
        
        for contrib in contributions:
            reliability = self._get_spire_reliability(contrib.spire_name)
            weight = contrib.weight * reliability
            weighted_confidence += contrib.confidence * weight
            total_weight += weight
        
        return weighted_confidence / total_weight if total_weight > 0 else 0.5
    
    def _analyze_dissent(self, contributions: List[SpireContribution]) -> List[str]:
        """Analyze sources of dissent among spire predictions"""
        dissent_factors = []
        
        try:
            # Find outlier predictions
            win_probs = []
            for contrib in contributions:
                win_prob = self._extract_numeric_value(
                    contrib.prediction, 
                    ['win_probability', 'probability', 'likelihood'], 
                    0.5
                )
                win_probs.append((contrib.spire_name, win_prob))
            
            if len(win_probs) > 2:
                mean_prob = np.mean([p[1] for p in win_probs])
                std_prob = np.std([p[1] for p in win_probs])
                
                # Identify outliers (> 1.5 std from mean)
                for spire_name, prob in win_probs:
                    if std_prob > 0 and abs(prob - mean_prob) > 1.5 * std_prob: # Avoid division by zero
                        dissent_factors.append(f"{spire_name} outlier prediction ({prob:.3f} vs {mean_prob:.3f})")
            
            # Check for low confidence spires
            low_confidence_spires = [c.spire_name for c in contributions if c.confidence < 0.4]
            if low_confidence_spires:
                dissent_factors.append(f"Low confidence from: {', '.join(low_confidence_spires)}")
            
            # Check for processing issues
            slow_spires = [c.spire_name for c in contributions if c.processing_time > 10.0]
            if slow_spires:
                dissent_factors.append(f"Slow processing from: {', '.join(slow_spires)}")
            
        except Exception as e:
            logger.error(f" Dissent analysis failed: {e}")
            dissent_factors.append("Analysis error occurred")
        
        return dissent_factors
    
    def _assess_consensus_risks(self, contributions: List[SpireContribution], 
                                consensus: Dict[str, Any]) -> Dict[str, float]:
        """Assess risks in the consensus decision"""
        risks = {
            'model_uncertainty': 0.0,
            'data_quality': 0.0,
            'prediction_variance': 0.0,
            'spire_reliability': 0.0,
            'processing_issues': 0.0
        }
        
        try:
            # Model uncertainty - based on prediction range
            pred_range = consensus.get('prediction_range', {})
            if 'std' in pred_range:
                risks['model_uncertainty'] = min(1.0, pred_range['std'] * 2)
            
            # Data quality - based on fallback usage
            fallback_count = len([c for c in contributions if c.status == 'fallback'])
            risks['data_quality'] = fallback_count / len(contributions) if contributions else 0.0
            
            # Prediction variance - same as model uncertainty for now
            risks['prediction_variance'] = risks['model_uncertainty']
            
            # Spire reliability - average reliability
            total_reliability = sum(self._get_spire_reliability(c.spire_name) for c in contributions)
            avg_reliability = total_reliability / len(contributions) if contributions else 0.5
            risks['spire_reliability'] = 1.0 - avg_reliability
            
            # Processing issues - based on timeouts/errors
            error_count = len([c for c in contributions if c.status == 'error'])
            risks['processing_issues'] = error_count / len(contributions) if contributions else 0.0
            
        except Exception as e:
            logger.error(f" Risk assessment failed: {e}")
            # Default to moderate risk
            for key in risks:
                risks[key] = 0.5
            
        return risks
    
    def _optimize_ensemble_weights(self, contributions: List[SpireContribution], 
                                   decision: CouncilDecision):
        """Optimize ensemble weights based on performance"""
        if not self.enable_ensemble_optimization:
            return
        
        try:
            # Update spire reliability based on contributions
            for contrib in contributions:
                reliability = self.spire_reliability.get(contrib.spire_name, {})
                
                # Update success rate
                if contrib.status == 'success':
                    reliability['success_rate'] = min(1.0, reliability.get('success_rate', 0.8) + 0.01)
                else:
                    reliability['success_rate'] = max(0.1, reliability.get('success_rate', 0.8) - 0.02)
                
                # Update average confidence
                reliability['avg_confidence'] = (
                    reliability.get('avg_confidence', 0.75) * 0.9 + contrib.confidence * 0.1
                )
                
                # Update processing time
                reliability['avg_processing_time'] = (
                    reliability.get('avg_processing_time', 2.0) * 0.9 + contrib.processing_time * 0.1
                )
                
                self.spire_reliability[contrib.spire_name] = reliability
            
            # Adjust ensemble weights based on reliability
            self._adjust_ensemble_weights()
            
            
        except Exception as e:
            logger.error(f" Weight optimization failed: {e}")
    
    def _adjust_ensemble_weights(self):
        """Adjust ensemble weights based on spire reliability"""
        try:
            # Calculate new weights based on reliability scores
            reliability_scores = {}
            for spire_name, reliability in self.spire_reliability.items():
                score = (
                    reliability.get('success_rate', 0.8) * 0.4 +
                    reliability.get('avg_confidence', 0.75) * 0.3 +
                    (1.0 - min(reliability.get('avg_processing_time', 2.0) / 10.0, 1.0)) * 0.2 +
                    (1.0 + reliability.get('accuracy_trend', 0.0)) * 0.1
                )
                reliability_scores[spire_name] = score
            
            # Normalize scores to weights
            total_score = sum(reliability_scores.values())
            if total_score > 0:
                weight_mapping = {
                    'chronos': 'chronos',
                    'nike': 'nike', 
                    'athena': 'athena',
                    'metis': 'metis',
                    'ares': 'ares',
                    'fate_forge': 'fate_forge',
                    'fate_weaver': 'fate_weaver',
                    'hephaestus': 'hephaestus'
                }
                
                for spire_name, weight_attr in weight_mapping.items():
                    if spire_name in reliability_scores:
                        new_weight = reliability_scores[spire_name] / total_score
                        setattr(self.ensemble_weights, weight_attr, new_weight)
            
        except Exception as e:
            logger.error(f" Weight adjustment failed: {e}")
    
    def _generate_execution_strategy(self, decision: CouncilDecision, 
                                     game_context: Dict) -> Dict[str, Any]:
        """Generate execution strategy for council decision"""
        strategy = {
            'action': decision.execution_strategy,
            'confidence_threshold': 0.75,
            'risk_tolerance': 'medium',
            'position_sizing': 'standard',
            'hedge_recommendations': [],
            'monitoring_requirements': []
        }
        
        try:
            # Adjust strategy based on confidence and risk
            if decision.confidence_score > 0.85:
                strategy['position_sizing'] = 'aggressive'
                strategy['risk_tolerance'] = 'high'
            elif decision.confidence_score < 0.55:
                strategy['position_sizing'] = 'conservative'
                strategy['risk_tolerance'] = 'low'
                strategy['hedge_recommendations'].append('Consider opposite position hedge')
            
            # Add monitoring based on dissent factors
            if decision.dissent_factors:
                strategy['monitoring_requirements'].extend([
                    'Monitor for consensus shifts',
                    'Track outlier spire performance'
                ])
            
            # Game context adjustments
            game_importance = game_context.get('importance_score', 0.5)
            if game_importance > 0.8:
                strategy['monitoring_requirements'].append('High-stakes game monitoring')
            
        except Exception as e:
            logger.error(f" Strategy generation failed: {e}")
        
        return strategy
    
    def _generate_meta_analysis(self, contributions: List[SpireContribution]) -> Dict[str, Any]:
        """Generate meta-analysis of ensemble performance"""
        return {
            'total_spires_active': len([c for c in contributions if c.status == 'success']),
            'average_confidence': np.mean([c.confidence for c in contributions]) if contributions else 0.0,
            'total_processing_time': sum(c.processing_time for c in contributions),
            'reliability_distribution': {
                c.spire_name: self._get_spire_reliability(c.spire_name) 
                for c in contributions
            },
            'weight_distribution': {
                c.spire_name: c.weight for c in contributions
            },
            'status_breakdown': {
                status: len([c for c in contributions if c.status == status])
                for status in ['success', 'fallback', 'error']
            }
        }
    
    def _get_spire_reliability(self, spire_name: str) -> float:
        """Get reliability score for a spire"""
        reliability = self.spire_reliability.get(spire_name, {})
        return reliability.get('success_rate', 0.8)
    
    def _extract_numeric_value(self, data: Dict, keys: List[str], default: float) -> float:
        """Extract numeric value from prediction data"""
        for key in keys:
            if key in data:
                value = data[key]
                if isinstance(value, (int, float)):
                    return float(value)
        return default
    
    def _get_fallback_spire_prediction(self, spire_name: str) -> Dict[str, Any]:
        """Generate fallback prediction for missing spire"""
        return {
            'win_probability': 0.5,
            'confidence': 0.4,
            'status': 'fallback',
            'key_factors': [f'{spire_name}_unavailable'],
            'processing_time_seconds': 0.1
        }
    
    def _get_fallback_contributions(self) -> List[SpireContribution]:
        """Generate fallback contributions when coordination fails"""
        spire_names = ['chronos', 'nike', 'athena', 'metis', 'ares']
        contributions = []
        
        for spire_name in spire_names:
            contribution = SpireContribution(
                spire_name=spire_name,
                prediction=self._get_fallback_spire_prediction(spire_name),
                confidence=0.4,
                weight=0.2,
                processing_time=0.1,
                status='fallback'
            )
            contributions.append(contribution)
        
        return contributions
    
    def _get_emergency_decision(self) -> CouncilDecision:
        """Generate emergency decision when consensus building fails"""
        return CouncilDecision(
            consensus_prediction={
                'win_probability': 0.5,
                'spread_prediction': 0.0,
                'total_prediction': 220.0,
                'key_factors': ['emergency_fallback'],
                'prediction_range': {'min': 0.4, 'max': 0.6, 'std': 0.1}
            },
            confidence_score=0.3,
            unanimity_ratio=0.5,
            dissent_factors=['Emergency fallback activated'],
            contributing_spires=['emergency_fallback'],
            execution_strategy='hold_analyze_further',
            risk_assessment={
                'model_uncertainty': 0.8,
                'data_quality': 0.8,
                'prediction_variance': 0.8,
                'spire_reliability': 0.8,
                'processing_issues': 0.8
            }
        )
    
    def _get_fallback_decision(self) -> Dict[str, Any]:
        """Fallback decision when main prediction fails - consistent with predict return type"""
        emergency_decision = self._get_emergency_decision()
        return {
            'council_decision': emergency_decision.__dict__,
            'spire_contributions': [c.__dict__ for c in self._get_fallback_contributions()],
            'ensemble_weights': self.ensemble_weights.__dict__,
            'execution_strategy': {
                'action': 'hold_analyze_further',
                'confidence_threshold': 0.75,
                'risk_tolerance': 'low',
                'position_sizing': 'minimal'
            },
            'meta_analysis': {
                'total_spires_active': 0,
                'average_confidence': 0.3,
                'status': 'emergency_fallback'
            },
            'processing_time_seconds': 0.01,
            'decision_timestamp': datetime.now().isoformat(),
            'council_version': '2.0_expert_fallback'
        }
    
    def _validate_contributions(self, contributions: List[SpireContribution]):
        """Validate spire contributions and normalize weights if needed"""
        total_weight = sum(c.weight for c in contributions)
        if total_weight > 0:
            # Normalize weights
            for contribution in contributions:
                contribution.weight = contribution.weight / total_weight

    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """
        Self-learning and feedback-driven adaptation for OlympianCouncil_Expert.
        Adjusts ensemble weights, consensus logic, or other parameters based on feedback from the War Council, spires, or system performance.
        """
        if feedback:
            logger.info(f"[OlympianCouncil_Expert] Received feedback: {feedback}")
            # Example: Adjust ensemble weights based on feedback
            if 'ensemble_weights' in feedback:
                for k, v in feedback['ensemble_weights'].items():
                    if hasattr(self.ensemble_weights, k):
                        setattr(self.ensemble_weights, k, v)
            # Log feedback for meta-learning
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)


# Compatibility functions for legacy integration
def coordinate_spires(spire_data: Dict[str, Any]) -> Dict[str, Any]:
    """Legacy compatibility function"""
    council = OlympianCouncil_Expert()
    mock_input = {
        'spire_predictions': spire_data,
        'decision_type': 'game_prediction',
        'game_context': {'importance_score': 0.75}
    }
    # Call predict and return the structured result
    return council.predict(mock_input)


if __name__ == "__main__":
    # Test the expert council
    council = OlympianCouncil_Expert()
    
    test_data = {
        'spire_predictions': {
            'chronos_prediction': {'win_probability': 0.65, 'confidence': 0.8, 'processing_time_seconds': 1.5, 'key_factors': ['fatigue_advantage']},
            'nike_prediction': {'win_probability': 0.72, 'confidence': 0.75, 'processing_time_seconds': 2.1, 'key_factors': ['momentum_boost']},
            'athena_prediction': {'win_probability': 0.68, 'confidence': 0.85, 'processing_time_seconds': 1.8, 'key_factors': ['strategic_matchup']}
        },
        'decision_type': 'game_prediction',
        'game_context': {'importance_score': 0.85, 'rest_days': 2}
    }
    
    result = council.predict(test_data)
    
    # Access elements from the returned dictionary
    decision = result['council_decision']
