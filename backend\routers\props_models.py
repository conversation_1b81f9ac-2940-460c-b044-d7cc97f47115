from pydantic import BaseModel
from typing import Optional




class ConfidenceTrendPoint(BaseModel):
 date: str # SQL returns date as string (YYYY-MM-DD)
 avg_confidence: float
 total: int


class PredictionSnapshot(BaseModel):
 mythic_roster_id: str
 hero_id: str
 prediction: float
 confidence: float
 stat_type: Optional[str] = None
 model_name: Optional[str] = None
 # Add more fields as needed based on your model output


class AccuracyRecord(BaseModel):
 date: str # SQL returns date as string
 accuracy: float
 total: int


class ModelVersionInfo(BaseModel):
 model_name: str
 version: str
 date: str # SQL returns timestamp as string
 accuracy: float
