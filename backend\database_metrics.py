import logging
import time
import threading
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from collections import defaultdict
from datetime import datetime, timedelta

logger = logging.getLogger("database_metrics")

# Production Metrics System with Real Prometheus Integration
class ProductionMetricsRegistry:
    """Production-grade metrics registry with Prometheus integration and intelligent fallbacks"""

    def __init__(self):
        self.metrics_store = {}
        self.prometheus_available = False
        self.shared_registry_available = False
        self.lock = threading.RLock()
        self._initialize_prometheus()

    def _initialize_prometheus(self):
        """Initialize Prometheus with proper error handling and fallbacks"""
        try:
            # Try shared registry first
            from backend.shared_prometheus_registry import (
                get_or_create_counter,
                get_or_create_histogram,
                get_or_create_gauge
            )
            self.get_or_create_counter = get_or_create_counter
            self.get_or_create_histogram = get_or_create_histogram
            self.get_or_create_gauge = get_or_create_gauge
            self.shared_registry_available = True
            self.prometheus_available = True
            logger.info("✅ Shared Prometheus registry initialized successfully")

        except ImportError:
            try:
                # Fallback to standard prometheus_client
                from prometheus_client import Counter, Histogram, Gauge, CollectorRegistry
                self.Counter = Counter
                self.Histogram = Histogram
                self.Gauge = Gauge
                self.registry = CollectorRegistry()
                self.prometheus_available = True
                logger.info("✅ Standard Prometheus client initialized successfully")

            except ImportError:
                logger.warning("⚠️ Prometheus not available, using production in-memory metrics")
                self.prometheus_available = False

    def create_counter(self, name: str, description: str = "", labelnames: list = None):
        """Create a counter metric with proper fallback handling"""
        with self.lock:
            if self.shared_registry_available:
                return self.get_or_create_counter(name, description, labelnames or [])
            elif self.prometheus_available:
                return self.Counter(name, description, labelnames=labelnames or [], registry=self.registry)
            else:
                return ProductionInMemoryMetric(name, description, 'counter')

    def create_histogram(self, name: str, description: str = "", labelnames: list = None, buckets=None):
        """Create a histogram metric with proper fallback handling"""
        with self.lock:
            if self.shared_registry_available:
                return self.get_or_create_histogram(name, description, labelnames or [])
            elif self.prometheus_available:
                return self.Histogram(name, description, labelnames=labelnames or [], buckets=buckets, registry=self.registry)
            else:
                return ProductionInMemoryMetric(name, description, 'histogram')

    def create_gauge(self, name: str, description: str = "", labelnames: list = None):
        """Create a gauge metric with proper fallback handling"""
        with self.lock:
            if self.shared_registry_available:
                return self.get_or_create_gauge(name, description, labelnames or [])
            elif self.prometheus_available:
                return self.Gauge(name, description, labelnames=labelnames or [], registry=self.registry)
            else:
                return ProductionInMemoryMetric(name, description, 'gauge')


@dataclass
class MetricData:
    """Data structure for storing metric information"""
    value: float = 0.0
    timestamp: datetime = field(default_factory=datetime.now)
    labels: Dict[str, str] = field(default_factory=dict)
    observations: list = field(default_factory=list)


class ProductionInMemoryMetric:
    """Production-grade in-memory metric implementation with full feature support"""

    def __init__(self, name: str, description: str = "", metric_type: str = 'gauge'):
        self.name = name
        self.description = description
        self.metric_type = metric_type
        self.data = MetricData()
        self.labeled_metrics = {}
        self.lock = threading.RLock()

        # Track metric creation
        logger.debug(f"Created {metric_type} metric: {name}")

    def observe(self, value: float):
        """Record an observation for histogram metrics"""
        with self.lock:
            self.data.value = value
            self.data.timestamp = datetime.now()
            if self.metric_type == 'histogram':
                self.data.observations.append(value)
                # Keep only last 1000 observations for memory efficiency
                if len(self.data.observations) > 1000:
                    self.data.observations = self.data.observations[-1000:]

    def inc(self, amount: float = 1):
        """Increment counter metric"""
        with self.lock:
            self.data.value += amount
            self.data.timestamp = datetime.now()

    def set(self, value: float):
        """Set gauge metric value"""
        with self.lock:
            self.data.value = value
            self.data.timestamp = datetime.now()

    def labels(self, **kwargs):
        """Return labeled metric instance"""
        with self.lock:
            label_key = tuple(sorted(kwargs.items()))
            if label_key not in self.labeled_metrics:
                labeled_metric = ProductionInMemoryMetric(
                    f"{self.name}_{hash(label_key)}",
                    self.description,
                    self.metric_type
                )
                labeled_metric.data.labels = kwargs
                self.labeled_metrics[label_key] = labeled_metric
            return self.labeled_metrics[label_key]

    def get_value(self) -> float:
        """Get current metric value"""
        with self.lock:
            return self.data.value

    def get_statistics(self) -> Dict[str, Any]:
        """Get comprehensive metric statistics"""
        with self.lock:
            stats = {
                'name': self.name,
                'type': self.metric_type,
                'value': self.data.value,
                'timestamp': self.data.timestamp.isoformat(),
                'labels': self.data.labels
            }

            if self.metric_type == 'histogram' and self.data.observations:
                import statistics
                obs = self.data.observations
                stats.update({
                    'count': len(obs),
                    'sum': sum(obs),
                    'min': min(obs),
                    'max': max(obs),
                    'mean': statistics.mean(obs),
                    'median': statistics.median(obs)
                })

            return stats


# Global production metrics registry
_metrics_registry = ProductionMetricsRegistry()

# Global database metrics - created once and shared
_DATABASE_METRICS = None

class DatabaseMetrics:
    """Production-grade centralized database metrics with intelligent Prometheus integration"""

    def __init__(self):
        """Initialize database metrics using production registry"""
        self.registry = _metrics_registry
        self._initialize_all_metrics()
        logger.info("✅ Production database metrics initialized successfully")

    def _initialize_all_metrics(self):
        """Initialize all database metrics with proper error handling"""
        try:
            # NBA Database Query Metrics
            self.db_queries = self.registry.create_counter(
                "nba_db_queries_total",
                "Total database queries",
                ["operation", "table", "status"]
            )

            self.db_query_duration = self.registry.create_histogram(
                "nba_db_query_duration_seconds",
                "Database query duration",
                ["operation", "table"]
            )

            self.db_connections = self.registry.create_gauge(
                "nba_db_connections_active",
                "Active database connections"
            )

            self.db_cache_hits = self.registry.create_counter(
                "nba_db_cache_hits_total",
                "Database cache hits",
                ["cache_type"]
            )

            self.db_cache_misses = self.registry.create_counter(
                "nba_db_cache_misses_total",
                "Database cache misses",
                ["cache_type"]
            )

            self.db_errors = self.registry.create_counter(
                "nba_db_errors_total",
                "Database errors",
                ["error_type", "operation"]
            )

            self.db_health_status = self.registry.create_gauge(
                "nba_db_health_status",
                "Database health status (1=healthy, 0=unhealthy)"
            )

            # General Database Infrastructure Metrics
            self.db_connections_total = self.registry.create_counter(
                "db_connections_total",
                "Total database connections",
                ["database", "status"]
            )

            self.db_infrastructure_query_duration = self.registry.create_histogram(
                "db_query_duration_seconds",
                "Database query duration",
                ["database", "query_type"]
            )

            self.db_connection_pool_size = self.registry.create_gauge(
                "db_connection_pool_size",
                "Current connection pool size",
                ["database"]
            )

            self.db_connection_pool_checked_out = self.registry.create_gauge(
                "db_connection_pool_checked_out",
                "Checked out connections",
                ["database"]
            )

            self.cache_operations_total = self.registry.create_counter(
                "cache_operations_total",
                "Total cache operations",
                ["operation", "status"]
            )

            self.cache_hit_ratio = self.registry.create_gauge(
                "cache_hit_ratio",
                "Cache hit ratio"
            )

            self.db_infrastructure_health_status = self.registry.create_gauge(
                "db_health_status",
                "Database health status (1=healthy, 0=unhealthy)",
                ["component"]
            )

            # NBA Database Bulk Operations Metrics
            self.db_bulk_operations = self.registry.create_counter(
                "nba_db_bulk_operations_total",
                "Bulk database operations",
                ["operation", "table"]
            )

            logger.info("✅ Database metrics initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing database metrics: {e}")
            # Initialize with basic fallback metrics
            self._initialize_fallback_metrics()
    
    def _initialize_fallback_metrics(self):
        """Initialize fallback metrics when primary initialization fails"""
        logger.info("🔧 Initializing fallback database metrics...")

        # Create basic in-memory metrics for all required metrics
        metric_names = [
            'db_queries', 'db_query_duration', 'db_connections', 'db_cache_hits',
            'db_cache_misses', 'db_errors', 'db_health_status', 'db_connections_total',
            'db_infrastructure_query_duration', 'db_connection_pool_size',
            'db_connection_pool_checked_out', 'cache_operations_total',
            'cache_hit_ratio', 'db_infrastructure_health_status', 'db_bulk_operations'
        ]

        for metric_name in metric_names:
            setattr(self, metric_name, ProductionInMemoryMetric(metric_name, f"Fallback {metric_name}"))

        logger.info("✅ Fallback database metrics initialized")

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive summary of all database metrics"""
        summary = {
            'timestamp': datetime.now().isoformat(),
            'registry_type': 'shared' if self.registry.shared_registry_available else
                           'prometheus' if self.registry.prometheus_available else 'in_memory',
            'metrics': {}
        }

        # Collect metrics data
        metric_names = [
            'db_queries', 'db_query_duration', 'db_connections', 'db_cache_hits',
            'db_cache_misses', 'db_errors', 'db_health_status'
        ]

        for metric_name in metric_names:
            metric = getattr(self, metric_name, None)
            if metric and hasattr(metric, 'get_statistics'):
                summary['metrics'][metric_name] = metric.get_statistics()
            elif metric and hasattr(metric, 'get_value'):
                summary['metrics'][metric_name] = {'value': metric.get_value()}

        return summary

    def record_query(self, operation: str, table: str, duration: float, status: str = 'success'):
        """Record database query metrics"""
        try:
            self.db_queries.labels(operation=operation, table=table, status=status).inc()
            self.db_query_duration.labels(operation=operation, table=table).observe(duration)

            if status == 'error':
                self.db_errors.labels(error_type='query_error', operation=operation).inc()

        except Exception as e:
            logger.error(f"Error recording query metrics: {e}")

    def record_cache_operation(self, cache_type: str, hit: bool):
        """Record cache operation metrics"""
        try:
            if hit:
                self.db_cache_hits.labels(cache_type=cache_type).inc()
            else:
                self.db_cache_misses.labels(cache_type=cache_type).inc()

        except Exception as e:
            logger.error(f"Error recording cache metrics: {e}")

    def set_health_status(self, healthy: bool):
        """Set database health status"""
        try:
            self.db_health_status.set(1.0 if healthy else 0.0)
        except Exception as e:
            logger.error(f"Error setting health status: {e}")

    def set_connection_count(self, count: int):
        """Set active connection count"""
        try:
            self.db_connections.set(float(count))
        except Exception as e:
            logger.error(f"Error setting connection count: {e}")

def get_database_metrics() -> DatabaseMetrics:
    """Get the global database metrics instance"""
    global _DATABASE_METRICS
    
    if _DATABASE_METRICS is None:
        _DATABASE_METRICS = DatabaseMetrics()
    
    return _DATABASE_METRICS

# Initialize metrics on module import
logger.info("🚀 Initializing database metrics module...")
_DATABASE_METRICS = DatabaseMetrics()
logger.info("✅ Database metrics module ready")
