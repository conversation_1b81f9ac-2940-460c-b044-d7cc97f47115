#!/usr/bin/env python3
"""
🏀 HYPER MEDUSA NEURAL VAULT - System Integration Analysis
=========================================================

Comprehensive analysis of system connections and integration gaps.
"""

import os
import sys
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
import importlib.util

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("SystemIntegrationAnalysis")

class SystemIntegrationAnalyzer:
    """Analyzes system integration and identifies connection gaps"""
    
    def __init__(self):
        self.integration_status = {}
        self.connection_gaps = []
        self.service_dependencies = {}
        self.configuration_issues = []
        
    def analyze_system_architecture(self):
        """Analyze the overall system architecture"""
        logger.info("🔍 ANALYZING SYSTEM ARCHITECTURE")
        logger.info("=" * 60)
        
        # 1. Main Entry Points Analysis
        self._analyze_main_entry_points()
        
        # 2. API Integration Analysis
        self._analyze_api_integration()
        
        # 3. Database Connection Analysis
        self._analyze_database_connections()
        
        # 4. Neural System Integration
        self._analyze_neural_integration()
        
        # 5. Real-time Data Flow Analysis
        self._analyze_realtime_data_flow()
        
        # 6. Configuration Management Analysis
        self._analyze_configuration_management()
        
        # 7. Service Discovery Analysis
        self._analyze_service_discovery()
        
        # 8. Authentication Flow Analysis
        self._analyze_authentication_flow()
        
        # 9. WebSocket Integration Analysis
        self._analyze_websocket_integration()
        
        # 10. Health Monitoring Integration
        self._analyze_health_monitoring()
        
        return self._generate_integration_report()
    
    def _analyze_main_entry_points(self):
        """Analyze main application entry points"""
        logger.info("📍 Analyzing Main Entry Points...")
        
        entry_points = [
            "backend/main.py",
            "backend/main_consolidated.py", 
            "vault_oracle/core/main.py",
            "src/api/prediction_api.py",
            "src/api/ml_prediction_api.py"
        ]
        
        active_entries = []
        for entry in entry_points:
            if os.path.exists(entry):
                active_entries.append(entry)
                logger.info(f"   ✅ Found: {entry}")
            else:
                logger.warning(f"   ❌ Missing: {entry}")
        
        # Check for multiple main files (potential conflict)
        if len(active_entries) > 2:
            self.connection_gaps.append({
                "issue": "Multiple main entry points detected",
                "severity": "MEDIUM",
                "files": active_entries,
                "recommendation": "Consolidate to single main entry point"
            })
        
        self.integration_status["main_entries"] = {
            "status": "MULTIPLE_DETECTED" if len(active_entries) > 2 else "OK",
            "active_files": active_entries,
            "primary_recommended": "backend/main.py"
        }
    
    def _analyze_api_integration(self):
        """Analyze API endpoint integration"""
        logger.info("🌐 Analyzing API Integration...")
        
        api_files = [
            "src/api/prediction_api.py",
            "src/api/ml_prediction_api.py", 
            "backend/routers/",
            "backend/consolidated_api_router.py"
        ]
        
        # Check for API endpoint conflicts
        prediction_apis = [f for f in api_files if "prediction" in f and os.path.exists(f)]
        if len(prediction_apis) > 1:
            self.connection_gaps.append({
                "issue": "Multiple prediction APIs detected",
                "severity": "HIGH", 
                "files": prediction_apis,
                "recommendation": "Consolidate prediction endpoints"
            })
        
        self.integration_status["api_integration"] = {
            "status": "NEEDS_CONSOLIDATION",
            "prediction_apis": prediction_apis,
            "router_system": "backend/routers/" if os.path.exists("backend/routers/") else "MISSING"
        }
    
    def _analyze_database_connections(self):
        """Analyze database connection patterns"""
        logger.info("🗄️ Analyzing Database Connections...")
        
        db_files = [
            "hyper_medusa_consolidated.db",
            "data/unified_nba_wnba_data.db",
            "backend/database/__init__.py",
            "backend/infrastructure/database.py"
        ]
        
        db_status = {}
        for db_file in db_files:
            if os.path.exists(db_file):
                db_status[db_file] = "EXISTS"
                logger.info(f"   ✅ Database: {db_file}")
            else:
                db_status[db_file] = "MISSING"
                logger.warning(f"   ❌ Missing: {db_file}")
        
        self.integration_status["database_connections"] = {
            "status": "PARTIALLY_CONNECTED",
            "databases": db_status,
            "primary_db": "hyper_medusa_consolidated.db"
        }
    
    def _analyze_neural_integration(self):
        """Analyze neural system integration"""
        logger.info("🧠 Analyzing Neural System Integration...")
        
        neural_components = [
            "src/neural_cortex/neural_training_pipeline.py",
            "src/neural_cortex/neural_basketball_core.py",
            "src/cognitive_spires/",
            "src/models/unified_prediction_orchestrator.py"
        ]
        
        neural_status = {}
        for component in neural_components:
            if os.path.exists(component):
                neural_status[component] = "AVAILABLE"
                logger.info(f"   ✅ Neural: {component}")
            else:
                neural_status[component] = "MISSING"
                logger.warning(f"   ❌ Missing: {component}")
        
        self.integration_status["neural_integration"] = {
            "status": "MOSTLY_INTEGRATED",
            "components": neural_status,
            "training_pipeline": "OPERATIONAL"
        }
    
    def _analyze_realtime_data_flow(self):
        """Analyze real-time data flow integration"""
        logger.info("⚡ Analyzing Real-time Data Flow...")
        
        realtime_components = [
            "src/nba_ingestion/nba_real_time_pipeline.py",
            "src/data_integration/real_data_pipeline.py",
            "backend/infrastructure/realtime.py",
            "backend/routers/websocket.py"
        ]
        
        realtime_status = {}
        for component in realtime_components:
            if os.path.exists(component):
                realtime_status[component] = "AVAILABLE"
                logger.info(f"   ✅ Realtime: {component}")
            else:
                realtime_status[component] = "MISSING"
                logger.warning(f"   ❌ Missing: {component}")
        
        self.integration_status["realtime_data_flow"] = {
            "status": "INTEGRATED",
            "components": realtime_status,
            "websocket_support": "AVAILABLE"
        }
    
    def _analyze_configuration_management(self):
        """Analyze configuration management integration"""
        logger.info("⚙️ Analyzing Configuration Management...")
        
        config_files = [
            "kingdom/config/services_config.toml",
            "backend/config/settings.py",
            "vault_oracle/config/",
            "src/core/config.py"
        ]
        
        config_status = {}
        for config in config_files:
            if os.path.exists(config):
                config_status[config] = "AVAILABLE"
                logger.info(f"   ✅ Config: {config}")
            else:
                config_status[config] = "MISSING"
                logger.warning(f"   ❌ Missing: {config}")
        
        self.integration_status["configuration_management"] = {
            "status": "UNIFIED_TOML_AVAILABLE",
            "configs": config_status,
            "primary_config": "kingdom/config/services_config.toml"
        }
    
    def _analyze_service_discovery(self):
        """Analyze service discovery and dependency injection"""
        logger.info("🔍 Analyzing Service Discovery...")
        
        di_files = [
            "backend/dependencies.py",
            "src/architecture/advanced_patterns.py",
            "backend/services/"
        ]
        
        di_status = {}
        for di_file in di_files:
            if os.path.exists(di_file):
                di_status[di_file] = "AVAILABLE"
                logger.info(f"   ✅ DI: {di_file}")
            else:
                di_status[di_file] = "MISSING"
                logger.warning(f"   ❌ Missing: {di_file}")
        
        self.integration_status["service_discovery"] = {
            "status": "IMPLEMENTED",
            "components": di_status,
            "pattern": "DEPENDENCY_INJECTION"
        }
    
    def _analyze_authentication_flow(self):
        """Analyze authentication and authorization flow"""
        logger.info("🔐 Analyzing Authentication Flow...")
        
        auth_components = [
            "backend/middleware/auth_middleware.py",
            "backend/services/auth_service.py",
            "backend/database/models.py"
        ]
        
        auth_status = {}
        for component in auth_components:
            if os.path.exists(component):
                auth_status[component] = "AVAILABLE"
                logger.info(f"   ✅ Auth: {component}")
            else:
                auth_status[component] = "MISSING"
                logger.warning(f"   ❌ Missing: {component}")
        
        self.integration_status["authentication_flow"] = {
            "status": "IMPLEMENTED",
            "components": auth_status,
            "middleware": "AVAILABLE"
        }
    
    def _analyze_websocket_integration(self):
        """Analyze WebSocket integration"""
        logger.info("🔌 Analyzing WebSocket Integration...")
        
        ws_components = [
            "backend/routers/websocket.py",
            "backend/infrastructure/realtime.py"
        ]
        
        ws_status = {}
        for component in ws_components:
            if os.path.exists(component):
                ws_status[component] = "AVAILABLE"
                logger.info(f"   ✅ WebSocket: {component}")
            else:
                ws_status[component] = "MISSING"
                logger.warning(f"   ❌ Missing: {component}")
        
        self.integration_status["websocket_integration"] = {
            "status": "FULLY_INTEGRATED",
            "components": ws_status,
            "connection_manager": "AVAILABLE"
        }
    
    def _analyze_health_monitoring(self):
        """Analyze health monitoring integration"""
        logger.info("🏥 Analyzing Health Monitoring...")
        
        health_components = [
            "backend/monitoring/production_health_monitor.py",
            "src/core/health_monitoring.py",
            "backend/infrastructure/monitoring.py"
        ]
        
        health_status = {}
        for component in health_components:
            if os.path.exists(component):
                health_status[component] = "AVAILABLE"
                logger.info(f"   ✅ Health: {component}")
            else:
                health_status[component] = "MISSING"
                logger.warning(f"   ❌ Missing: {component}")
        
        self.integration_status["health_monitoring"] = {
            "status": "COMPREHENSIVE",
            "components": health_status,
            "prometheus_support": "AVAILABLE"
        }
    
    def _generate_integration_report(self):
        """Generate comprehensive integration report"""
        logger.info("\n" + "=" * 60)
        logger.info("📊 SYSTEM INTEGRATION REPORT")
        logger.info("=" * 60)
        
        # Overall system health
        total_systems = len(self.integration_status)
        healthy_systems = sum(1 for status in self.integration_status.values() 
                            if status.get("status") not in ["MISSING", "CRITICAL"])
        
        integration_health = (healthy_systems / total_systems) * 100
        
        logger.info(f"🎯 Overall Integration Health: {integration_health:.1f}%")
        logger.info(f"✅ Healthy Systems: {healthy_systems}/{total_systems}")
        logger.info(f"⚠️ Connection Gaps: {len(self.connection_gaps)}")
        
        # System status summary
        logger.info("\n📋 SYSTEM STATUS SUMMARY:")
        for system, status in self.integration_status.items():
            status_icon = "✅" if status.get("status") not in ["MISSING", "CRITICAL"] else "❌"
            logger.info(f"   {status_icon} {system.replace('_', ' ').title()}: {status.get('status')}")
        
        # Critical gaps
        if self.connection_gaps:
            logger.info("\n🚨 CRITICAL CONNECTION GAPS:")
            for gap in self.connection_gaps:
                logger.info(f"   ⚠️ {gap['issue']} (Severity: {gap['severity']})")
                logger.info(f"      Recommendation: {gap['recommendation']}")
        
        return {
            "integration_health": integration_health,
            "system_status": self.integration_status,
            "connection_gaps": self.connection_gaps,
            "recommendations": self._generate_recommendations()
        }
    
    def _generate_recommendations(self):
        """Generate integration improvement recommendations"""
        recommendations = []
        
        # Main entry point consolidation
        if len(self.integration_status.get("main_entries", {}).get("active_files", [])) > 2:
            recommendations.append({
                "priority": "HIGH",
                "action": "Consolidate main entry points",
                "description": "Use backend/main.py as primary entry point",
                "impact": "Eliminates startup conflicts and improves maintainability"
            })
        
        # API consolidation
        if self.integration_status.get("api_integration", {}).get("status") == "NEEDS_CONSOLIDATION":
            recommendations.append({
                "priority": "HIGH", 
                "action": "Consolidate prediction APIs",
                "description": "Merge prediction endpoints into unified API",
                "impact": "Reduces endpoint conflicts and improves API consistency"
            })
        
        # Database optimization
        recommendations.append({
            "priority": "MEDIUM",
            "action": "Optimize database connections",
            "description": "Implement connection pooling and health checks",
            "impact": "Improves database performance and reliability"
        })
        
        return recommendations

def main():
    """Run system integration analysis"""
    analyzer = SystemIntegrationAnalyzer()
    report = analyzer.analyze_system_architecture()
    
    print("\n" + "=" * 60)
    print("🎯 INTEGRATION ANALYSIS COMPLETE")
    print("=" * 60)
    print(f"Integration Health: {report['integration_health']:.1f}%")
    print(f"Connection Gaps: {len(report['connection_gaps'])}")
    print(f"Recommendations: {len(report['recommendations'])}")

if __name__ == "__main__":
    main()
