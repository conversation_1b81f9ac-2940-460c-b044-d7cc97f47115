import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from collections import defaultdict
from src.features.feature_feedback import FeatureFeedback
from vault_oracle.analysis.unified_temporal_anomaly import unified_temporal_anomaly_detector
from enum import Enum

try:
    from src.features.feature_alchemist import SelfLearningFeatureAlchemist
    FEATURE_ALCHEMIST_AVAILABLE = True
except ImportError:
    FEATURE_ALCHEMIST_AVAILABLE = False
    SelfLearningFeatureAlchemist = None

# Optional import to avoid circular dependency
try:
    pass  # Add specific imports here if needed
except ImportError:
    # Fallback for circular import
    SelfLearningFeatureAlchemist = None

"""
HYPER MEDUSA DEFENSIVE STRATEGIST - Advanced Threat Analysis System
===============================================================================

AresDefenseOracle_Expert (DefensiveStrategist):
- The town’s defensive expert, specializing in threat assessment, vulnerability analysis, and countermeasure generation.
- Participates in the War Council’s feedback broadcast/registration system for checks and balances.
- Integrates with the Feature Alchemist, Unified Retrainer, and Unified Drift Detector for continuous improvement.
- All major decisions, feedback, and retraining triggers are logged for auditability and explainability.
"""

#!/usr/bin/env python3
"""
AresDefenseOracle_Expert.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Expert-Level Ares Defense Oracle - Advanced Defensive Analysis and Prediction
"""


logger = logging.getLogger(__name__)

@dataclass
class DefensiveContext:
    """Context for defensive analysis"""
    game_situation: str
    team_defensive_data: Dict[str, Any] = field(default_factory=dict)
    opponent_offensive_data: Dict[str, Any] = field(default_factory=dict)
    situational_factors: Dict[str, Any] = field(default_factory=dict)
    defensive_constraints: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=datetime.now)

class ThreatLevel(Enum):
    """Threat assessment levels"""
    NEUTRAL = 0
    CAUTION = 1
    ELEVATED = 2
    HIGH = 3
    SEVERE = 4

@dataclass
class DefensiveInsight:
    """Comprehensive defensive strategy assessment"""
    game_id: str
    threat_assessment: Dict[str, float]  # Player/play type threat scores
    vulnerabilities: List[str]
    risk_score: float  # 0-1 scale
    timestamp: datetime = field(default_factory=datetime.utcnow)
    overall_threat_level: ThreatLevel = ThreatLevel.NEUTRAL
    countermeasures: list = field(default_factory=list)  # Remove type hint if Countermeasure not yet defined
    confidence: float = 0.0
    context_metadata: Dict[str, Any] = field(default_factory=dict)
    rationale: str = ""  # Explanation for decisions

# Move DefensivePattern above Countermeasure
class DefensivePattern(str, Enum):
    """Library of defensive patterns"""
    ICE = "ice"
    BLITZ = "blitz"
    SWITCH_EVERYTHING = "switch_everything"
    ZONE_2_3 = "2-3_zone"
    ZONE_3_2 = "3-2_zone"
    BOX_AND_1 = "box_and_1"
    MAN_TO_MAN = "man_to_man"
    FULL_COURT_PRESS = "full_court_press"
    TRAP = "trap"
    GAP_CONTROL = "gap_control"

@dataclass
class Countermeasure:
    pattern: DefensivePattern
    intensity: float  # 0-1 scale
    target_player: Optional[str] = None
    execution_risk: float = field(default=0.0)
    expected_efficiency: float = field(default=0.0)

class AresDefenseOracle_Expert:
    """
    Expert-Level Ares Defense Oracle
    ==============================
    Advanced defensive analysis, threat assessment, and protective strategy system
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None, enable_advanced_metrics: bool = True, **kwargs):
        """Initialize the Expert Ares Defense Oracle"""
        self.enable_advanced_metrics = enable_advanced_metrics
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info(" MEDUSA VAULT: AresDefenseOracle_Expert initialized with advanced defensive modeling")

        # Advanced defensive components
        self.defensive_patterns = defaultdict(dict)
        self.threat_intelligence = {}
        self.countermeasure_library = {}
        self.vulnerability_database = defaultdict(list)

        # Defensive metrics
        self.defensive_weights = self._initialize_defensive_weights()
        self.threat_categories = self._initialize_threat_categories()

        # Performance tracking
        self.defense_effectiveness = []
        self.threat_predictions = []
        self.response_history = []

        # Feature Alchemist integration
        self.feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)

    def _initialize_defensive_weights(self) -> Dict[str, float]:
        """Initialize defensive weighting factors"""
        return {
            'perimeter_defense': 0.22,
            'interior_defense': 0.20,
            'transition_defense': 0.18,
            'defensive_rebounding': 0.15,
            'steal_generation': 0.12,
            'help_defense': 0.13
        }

    def _initialize_threat_categories(self) -> Dict[str, Dict[str, float]]:
        """Initialize threat categorization system"""
        return {
            'high_volume_scoring': {
                'threat_multiplier': 1.3,
                'priority_level': 0.9,
                'response_urgency': 0.8
            },
            'elite_playmaking': {
                'threat_multiplier': 1.2,
                'priority_level': 0.85,
                'response_urgency': 0.75
            },
            'interior_dominance': {
                'threat_multiplier': 1.25,
                'priority_level': 0.88,
                'response_urgency': 0.9
            },
            'three_point_barrage': {
                'threat_multiplier': 1.4,
                'priority_level': 0.92,
                'response_urgency': 0.85
            },
            'transition_speed': {
                'threat_multiplier': 1.15,
                'priority_level': 0.8,
                'response_urgency': 0.95
            }
        }

    async def analyze_defensive_situation(self, context: DefensiveContext) -> DefensiveInsight:
        """
        Perform comprehensive defensive analysis

        Args:
            context: Defensive context for analysis

        Returns:
            Defensive insight with strategic recommendations
        """
        try:
            # Multi-layered defensive analysis
            threat_analysis = await self._analyze_offensive_threats(context)
            vulnerability_analysis = await self._analyze_defensive_vulnerabilities(context)
            # Corrected: Call the renamed method for situational factors analysis
            situational_analysis = await self._analyze_situational_factors(context)
            countermeasure_analysis = await self._analyze_countermeasures(context)

            # Synthesize defensive insight
            defensive_strategy = self._determine_optimal_defense(
                threat_analysis, vulnerability_analysis, situational_analysis
            )

            threat_level = self._assess_threat_level(
                threat_analysis, context
            )

            confidence = self._calculate_defensive_confidence(
                threat_analysis, vulnerability_analysis, situational_analysis
            )

            key_vulnerabilities = self._identify_key_vulnerabilities(
                vulnerability_analysis, threat_analysis
            )

            defensive_adjustments = self._recommend_defensive_adjustments(
                defensive_strategy, context, vulnerability_analysis
            )

            risk_assessment = self._assess_defensive_risks(
                defensive_strategy, context, threat_analysis
            )

            expected_effectiveness = self._estimate_defensive_effectiveness(
                defensive_strategy, context, countermeasure_analysis
            )

            countermeasures = self._select_countermeasures(
                threat_analysis, defensive_strategy
            )

            insight = DefensiveInsight(
                defensive_strategy=defensive_strategy,
                threat_level=threat_level,
                confidence=confidence,
                key_vulnerabilities=key_vulnerabilities,
                defensive_adjustments=defensive_adjustments,
                risk_assessment=risk_assessment,
                expected_effectiveness=expected_effectiveness,
                countermeasures=countermeasures
            )

            # Update defensive intelligence
            self._update_defensive_intelligence(context, insight)

            return insight

        except Exception as e:
            self.logger.error(f" Defensive analysis failed: {e}")
            return self._generate_fallback_insight()

    async def _analyze_offensive_threats(self, context: DefensiveContext) -> Dict[str, Any]:
        """Analyze offensive threats and attack patterns"""
        threats = {
            'primary_scorers': np.random.randint(1, 4),
            'scoring_efficiency': np.random.uniform(0.4, 0.7),
            'three_point_threat': np.random.uniform(0.3, 0.8),
            'interior_threat': np.random.uniform(0.2, 0.7),
            'playmaking_threat': np.random.uniform(0.3, 0.8),
            'transition_threat': np.random.uniform(0.2, 0.9)
        }

        # Threat categorization
        dominant_threats = []
        for threat_type, threshold in [
            ('three_point_threat', 0.6),
            ('interior_threat', 0.6),
            ('playmaking_threat', 0.65),
            ('transition_threat', 0.7)
        ]:
            if threats[threat_type] > threshold:
                dominant_threats.append(threat_type)

        threats['dominant_threats'] = dominant_threats

        # Threat evolution
        threats['threat_trend'] = np.random.choice(['increasing', 'stable', 'decreasing'])
        threats['adaptation_speed'] = np.random.uniform(0.3, 0.8)

        # Overall threat score
        threat_components = [
            threats['scoring_efficiency'] * 0.3,
            threats['three_point_threat'] * 0.25,
            threats['interior_threat'] * 0.2,
            threats['playmaking_threat'] * 0.15,
            threats['transition_threat'] * 0.1
        ]
        threats['overall_threat_score'] = sum(threat_components)

        return threats

    async def _analyze_defensive_vulnerabilities(self, context: DefensiveContext) -> Dict[str, Any]:
        """Analyze current defensive vulnerabilities"""
        vulnerabilities = {
            'perimeter_weakness': np.random.uniform(0.1, 0.6),
            'interior_weakness': np.random.uniform(0.1, 0.5),
            'help_defense_gaps': np.random.uniform(0.1, 0.7),
            'transition_vulnerability': np.random.uniform(0.2, 0.8),
            'rebounding_weakness': np.random.uniform(0.1, 0.6),
            'communication_gaps': np.random.uniform(0.1, 0.5)
        }

        # Identify critical vulnerabilities
        critical_vulnerabilities = []
        for vuln, level in vulnerabilities.items():
            if level > 0.5:
                critical_vulnerabilities.append(vuln)

        vulnerabilities['critical_vulnerabilities'] = critical_vulnerabilities

        # Vulnerability clusters
        if vulnerabilities['perimeter_weakness'] > 0.4 and vulnerabilities['help_defense_gaps'] > 0.4:
            vulnerabilities['perimeter_cluster'] = True

        if vulnerabilities['interior_weakness'] > 0.4 and vulnerabilities['rebounding_weakness'] > 0.4:
            vulnerabilities['interior_cluster'] = True

        # Overall defensive fragility
        vuln_scores = [v for k, v in vulnerabilities.items() if isinstance(v, float)]
        vulnerabilities['defensive_fragility'] = np.mean(vuln_scores)

        return vulnerabilities

    async def _analyze_situational_factors(self, context: DefensiveContext) -> Dict[str, Any]:
        """Analyze current defensive situational factors (renamed from _analyze_defensive_situation to avoid recursion)"""
        situation = {
            'personnel_availability': np.random.uniform(0.6, 1.0),
            'foul_trouble': np.random.uniform(0.0, 0.6),
            'fatigue_level': np.random.uniform(0.1, 0.7),
            'momentum_factor': np.random.uniform(-0.5, 0.5),
            'crowd_energy': np.random.uniform(0.2, 0.8),
            'coaching_adjustments': np.random.uniform(0.3, 0.8)
        }

        # Situational advantages/disadvantages
        situation['defensive_advantages'] = []
        situation['defensive_disadvantages'] = []

        if situation['personnel_availability'] > 0.85:
            situation['defensive_advantages'].append('full_roster_available')
        elif situation['personnel_availability'] < 0.7:
            situation['defensive_disadvantages'].append('limited_personnel')

        if situation['foul_trouble'] > 0.4:
            situation['defensive_disadvantages'].append('foul_trouble')

        if situation['fatigue_level'] > 0.5:
            situation['defensive_disadvantages'].append('player_fatigue')

        # Situational defensive capacity
        capacity_factors = [
            situation['personnel_availability'] * 0.4,
            (1 - situation['foul_trouble']) * 0.3,
            (1 - situation['fatigue_level']) * 0.2,
            situation['coaching_adjustments'] * 0.1
        ]
        situation['defensive_capacity'] = sum(capacity_factors)

        # Added for _assess_vulnerabilities features
        situation['vulnerability_severity'] = np.random.uniform(0.3, 0.7) # Placeholder for now
        
        return situation

    async def _analyze_countermeasures(self, context: DefensiveContext) -> Dict[str, Any]:
        """Analyze available defensive countermeasures"""
        countermeasures = {
            'scheme_flexibility': np.random.uniform(0.4, 0.9),
            'personnel_versatility': np.random.uniform(0.3, 0.8),
            'adjustment_speed': np.random.uniform(0.2, 0.7),
            'communication_quality': np.random.uniform(0.4, 0.9),
            'execution_consistency': np.random.uniform(0.3, 0.8)
        }

        # Available schemes
        available_schemes = [
            'man_to_man',
            'zone_defense',
            'switching_defense',
            'help_and_recover',
            'pressure_defense'
        ]

        # Scheme effectiveness prediction
        scheme_effectiveness = {}
        for scheme in available_schemes:
            base_effectiveness = np.random.uniform(0.3, 0.8)
            # Adjust based on countermeasure capabilities
            adjusted_effectiveness = base_effectiveness * (
                countermeasures['scheme_flexibility'] * 0.3 +
                countermeasures['execution_consistency'] * 0.4 +
                countermeasures['communication_quality'] * 0.3
            )
            scheme_effectiveness[scheme] = min(0.9, adjusted_effectiveness)

        countermeasures['scheme_effectiveness'] = scheme_effectiveness
        countermeasures['available_schemes'] = available_schemes

        return countermeasures

    def _determine_optimal_defense(self, threats: Dict, vulnerabilities: Dict, situation: Dict) -> str:
        """Determine optimal defensive strategy"""
        strategies = [
            'aggressive_pressure',
            'conservative_containment',
            'switching_scheme',
            'help_heavy',
            'perimeter_focused',
            'interior_focused'
        ]

        # Strategy selection logic
        dominant_threats = threats.get('dominant_threats', [])

        if 'three_point_threat' in dominant_threats:
            return 'perimeter_focused'
        elif 'interior_threat' in dominant_threats:
            return 'interior_focused'
        elif 'transition_threat' in dominant_threats:
            return 'aggressive_pressure'
        elif threats.get('overall_threat_score', 0.5) > 0.65:
            return 'help_heavy'
        elif situation.get('defensive_capacity', 0.5) > 0.7:
            return 'switching_scheme'
        else:
            return 'conservative_containment'

    def _assess_threat_level(self, threats: Dict, context: DefensiveContext) -> str:
        """Assess overall threat level"""
        threat_score = threats.get('overall_threat_score', 0.5)
        dominant_count = len(threats.get('dominant_threats', []))

        if threat_score > 0.7 or dominant_count >= 3:
            return 'CRITICAL'
        elif threat_score > 0.55 or dominant_count >= 2:
            return 'HIGH'
        elif threat_score > 0.4 or dominant_count >= 1:
            return 'MODERATE'
        else:
            return 'LOW'

    def _calculate_defensive_confidence(self, threats: Dict, vulnerabilities: Dict, situation: Dict) -> float:
        """Calculate confidence in defensive assessment"""
        base_confidence = 0.5

        # Confidence factors
        confidence_factors = [
            (1 - vulnerabilities.get('defensive_fragility', 0.5)) * 0.3,
            situation.get('defensive_capacity', 0.5) * 0.25,
            (1 - threats.get('overall_threat_score', 0.5)) * 0.2,
            situation.get('coaching_adjustments', 0.5) * 0.15,
            (1 - len(vulnerabilities.get('critical_vulnerabilities', [])) / 6) * 0.1
        ]

        final_confidence = base_confidence + sum(confidence_factors)
        return min(0.95, max(0.1, final_confidence))

    def _identify_key_vulnerabilities(self, vulnerabilities: Dict, threats: Dict) -> List[str]:
        """Identify key vulnerabilities that need immediate attention"""
        key_vulnerabilities = []

        # Critical vulnerabilities
        critical_vulns = vulnerabilities.get('critical_vulnerabilities', [])
        key_vulnerabilities.extend(critical_vulns[:3]) # Top 3 critical

        # Threat-matched vulnerabilities
        dominant_threats = threats.get('dominant_threats', [])
        threat_vuln_map = {
            'three_point_threat': 'perimeter_weakness',
            'interior_threat': 'interior_weakness',
            'transition_threat': 'transition_vulnerability',
            'playmaking_threat': 'help_defense_gaps'
        }

        for threat in dominant_threats:
            if threat in threat_vuln_map:
                vuln = threat_vuln_map[threat]
                if vuln not in key_vulnerabilities:
                    key_vulnerabilities.append(vuln)

        return key_vulnerabilities[:5] # Limit to top 5

    def _recommend_defensive_adjustments(self, strategy: str, context: DefensiveContext, vulnerabilities: Dict) -> List[str]:
        """Recommend specific defensive adjustments"""
        adjustment_map = {
            'aggressive_pressure': [
                'Increase defensive pressure at point of attack',
                'Force ball handlers to uncomfortable spots',
                'Trap in corners and sidelines',
                'Contest all shots aggressively'
            ],
            'conservative_containment': [
                'Maintain defensive discipline',
                'Force difficult shots without gambling',
                'Protect against easy baskets',
                'Communicate switches clearly'
            ],
            'switching_scheme': [
                'Switch all screens 1-4',
                'Maintain size matchups when possible',
                'Communicate switch calls early',
                'Help on mismatches immediately'
            ],
            'help_heavy': [
                'Send help on all drives',
                'Rotate aggressively to open shooters',
                'Collapse on post-ups',
                'Prioritize rim protection'
            ],
            'perimeter_focused': [
                'Contest all three-point attempts',
                'Close out with control',
                'Force drives into help',
                'Limit catch-and-shoot opportunities'
            ],
            'interior_focused': [
                'Double team in post',
                'Contest all shots at rim',
                'Box out aggressively',
                'Help early on drives'
            ]
        }

        base_adjustments = adjustment_map.get(strategy, ['Maintain defensive principles'])

        # Add vulnerability-specific adjustments
        vuln_adjustments = {
            'communication_gaps': 'Increase vocal communication',
            'transition_vulnerability': 'Get back in transition quickly',
            'rebounding_weakness': 'Emphasize boxing out',
            'help_defense_gaps': 'Provide timely help defense'
        }

        key_vulns = vulnerabilities.get('critical_vulnerabilities', [])
        for vuln in key_vulns[:2]: # Top 2 vulnerabilities
            if vuln in vuln_adjustments:
                base_adjustments.append(vuln_adjustments[vuln])

        return base_adjustments[:6] # Limit to 6 adjustments

    def _assess_defensive_risks(self, strategy: str, context: DefensiveContext, threats: Dict) -> Dict[str, float]:
        """Assess risks associated with defensive strategy"""
        base_risks = {
            'foul_risk': np.random.uniform(0.1, 0.4),
            'fatigue_risk': np.random.uniform(0.1, 0.3),
            'mismatch_risk': np.random.uniform(0.05, 0.35),
            'breakdown_risk': np.random.uniform(0.1, 0.4),
            'adjustment_risk': np.random.uniform(0.05, 0.25)
        }

        # Strategy-specific risk modifiers
        strategy_risk_modifiers = {
            'aggressive_pressure': {'foul_risk': 1.4, 'fatigue_risk': 1.3},
            'conservative_containment': {'breakdown_risk': 0.8, 'adjustment_risk': 1.2},
            'switching_scheme': {'mismatch_risk': 1.3, 'communication_risk': 1.2},
            'help_heavy': {'breakdown_risk': 1.2, 'fatigue_risk': 1.1},
            'perimeter_focused': {'interior_risk': 1.3, 'mismatch_risk': 1.1},
            'interior_focused': {'perimeter_risk': 1.4, 'foul_risk': 1.2}
        }

        modifiers = strategy_risk_modifiers.get(strategy, {})
        for risk, modifier in modifiers.items():
            if risk in base_risks:
                base_risks[risk] *= modifier
            else:
                base_risks[risk] = np.random.uniform(0.1, 0.3) * modifier

        return base_risks

    def _estimate_defensive_effectiveness(self, strategy: str, context: DefensiveContext, countermeasures: Dict) -> float:
        """Estimate expected defensive effectiveness"""
        base_effectiveness = 0.5

        # Strategy effectiveness
        strategy_effectiveness = {
            'aggressive_pressure': np.random.uniform(0.4, 0.8),
            'conservative_containment': np.random.uniform(0.5, 0.7),
            'switching_scheme': np.random.uniform(0.45, 0.75),
            'help_heavy': np.random.uniform(0.5, 0.8),
            'perimeter_focused': np.random.uniform(0.4, 0.7),
            'interior_focused': np.random.uniform(0.45, 0.75)
        }

        strategy_component = strategy_effectiveness.get(strategy, 0.5)

        # Countermeasure capability
        countermeasure_component = countermeasures.get('execution_consistency', 0.5) * 0.3

        # Communication quality
        communication_component = countermeasures.get('communication_quality', 0.5) * 0.2

        final_effectiveness = (strategy_component * 0.5 +
                               countermeasure_component +
                               communication_component +
                               base_effectiveness * 0.3)

        return min(0.9, max(0.2, final_effectiveness))

    def _select_countermeasures(self, threats: Dict, strategy: str) -> List[str]:
        """Select appropriate countermeasures"""
        countermeasure_library = {
            'three_point_threat': [
                'Close out with control',
                'Force off three-point line',
                'Contest all attempts'
            ],
            'interior_threat': [
                'Send double teams',
                'Front the post',
                'Help early and often'
            ],
            'transition_threat': [
                'Get back quickly',
                'Communicate numbers',
                'Stop ball first'
            ],
            'playmaking_threat': [
                'Pressure ball handler',
                'Deny easy passes',
                'Switch to disrupt timing'
            ]
        }

        selected_countermeasures = []
        dominant_threats = threats.get('dominant_threats', [])

        for threat in dominant_threats:
            if threat in countermeasure_library:
                selected_countermeasures.extend(countermeasure_library[threat][:2])

        # Add strategy-specific countermeasures
        strategy_countermeasures = {
            'aggressive_pressure': ['Maintain pressure intensity', 'Rotate help quickly'],
            'switching_scheme': ['Communicate switches early', 'Help on mismatches'],
            'help_heavy': ['Rotate to shooters', 'Contest all shots']
        }

        if strategy in strategy_countermeasures:
            selected_countermeasures.extend(strategy_countermeasures[strategy])

        return list(set(selected_countermeasures))[:6] # Remove duplicates and limit

    def _update_defensive_intelligence(self, context: DefensiveContext, insight: DefensiveInsight):
        """Update defensive intelligence database"""
        intel_key = f"{insight.defensive_strategy}_{datetime.now().strftime('%Y%m%d')}"

        if intel_key not in self.threat_intelligence:
            self.threat_intelligence[intel_key] = []

        self.threat_intelligence[intel_key].append({
            'timestamp': context.timestamp,
            'insight': insight,
            'threat_level': insight.threat_level,
            'effectiveness': insight.expected_effectiveness
        })

        # Update vulnerability database
        for vuln in insight.key_vulnerabilities:
            self.vulnerability_database[vuln].append({
                'timestamp': context.timestamp,
                'strategy': insight.defensive_strategy,
                'severity': context.situational_factors.get('vulnerability_severity', 0.5)
            })

        # Limit database size
        if len(self.threat_intelligence[intel_key]) > 100:
            self.threat_intelligence[intel_key] = self.threat_intelligence[intel_key][-50:]

    def _generate_fallback_insight(self) -> DefensiveInsight:
        """Generate fallback insight for error cases"""
        return DefensiveInsight(
            defensive_strategy='conservative_containment',
            threat_level='MODERATE',
            confidence=0.3,
            key_vulnerabilities=['communication_gaps'],
            defensive_adjustments=['Maintain defensive discipline'],
            risk_assessment={'unknown_risk': 0.5},
            expected_effectiveness=0.4,
            countermeasures=['Contest all shots']
        )

    async def predict_win_probability(self, team_data: Dict[str, Any], opponent_data: Dict[str, Any]) -> float:
        """
        Predict win probability using defensive analysis

        Args:
            team_data: Team defensive data
            opponent_data: Opponent offensive data

        Returns:
            Win probability (0.0 to 1.0)
        """
        try:
            # Create defensive context
            context = DefensiveContext(
                game_situation='prediction_analysis',
                team_defensive_data=team_data,
                opponent_offensive_data=opponent_data,
                situational_factors={'analysis_type': 'pre_game'}
            )

            # Get defensive insight
            insight = await self.analyze_defensive_situation(context)

            # Calculate base probability
            base_prob = 0.5

            # Apply defensive adjustments
            effectiveness_adjustment = (insight.expected_effectiveness - 0.5) * 0.15
            confidence_adjustment = (insight.confidence - 0.5) * 0.08

            # Threat level adjustments
            threat_adjustments = {
                'LOW': 0.05,
                'MODERATE': 0.01,
                'HIGH': -0.03,
                'CRITICAL': -0.08
            }
            threat_adjustment = threat_adjustments.get(insight.threat_level, 0.0)

            # Risk assessment
            avg_risk = np.mean(list(insight.risk_assessment.values())) if insight.risk_assessment else 0.3
            risk_adjustment = -(avg_risk - 0.25) * 0.1

            # Calculate final probability
            win_prob = (base_prob + effectiveness_adjustment + confidence_adjustment +
                        threat_adjustment + risk_adjustment)

            # Ensure valid probability range
            win_prob = max(0.1, min(0.9, win_prob))

            self.logger.info(f" Defensive win probability: {win_prob:.3f} ({insight.threat_level} threat)")

            return win_prob

        except Exception as e:
            self.logger.error(f" Win probability prediction failed: {e}")
            return 0.5

    def get_defensive_summary(self) -> Dict[str, Any]:
        """Get summary of defensive capabilities and performance"""
        return {
            'spire_type': 'AresDefenseOracle_Expert',
            'version': '2.0.0',
            'capabilities': [
                'Advanced Threat Analysis',
                'Defensive Strategy Optimization',
                'Vulnerability Assessment',
                'Countermeasure Selection',
                'Risk-Based Defense Planning',
                'Real-time Adjustment Recommendations'
            ],
            'threat_intelligence_entries': len(self.threat_intelligence),
            'vulnerability_database_size': sum(len(vulns) for vulns in self.vulnerability_database.values()),
            'defense_analysis_count': len(self.defense_effectiveness),
            'average_effectiveness': np.mean(self.defense_effectiveness) if self.defense_effectiveness else 0.5,
            'last_updated': datetime.now().isoformat()
        }

    def predict(self, features, *args, **kwargs):
        """
        Standard prediction method for compatibility with test frameworks
        """
        try:
            # Extract defensive features
            features = self._extract_all_features(features)

            # Generate defensive analysis
            defensive_strength = self._calculate_defensive_strength(features)
            vulnerability_score = self._assess_vulnerabilities(features)

            prediction = {
                'prediction': defensive_strength,
                'vulnerability': vulnerability_score,
                'confidence': min(0.95, max(0.1, 1.0 - abs(defensive_strength - 0.5))),
                'spire_type': 'ares_defense',
                'features_used': len(features)
            }

            # --- Feedback wiring: send feedback if confidence is low ---
            confidence = prediction.get('confidence', 1.0)
            if confidence < 0.3:
                feedback = FeatureFeedback(self.__class__.__name__, features, confidence, message="Low confidence. Requesting feature improvement.")
                self.feature_alchemist.receive_feedback(feedback)

            return prediction
        except Exception as e:
            self.logger.error(f" Prediction error: {e}")
            return {
                'prediction': 0.5,
                'vulnerability': 0.0,
                'confidence': 0.1,
                'spire_type': 'ares_defense',
                'error': str(e)
            }

    def _extract_all_features(self, data: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract comprehensive defensive features from game data
        """
        try:
            features = {}

            # Core defensive metrics
            features.update({
                'defensive_rating': data.get('defensive_rating', 110.0) / 130.0,
                'opponent_fg_pct': 1.0 - data.get('opponent_fg_pct', 0.45), # Lower is better
                'defensive_rebounds_pct': data.get('defensive_rebounds_pct', 0.75),
                'steals_per_game': data.get('steals_per_game', 8.0) / 15.0,
                'blocks_per_game': data.get('blocks_per_game', 5.0) / 10.0,
                'turnovers_forced': data.get('turnovers_forced', 12.0) / 20.0, # Added
                'fouls_per_game': (30.0 - data.get('fouls_per_game', 20.0)) / 30.0, # Added, normalized inversely
            })

            # Defensive intensity and effort
            features.update({
                'deflections_per_game': data.get('deflections_per_game', 15.0) / 30.0,
                'charges_drawn': data.get('charges_drawn', 1.0) / 5.0,
                'loose_balls_recovered': data.get('loose_balls_recovered', 5.0) / 15.0,
                'defensive_rotations': data.get('defensive_rotations', 20.0) / 40.0,
                'help_defense_rate': data.get('help_defense_rate', 0.6)
            })

            # Perimeter defense
            features.update({
                'opponent_three_pct': 1.0 - data.get('opponent_three_pct', 0.35), # Lower is better
                'closeout_speed': data.get('closeout_speed', 0.7),
                'on_ball_defense': data.get('on_ball_defense', 0.6),
                'screen_navigation': data.get('screen_navigation', 0.6),
                'transition_defense': data.get('transition_defense', 0.7),
                'perimeter_defense': data.get('perimeter_defense', 0.7) # Added
            })

            # Interior defense
            features.update({
                'paint_protection': data.get('paint_protection', 0.7),
                'rim_protection': data.get('rim_protection', 0.6),
                'post_defense': data.get('post_defense', 0.6),
                'defensive_positioning': data.get('defensive_positioning', 0.7),
                'weak_side_help': data.get('weak_side_help', 0.6)
            })

            # Team defensive concepts
            features.update({
                'communication': data.get('communication', 0.7),
                'switching_ability': data.get('switching_ability', 0.6),
                'zone_defense_eff': data.get('zone_defense_eff', 0.5),
                'man_defense_eff': data.get('man_defense_eff', 0.7),
                'defensive_chemistry': data.get('defensive_chemistry', 0.6),
                'defensive_iq': data.get('defensive_iq', 0.6) # Added
            })

            # Situational defense
            features.update({
                'clutch_defense': data.get('clutch_defense', 0.6),
                'playoff_defense': data.get('playoff_defense', 0.7),
                'home_defense': data.get('home_defense', 0.7) if data.get('is_home', True) else 0.6,
                'fatigue_resistance': data.get('fatigue_resistance', 0.6),
                'foul_discipline': data.get('foul_discipline', 0.7),
                'injury_impact': data.get('injury_impact', 0.0), # Added, boolean to float
                'fatigue_level': data.get('fatigue_level', 0.0), # Added, boolean to float
                'foul_trouble_risk': data.get('foul_trouble_risk', 0.3), # Added, boolean to float
                'back_to_back': 1.0 if data.get('back_to_back', False) else 0.0, # Added, boolean to float
                'key_player_out': 1.0 if data.get('key_player_out', False) else 0.0, # Added, boolean to float
                'road_game': 1.0 if not data.get('is_home', True) else 0.0, # Added, boolean to float
                'pace_mismatch': data.get('pace_mismatch', 0.0), # Added
                'size_disadvantage': data.get('size_disadvantage', 0.0) # Added
            })

            # Recent performance trends
            recent_games = data.get('recent_games', [])
            if recent_games:
                def_ratings = [game.get('def_rating', 110) for game in recent_games]
                avg_def_rating = np.mean(def_ratings)
                features.update({
                    'recent_def_form': (130.0 - avg_def_rating) / 20.0, # Normalized
                    'defensive_consistency': 1.0 - (np.std(def_ratings) / max(avg_def_rating, 1)),
                    'improvement_trend': data.get('improvement_trend', 0.5),
                    'defensive_focus': data.get('defensive_focus', 0.7)
                })
            else:
                features.update({
                    'recent_def_form': 0.5,
                    'defensive_consistency': 0.5,
                    'improvement_trend': 0.5,
                    'defensive_focus': 0.7
                })

            return features

        except Exception as e:
            self.logger.error(f" Defensive feature extraction error: {e}")
            # Ensure all features used in calculation methods have a default here
            return {
                'defensive_rating': 0.5,
                'opponent_fg_pct': 0.55,
                'defensive_rebounds_pct': 0.75,
                'steals_per_game': 0.5,
                'blocks_per_game': 0.5,
                'turnovers_forced': 0.6, # Added default
                'fouls_per_game': 0.6, # Added default
                'deflections_per_game': 0.5,
                'charges_drawn': 0.2,
                'loose_balls_recovered': 0.3,
                'defensive_rotations': 0.5,
                'help_defense_rate': 0.6,
                'opponent_three_pct': 0.65,
                'closeout_speed': 0.7,
                'on_ball_defense': 0.6,
                'screen_navigation': 0.6,
                'transition_defense': 0.7,
                'perimeter_defense': 0.7, # Added default
                'paint_protection': 0.7,
                'rim_protection': 0.6,
                'post_defense': 0.6,
                'defensive_positioning': 0.7,
                'weak_side_help': 0.6,
                'communication': 0.7,
                'switching_ability': 0.6,
                'zone_defense_eff': 0.5,
                'man_defense_eff': 0.7,
                'defensive_chemistry': 0.6,
                'defensive_iq': 0.6, # Added default
                'clutch_defense': 0.6,
                'playoff_defense': 0.7,
                'home_defense': 0.65,
                'fatigue_resistance': 0.6,
                'foul_discipline': 0.7,
                'injury_impact': 0.0, # Added default
                'fatigue_level': 0.0, # Added default
                'foul_trouble_risk': 0.3, # Added default
                'back_to_back': 0.0, # Added default
                'key_player_out': 0.0, # Added default
                'road_game': 0.0, # Added default
                'pace_mismatch': 0.0, # Added default
                'size_disadvantage': 0.0, # Added default
                'recent_def_form': 0.5,
                'defensive_consistency': 0.5,
                'improvement_trend': 0.5,
                'defensive_focus': 0.7
            }

    def _calculate_defensive_strength(self, features: Dict[str, float]) -> float:
        """
        Calculate the overall defensive strength based on extracted features
        """
        try:
            # Weight different defensive components
            weights = {
                'defensive_rating': 0.20,
                'opponent_fg_pct': 0.15,
                'defensive_rebounds_pct': 0.12, # Corrected feature name
                'steals_per_game': 0.10,
                'blocks_per_game': 0.10,
                'turnovers_forced': 0.08, # Corrected feature name
                'fouls_per_game': -0.05, # Corrected feature name (normalized inversely, so lower original fouls = higher feature value)
                'defensive_consistency': 0.10,
                'clutch_defense': 0.08,
                'home_defense': 0.05,
                'recent_def_form': 0.07,
                'perimeter_defense': 0.04, # Added weight for this feature
                'rim_protection': 0.05, # Added as part of calculation for clarity
                'defensive_iq': 0.03 # Added as part of calculation for clarity
            }

            # Calculate weighted defensive score
            defensive_score = 0.0
            total_weight = 0.0

            for feature, weight in weights.items():
                if feature in features:
                    defensive_score += features[feature] * weight
                    total_weight += abs(weight) # Use absolute value for normalization

            # Normalize if we have features
            if total_weight > 0:
                defensive_score = defensive_score / total_weight
            else:
                defensive_score = 0.5 # Neutral if no features

            # Injury impact reduces defensive strength
            if 'injury_impact' in features:
                defensive_score -= features['injury_impact'] * 0.1

            # Ensure bounds [0, 1]
            defensive_score = max(0.0, min(1.0, defensive_score))

            return defensive_score

        except Exception as e:
            self.logger.error(f" Error calculating defensive strength: {e}")
            return 0.5 # Return neutral score on error

    def _assess_vulnerabilities(self, features: Dict[str, float]) -> float:
        """
        Assess defensive vulnerabilities and weaknesses
        """
        try:
            # Vulnerability assessment factors
            vulnerability_factors = {
                'perimeter_weakness': 1.0 - features.get('perimeter_defense', 0.7),
                'interior_weakness': 1.0 - features.get('rim_protection', 0.7),
                'rebounding_weakness': 1.0 - features.get('defensive_rebounds_pct', 0.7), # Corrected feature name
                'transition_weakness': 1.0 - features.get('transition_defense', 0.7),
                'clutch_weakness': 1.0 - features.get('clutch_defense', 0.7),
                'injury_vulnerability': features.get('injury_impact', 0.0), # Corrected feature name
                'fatigue_vulnerability': features.get('fatigue_level', 0.0), # Corrected feature name
                'foul_tendency': features.get('foul_trouble_risk', 0.3) # Corrected feature name
            }

            # Weight vulnerability factors
            weights = {
                'perimeter_weakness': 0.18,
                'interior_weakness': 0.17,
                'rebounding_weakness': 0.15,
                'transition_weakness': 0.12,
                'clutch_weakness': 0.10,
                'injury_vulnerability': 0.12,
                'fatigue_vulnerability': 0.08,
                'foul_tendency': 0.08
            }

            # Calculate weighted vulnerability score
            vulnerability_score = 0.0
            for factor, value in vulnerability_factors.items():
                weight = weights.get(factor, 0.0)
                vulnerability_score += value * weight

            # Situational vulnerability modifiers
            if features.get('back_to_back', 0.0) > 0.5:
                vulnerability_score += 0.05 # More vulnerable when tired

            if features.get('key_player_out', 0.0) > 0.5:
                vulnerability_score += 0.08 # More vulnerable without key defenders

            if features.get('road_game', 0.0) > 0.5:
                vulnerability_score += 0.02 # Slightly more vulnerable on road

            # Matchup-specific vulnerabilities
            if features.get('pace_mismatch', 0.0) > 0.6:
                vulnerability_score += 0.04

            if features.get('size_disadvantage', 0.0) > 0.6:
                vulnerability_score += 0.06

            # Ensure bounds [0, 1]
            vulnerability_score = max(0.0, min(1.0, vulnerability_score))

            return vulnerability_score

        except Exception as e:
            self.logger.error(f" Error assessing vulnerabilities: {e}")
            return 0.3 # Return moderate vulnerability on error

    def analyze_threats(self, 
                        game_context: Dict[str, Any], 
                        team_data: Dict[str, Any], 
                        opponent_data: Dict[str, Any]) -> DefensiveInsight:
        """Core defensive threat analysis with real-time adjustments"""

        # --- Extract features for analysis ---
        enhanced_features = self.feature_alchemist.enhance_features({
            "game_context": game_context,
            "team_data": team_data,
            "opponent_data": opponent_data
        }, feature_type="defensive")

        # --- Contextual adjustments based on game situation ---
        if game_context['quarter'] <= 2:
            # Early game: focus on establishing defensive rhythm
            self.defensive_weights['help_defense'] += 0.02
            self.defensive_weights['transition_defense'] += 0.02
        elif game_context['quarter'] == 3:
            # Mid-game: adjust based on first half performance
            if game_context.get('score_differential', 0) < 0:
                # Losing: increase pressure
                self.defensive_weights['perimeter_defense'] += 0.03
                self.defensive_weights['steal_generation'] += 0.02
            else:
                # Winning: maintain lead with solid defense
                self.defensive_weights['help_defense'] += 0.01
                self.defensive_weights['transition_defense'] += 0.01
        else:
            # Late game: focus on preventing fouls and protecting the rim
            self.defensive_weights['fouls_per_game'] -= 0.02
            self.defensive_weights['rim_protection'] += 0.03

        # --- Perform standard defensive analysis ---
        threat_scores = self._calculate_threat_matrix(
            enhanced_features['opponent_tendencies'],
            enhanced_features['player_matchups']
        )
        vulnerabilities = self._identify_vulnerabilities(
            enhanced_features['team_weaknesses'],
            enhanced_features['opponent_strengths']
        )
        risk_score = self._compute_risk_index(
            threat_scores,
            vulnerabilities,
            game_context['game_situation']
        )
        countermeasures = self._generate_countermeasures(
            threat_scores,
            vulnerabilities,
            risk_score,
            game_context
        )

        # --- Rationale for decisions ---
        rationale = self._generate_rationale(threat_scores, vulnerabilities, countermeasures, game_context)

        return DefensiveInsight(
            game_id=game_context['game_id'],
            threat_assessment=threat_scores,
            overall_threat_level=self._determine_threat_level(threat_scores),
            vulnerabilities=vulnerabilities,
            risk_score=risk_score,
            countermeasures=countermeasures,
            confidence=self._calculate_confidence(game_context),
            context_metadata=enhanced_features['metadata'],
            rationale=rationale
        )

    def _generate_rationale(self, threat_scores: Dict[str, float], vulnerabilities: List[str], countermeasures: List[Countermeasure], game_context: Dict[str, Any]) -> str:
        """Generate explanation for chosen threats and countermeasures."""
        if not threat_scores:
            return "No significant threats detected."
        main_threat = max(threat_scores, key=threat_scores.get)
        rationale = f"Primary threat: {main_threat} (score: {threat_scores[main_threat]:.2f}). "
        if vulnerabilities:
            rationale += f"Key vulnerabilities: {', '.join(vulnerabilities)}. "
        if countermeasures:
            rationale += f"Recommended countermeasures: {', '.join([c.pattern.value for c in countermeasures])}."
        return rationale

    def self_learn(self, feedback: Dict[str, Any]):
        """Adaptive learning from feedback with centralized drift detection and War Council broadcast."""
        # Log feedback with timestamp
        self.feedback_log.append({
            "timestamp": datetime.utcnow(),
            "feedback": feedback,
            "pre_adapt_state": self.adaptation_weights.copy()
        })
        # Broadcast feedback to War Council
        self.war_council.broadcast_feedback(
            sender="AresDefenseOracle_Expert",
            feedback=feedback
        )
        # Process different feedback types
        if feedback["type"] == "effectiveness":
            self._adapt_from_effectiveness(feedback["data"])
        elif feedback["type"] == "feature_analysis":
            self._adapt_from_feature_analysis(feedback["data"])
        # Centralized drift detection
        drift_result = unified_temporal_anomaly_detector.detect_drift(self.feedback_log)
        if drift_result.get('drift', False):
            self._trigger_retraining("concept_drift", feedback)
