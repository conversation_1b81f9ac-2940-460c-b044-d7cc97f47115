import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, Depends, Request, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from backend.middleware.feature_flags import UserTier, get_user_tier_from_context
from backend.services.intellectual_property_protection import ip_protection
from backend.services.algorithm_obfuscation import algorithm_obfuscator
from backend.services.license_enforcement import license_enforcement, LicenseType
from backend.services.competitive_advantage_system import ProprietaryFeature
from backend.database.models import UserModel as User


"""
🛡️ HYPER MEDUSA NEURAL VAULT - Intellectual Property Protection Router
======================================================================
API endpoints for managing intellectual property protection, licensing, and algorithm security.

Features:
- License Management
- Algorithm Protection Status
- Security Monitoring
- Violation Tracking
- Competitive Advantage Validation
"""



# Simple user context dependency for IP protection
async def get_user_context(request: Request) -> Dict[str, Any]:
    """Get user context for IP protection endpoints"""
    # For testing/demo purposes, return a default context
    # In production, this would extract from JWT tokens, session, etc.
    return {
        "user_id": "demo_user",
        "user_tier": UserTier.PRO,  # Default to PRO for testing
        "is_admin": False,
        "authenticated": True
    }

# Configure logging
logger = logging.getLogger("hyper_medusa_neural_vault.ip_protection_router")

# Create router
router = APIRouter(
    prefix="/api/ip-protection",
    tags=["Intellectual Property Protection"],
    responses={
        404: {"description": "Not found"},
        403: {"description": "Access denied"},
        401: {"description": "Unauthorized"}
    }
)

# Pydantic models
class LicenseRequest(BaseModel):
    """License generation request"""
    user_id: str = Field(..., description="User identifier")
    license_type: str = Field(..., description="Type of license")
    duration_days: int = Field(365, description="License duration in days")
    features: Optional[List[str]] = Field(None, description="Specific features to enable")
    geographic_restrictions: Optional[List[str]] = Field(None, description="Geographic restrictions")
    concurrent_users: int = Field(1, description="Number of concurrent users allowed")

class AlgorithmAccessRequest(BaseModel):
    """Algorithm access request"""
    algorithm_id: str = Field(..., description="Algorithm identifier")
    context: Optional[Dict[str, Any]] = Field(None, description="Access context")

class SecurityEventQuery(BaseModel):
    """Security event query parameters"""
    start_date: Optional[datetime] = Field(None, description="Start date for events")
    end_date: Optional[datetime] = Field(None, description="End date for events")
    threat_level: Optional[str] = Field(None, description="Threat level filter")
    user_id: Optional[str] = Field(None, description="User ID filter")

@router.get("/status", summary="Get IP Protection System Status")
async def get_ip_protection_status(
    request: Request,
    user_context: Dict[str, Any] = Depends(get_user_context)
):
    """Get comprehensive intellectual property protection system status"""
    try:
        # Check access permissions
        if user_context.get("user_tier") not in [UserTier.ENTERPRISE]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Enterprise tier required for IP protection status"
            )
        
        # Get protection status from all systems
        ip_status = ip_protection.get_protection_status()
        obfuscation_status = algorithm_obfuscator.get_obfuscation_status()
        license_status = license_enforcement.get_enforcement_status()
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "success",
                "timestamp": datetime.now().isoformat(),
                "ip_protection": ip_status,
                "algorithm_obfuscation": obfuscation_status,
                "license_enforcement": license_status,
                "overall_security_level": "MAXIMUM",
                "competitive_advantages_protected": len(ProprietaryFeature),
                "system_integrity": "SECURE"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 IP protection status error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve IP protection status"
        )

@router.post("/license/generate", summary="Generate New License")
async def generate_license(
    license_request: LicenseRequest,
    request: Request,
    user_context: Dict[str, Any] = Depends(get_user_context)
):
    """Generate a new license for a user"""
    try:
        # Check admin permissions
        if user_context.get("user_tier") != UserTier.ENTERPRISE or not user_context.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin privileges required for license generation"
            )
        
        # Validate license type
        try:
            license_type = LicenseType(license_request.license_type.lower())
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid license type: {license_request.license_type}"
            )
        
        # Generate license
        license_token = license_enforcement.generate_license(
            user_id=license_request.user_id,
            license_type=license_type,
            duration_days=license_request.duration_days,
            geographic_restrictions=license_request.geographic_restrictions or [],
            concurrent_users=license_request.concurrent_users,
            metadata={
                "generated_by": user_context.get("user_id"),
                "generation_timestamp": datetime.now().isoformat(),
                "custom_features": license_request.features
            }
        )
        
        return JSONResponse(
            status_code=status.HTTP_201_CREATED,
            content={
                "status": "success",
                "message": "License generated successfully",
                "license_token": license_token,
                "license_type": license_type.value,
                "duration_days": license_request.duration_days,
                "user_id": license_request.user_id,
                "generated_at": datetime.now().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 License generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate license"
        )

@router.post("/license/validate", summary="Validate License")
async def validate_license(
    license_token: str,
    feature: Optional[str] = None,
    request: Request = None
):
    """Validate a license token and check feature access"""
    try:
        # Validate license
        valid, reason, license_data = license_enforcement.validate_license(license_token, feature)
        
        if not valid:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={
                    "status": "invalid",
                    "reason": reason,
                    "valid": False,
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "valid",
                "valid": True,
                "license_data": license_data,
                "feature_access": feature is None or feature in license_data.get("features_enabled", []),
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"🚨 License validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to validate license"
        )

@router.post("/algorithm/access", summary="Request Algorithm Access")
async def request_algorithm_access(
    access_request: AlgorithmAccessRequest,
    request: Request,
    user_context: Dict[str, Any] = Depends(get_user_context)
):
    """Request access to a proprietary algorithm"""
    try:
        user_id = user_context.get("user_id")
        user_tier = user_context.get("user_tier")
        
        if not user_id or not user_tier:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User authentication required"
            )
        
        # Request algorithm access
        access_granted, reason, protected_data = ip_protection.protect_algorithm_access(
            algorithm_id=access_request.algorithm_id,
            user_id=user_id,
            user_tier=user_tier,
            context=access_request.context or {}
        )
        
        if not access_granted:
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content={
                    "status": "access_denied",
                    "reason": reason,
                    "algorithm_id": access_request.algorithm_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "access_granted",
                "algorithm_id": access_request.algorithm_id,
                "protected_data": protected_data,
                "access_level": user_tier.value if user_tier else "unknown",
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Algorithm access error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process algorithm access request"
        )

@router.get("/security/events", summary="Get Security Events")
async def get_security_events(
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    threat_level: Optional[str] = None,
    user_id: Optional[str] = None,
    limit: int = 100,
    request: Request = None,
    user_context: Dict[str, Any] = Depends(get_user_context)
):
    """Get security events and violations"""
    try:
        # Check admin permissions
        if user_context.get("user_tier") != UserTier.ENTERPRISE or not user_context.get("is_admin", False):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin privileges required for security events"
            )
        
        # Get security events from IP protection system
        all_events = ip_protection.security_events
        
        # Apply filters
        filtered_events = all_events
        
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            filtered_events = [e for e in filtered_events if e.timestamp >= start_dt]
        
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            filtered_events = [e for e in filtered_events if e.timestamp <= end_dt]
        
        if threat_level:
            filtered_events = [e for e in filtered_events if e.threat_level.value == threat_level.lower()]
        
        if user_id:
            filtered_events = [e for e in filtered_events if e.user_id == user_id]
        
        # Limit results
        filtered_events = filtered_events[:limit]
        
        # Convert to serializable format
        events_data = []
        for event in filtered_events:
            events_data.append({
                "event_id": event.event_id,
                "event_type": event.event_type,
                "threat_level": event.threat_level.value,
                "timestamp": event.timestamp.isoformat(),
                "user_id": event.user_id,
                "algorithm_accessed": event.algorithm_accessed,
                "anomaly_score": event.anomaly_score,
                "details": event.details,
                "resolved": event.resolved
            })
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "success",
                "total_events": len(all_events),
                "filtered_events": len(events_data),
                "events": events_data,
                "filters_applied": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "threat_level": threat_level,
                    "user_id": user_id,
                    "limit": limit
                },
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Security events retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security events"
        )

@router.get("/competitive-advantages", summary="Get Competitive Advantages")
async def get_competitive_advantages(
    request: Request,
    user_context: Dict[str, Any] = Depends(get_user_context)
):
    """Get list of competitive advantages and proprietary features"""
    try:
        user_tier = user_context.get("user_tier")
        
        if not user_tier or user_tier == UserTier.FREE:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Paid tier required for competitive advantage information"
            )
        
        # Get available features based on user tier
        available_features = []
        
        for feature in ProprietaryFeature:
            # Check if user has access to this feature
            access_granted, _, _ = ip_protection.protect_algorithm_access(
                algorithm_id=feature.value,
                user_id=user_context.get("user_id", "anonymous"),
                user_tier=user_tier,
                context={"check_only": True}
            )
            
            feature_info = {
                "feature_id": feature.value,
                "name": feature.value.replace("_", " ").title(),
                "accessible": access_granted,
                "tier_required": "PRO" if feature.value in ["quantum_prediction_matrix", "cognitive_cortex_integration"] else "ENTERPRISE"
            }
            
            available_features.append(feature_info)
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": "success",
                "user_tier": user_tier.value,
                "total_features": len(ProprietaryFeature),
                "accessible_features": len([f for f in available_features if f["accessible"]]),
                "features": available_features,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"🚨 Competitive advantages retrieval error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve competitive advantages"
        )

@router.get("/health", summary="IP Protection System Health Check")
async def health_check():
    """Health check for IP protection system"""
    try:
        # Check system components
        ip_status = ip_protection.get_protection_status()
        obfuscation_status = algorithm_obfuscator.get_obfuscation_status()
        license_status = license_enforcement.get_enforcement_status()
        
        # Determine overall health
        overall_health = "healthy"
        if ip_status.get("system_status") != "SECURE":
            overall_health = "degraded"
        if license_status.get("system_status") != "ENFORCING":
            overall_health = "degraded"
        
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "status": overall_health,
                "timestamp": datetime.now().isoformat(),
                "components": {
                    "ip_protection": ip_status.get("system_status", "unknown"),
                    "algorithm_obfuscation": obfuscation_status.get("system_status", "unknown"),
                    "license_enforcement": license_status.get("system_status", "unknown")
                },
                "metrics": {
                    "protected_algorithms": ip_status.get("total_protected_algorithms", 0),
                    "active_licenses": license_status.get("total_active_licenses", 0),
                    "security_events_24h": ip_status.get("security_events_24h", 0)
                }
            }
        )
        
    except Exception as e:
        logger.error(f"🚨 IP protection health check error: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
        )
