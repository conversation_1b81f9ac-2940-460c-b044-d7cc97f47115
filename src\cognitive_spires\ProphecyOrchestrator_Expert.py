import logging
import asyncio
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import warnings
from src.features.feature_feedback import FeatureFeedback
import sqlite3

try:
    from src.features.feature_alchemist import SelfLearningFeatureAlchemist
    FEATURE_ALCHEMIST_AVAILABLE = True
except ImportError:
    FEATURE_ALCHEMIST_AVAILABLE = False
    SelfLearningFeatureAlchemist = None

# Optional import to avoid circular dependency
try:
    pass  # Add specific imports here if needed
except ImportError:
    # Fallback for circular import
    pass

"""
ProphecyOrchestrator_Expert.py
==============================

Expert-level prophecy orchestration and multi-game prediction coordination.
Manages complex prediction workflows, tournament scenarios, and 
multi-dimensional HYPER MEDUSA NEURAL VAULT predictions strategy orchestration.

Author: Cognitive Spires Expert System
"""


warnings.filterwarnings('ignore')

# Configure logging
logger = logging.getLogger(__name__)


@dataclass
class ProphecyTask:
    """Individual prophecy task in the orchestration"""
    task_id: str
    task_type: str # 'game_prediction', 'player_prop', 'parlay', 'tournament'
    priority: int # 1-10, higher is more urgent
    input_data: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    status: str = 'pending' # pending, running, completed, failed
    result: Optional[Dict[str, Any]] = None
    start_time: Optional[datetime] = None
    completion_time: Optional[datetime] = None
    processing_time: float = 0.0


@dataclass
class OrchestrationPlan:
    """Complete orchestration execution plan"""
    plan_id: str
    total_tasks: int
    execution_stages: List[List[str]] # List of task groups to execute in parallel
    estimated_duration: float
    resource_requirements: Dict[str, int]
    success_criteria: Dict[str, float]


@dataclass
class ProphecyResults:
    """Aggregated results from prophecy orchestration"""
    orchestration_id: str
    completed_tasks: int
    failed_tasks: int
    total_predictions: int
    overall_confidence: float
    execution_time: float
    performance_metrics: Dict[str, float]
    predictions_by_type: Dict[str, List[Dict]]
    risk_assessment: Dict[str, float]


class ProphecyOrchestrator_Expert:
    """
    Expert-level prophecy orchestration system for complex NBA prediction workflows.
    
    Features:
    - Multi-game tournament prediction coordination
    - Parallel task execution with dependency management
    - Dynamic resource allocation and load balancing
    - Complex parlay and combination bet orchestration
    - Real-time performance monitoring and optimization
    - Advanced error recovery and fallback strategies
    """
    
    def __init__(self, max_workers: int = 8, enable_parallel_execution: bool = True, max_models: bool = True, **kwargs):
        self.max_workers = max_workers
        self.enable_parallel_execution = enable_parallel_execution
        self.active_orchestrations = {}
        self.completed_orchestrations = {}
        self.performance_history = []
        self.resource_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        # Task type handlers
        self.task_handlers = {
            'game_prediction': self._handle_game_prediction,
            'player_prop': self._handle_player_prop,
            'parlay': self._handle_parlay,
            'tournament': self._handle_tournament,
            'live_update': self._handle_live_update,
            'risk_analysis': self._handle_risk_analysis
        }
        
        logger.info(f" ProphecyOrchestrator_Expert initialized with {max_workers} workers")
        self.feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)
    
    def predict(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main prediction interface for prophecy orchestration.
        
        Args:
            input_data: Dictionary containing orchestration requirements
            
        Returns:
            Dictionary with orchestration results and performance metrics
        """
        try:
            start_time = datetime.now()
            
            # Extract orchestration parameters
            orchestration_type = input_data.get('orchestration_type', 'multi_game')
            games_data = input_data.get('games_data', [])
            prediction_types = input_data.get('prediction_types', ['game_prediction'])
            priority_level = input_data.get('priority_level', 5)
            
            # Create orchestration plan
            orchestration_id = f"orch_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{np.random.randint(1000, 9999)}"
            
            # Generate tasks based on input
            # Call _get_real_games_data synchronously here as it's not async anymore
            actual_games_data = games_data if games_data else self._get_real_games_data() 
            tasks = self._generate_prophecy_tasks(actual_games_data, prediction_types, priority_level)
            
            # Create execution plan
            execution_plan = self._create_execution_plan(tasks, orchestration_id)
            
            # Execute orchestration
            raw_execution_results = self._execute_orchestration(execution_plan, tasks)
            
            # Aggregate and analyze results
            final_results = self._aggregate_results(raw_execution_results, orchestration_id)
            
            # Generate additional summaries
            task_breakdown = self._generate_task_breakdown(tasks)
            performance_summary = self._generate_performance_summary(raw_execution_results)
            recommendations = self._generate_recommendations(final_results)
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Return a comprehensive dictionary to match the type hint
            result = {
                'orchestration_results': final_results.__dict__,
                'execution_plan': execution_plan.__dict__,
                'task_breakdown': task_breakdown,
                'performance_summary': performance_summary,
                'recommendations': recommendations,
                'processing_time_seconds': processing_time,
                'orchestration_timestamp': datetime.now().isoformat(),
                'orchestrator_version': '2.0_expert'
            }
            confidence = final_results.confidence if hasattr(final_results, 'confidence') else 1.0
            # --- Feedback wiring: send feedback if confidence is low ---
            if confidence < 0.3:
                feedback = FeatureFeedback(self.__class__.__name__, input_data, confidence, message="Low confidence. Requesting feature improvement.")
                self.feature_alchemist.receive_feedback(feedback)
            return result
        except Exception as e:
            logger.error(f" ProphecyOrchestrator prediction failed: {e}")
            return self._get_fallback_orchestration() # Return comprehensive fallback
    
    def _generate_prophecy_tasks(self, games_data: List[Dict], 
                                 prediction_types: List[str], 
                                 priority_level: int) -> List[ProphecyTask]:
        """Generate prophecy tasks based on input requirements"""
        tasks = []
        task_counter = 0
        
        try:
            for game_idx, game in enumerate(games_data):
                titan_clash_id = game.get('titan_clash_id', f'game_{game_idx}')
                
                # Generate tasks for each prediction type
                for pred_type in prediction_types:
                    task_counter += 1
                    task_id = f"task_{task_counter}_{pred_type}_{titan_clash_id}"
                    
                    # Create task with appropriate priority
                    task_priority = priority_level
                    if pred_type == 'live_update':
                        task_priority += 2 # Higher priority for live updates
                    elif pred_type == 'tournament':
                        task_priority += 1 # Tournament analysis is important
                    
                    task = ProphecyTask(
                        task_id=task_id,
                        task_type=pred_type,
                        priority=min(10, task_priority),
                        input_data={
                            'game_data': game,
                            'prediction_requirements': {
                                'include_props': pred_type in ['player_prop', 'parlay'],
                                'include_live': pred_type == 'live_update',
                                'tournament_context': pred_type == 'tournament'
                            }
                        }
                    )
                    tasks.append(task)
                
                # Add dependency-based tasks
                if 'parlay' in prediction_types:
                    # Parlay tasks depend on individual game predictions
                    parlay_task_id = f"parlay_{titan_clash_id}_combo"
                    parlay_dependencies = [
                        t.task_id for t in tasks 
                        if t.input_data['game_data']['titan_clash_id'] == titan_clash_id and 
                        t.task_type in ['game_prediction', 'player_prop']
                    ]
                    
                    parlay_task = ProphecyTask(
                        task_id=parlay_task_id,
                        task_type='parlay',
                        priority=priority_level + 1,
                        input_data={'game_data': game, 'combination_type': 'multi_bet'},
                        dependencies=parlay_dependencies
                    )
                    tasks.append(parlay_task)
            
            # Add cross-game analysis tasks if multiple games exist
            if len(games_data) > 1 and 'tournament' in prediction_types:
                cross_analysis_task = ProphecyTask(
                    task_id="cross_game_analysis",
                    task_type='tournament',
                    priority=priority_level,
                    input_data={
                        'games_data': games_data,
                        'analysis_type': 'cross_correlation'
                    },
                    dependencies=[t.task_id for t in tasks if t.task_type == 'game_prediction']
                )
                tasks.append(cross_analysis_task)
            
            return tasks
        
        except Exception as e:
            logger.error(f" Task generation failed: {e}")
            return self._get_fallback_tasks()
    
    def _create_execution_plan(self, tasks: List[ProphecyTask], 
                               orchestration_id: str) -> OrchestrationPlan:
        """Create optimized execution plan for tasks"""
        try:
            # Analyze task dependencies to create execution stages
            execution_stages = self._analyze_task_dependencies(tasks)
            
            # Estimate resource requirements
            resource_requirements = {
                'cpu_cores': min(self.max_workers, len(tasks)),
                'memory_mb': len(tasks) * 50, # Estimate 50MB per task
                'network_bandwidth': len(tasks) * 10 # Estimate 10 units per task
            }
            
            # Estimate total duration
            estimated_duration = self._estimate_execution_duration(tasks, execution_stages)
            
            # Define success criteria
            success_criteria = {
                'minimum_completion_rate': 0.8,
                'maximum_error_rate': 0.2,
                'target_confidence': 0.7,
                'maximum_execution_time': estimated_duration * 1.5
            }
            
            return OrchestrationPlan(
                plan_id=f"plan_{orchestration_id}",
                total_tasks=len(tasks),
                execution_stages=execution_stages,
                estimated_duration=estimated_duration,
                resource_requirements=resource_requirements,
                success_criteria=success_criteria
            )
        
        except Exception as e:
            logger.error(f" Execution plan creation failed: {e}")
            return self._get_fallback_execution_plan(len(tasks), orchestration_id)
    
    def _analyze_task_dependencies(self, tasks: List[ProphecyTask]) -> List[List[str]]:
        """Analyze task dependencies to create execution stages"""
        stages = []
        completed_tasks = set()
        remaining_tasks = {task.task_id: task for task in tasks}
        
        while remaining_tasks:
            # Find tasks with no unmet dependencies
            ready_tasks = []
            for task_id, task in remaining_tasks.items():
                if all(dep in completed_tasks for dep in task.dependencies):
                    ready_tasks.append(task_id)
            
            if not ready_tasks:
                # Break circular dependencies by including all remaining tasks
                ready_tasks = list(remaining_tasks.keys())
                logger.warning(" TITAN WARNING: Circular dependencies detected, breaking with parallel execution")
            
            stages.append(ready_tasks)
            
            # Move ready tasks to completed
            for task_id in ready_tasks:
                completed_tasks.add(task_id)
                del remaining_tasks[task_id]
        
        return stages
    
    def _estimate_execution_duration(self, tasks: List[ProphecyTask], 
                                     stages: List[List[str]]) -> float:
        """Estimate total execution duration"""
        # Base time estimates per task type (in seconds)
        task_durations = {
            'game_prediction': 3.0,
            'player_prop': 2.0,
            'parlay': 4.0,
            'tournament': 6.0,
            'live_update': 1.5,
            'risk_analysis': 2.5
        }
        
        total_duration = 0.0
        
        # Calculate duration for each stage (bottleneck task)
        for stage in stages:
            stage_tasks = [t for t in tasks if t.task_id in stage]
            if stage_tasks:
                # Max duration in stage (bottleneck task)
                stage_duration = max(
                    task_durations.get(task.task_type, 3.0) 
                    for task in stage_tasks
                )
                total_duration += stage_duration
        
        # Add overhead (20% buffer)
        return total_duration * 1.2
    
    def _execute_orchestration(self, plan: OrchestrationPlan, 
                               tasks: List[ProphecyTask]) -> Dict[str, Any]:
        """Execute the orchestration plan"""
        results = {
            'completed_tasks': [],
            'failed_tasks': [],
            'execution_metrics': {
                'start_time': datetime.now(),
                'stages_completed': 0,
                'total_stages': len(plan.execution_stages)
            }
        }
        
        try:
            task_dict = {task.task_id: task for task in tasks}
            
            # Execute each stage
            for stage_idx, stage_task_ids in enumerate(plan.execution_stages):
                
                stage_tasks = [task_dict[task_id] for task_id in stage_task_ids if task_id in task_dict]
                
                if self.enable_parallel_execution and len(stage_tasks) > 1:
                    # Execute stage tasks in parallel
                    stage_results = self._execute_stage_parallel(stage_tasks)
                else:
                    # Execute stage tasks sequentially
                    stage_results = self._execute_stage_sequential(stage_tasks)
                
                # Process stage results
                for task_result in stage_results:
                    if task_result['status'] == 'completed':
                        results['completed_tasks'].append(task_result)
                    else:
                        results['failed_tasks'].append(task_result)
                
                results['execution_metrics']['stages_completed'] = stage_idx + 1
            
            results['execution_metrics']['end_time'] = datetime.now()
            results['execution_metrics']['total_duration'] = (
                results['execution_metrics']['end_time'] - 
                results['execution_metrics']['start_time']
            ).total_seconds()
            
            logger.info(f" Orchestration completed: {len(results['completed_tasks'])} success, {len(results['failed_tasks'])} failed")
        
        except Exception as e:
            logger.error(f" Orchestration execution failed: {e}")
            results['execution_error'] = str(e)
        
        return results
    
    def _execute_stage_parallel(self, stage_tasks: List[ProphecyTask]) -> List[Dict[str, Any]]:
        """Execute stage tasks in parallel"""
        results = []
        
        # Submit all tasks to thread pool
        future_to_task = {
            self.resource_pool.submit(self._execute_single_task, task): task 
            for task in stage_tasks
        }
        
        # Collect results as they complete
        for future in as_completed(future_to_task):
            task = future_to_task[future]
            try:
                result = future.result(timeout=30.0) # 30 second timeout per task
                results.append(result)
            except Exception as e:
                logger.error(f" Task {task.task_id} failed: {e}")
                results.append({
                    'task_id': task.task_id,
                    'status': 'failed',
                    'error': str(e),
                    'processing_time': 0.0
                })
        
        return results
    
    def _execute_stage_sequential(self, stage_tasks: List[ProphecyTask]) -> List[Dict[str, Any]]:
        """Execute stage tasks sequentially"""
        results = []
        
        for task in stage_tasks:
            try:
                result = self._execute_single_task(task)
                results.append(result)
            except Exception as e:
                logger.error(f" Task {task.task_id} failed: {e}")
                results.append({
                    'task_id': task.task_id,
                    'status': 'failed',
                    'error': str(e),
                    'processing_time': 0.0
                })
        
        return results
    
    def _execute_single_task(self, task: ProphecyTask) -> Dict[str, Any]:
        """Execute a single prophecy task"""
        start_time = datetime.now()
        task.start_time = start_time
        task.status = 'running'
        
        try:
            # Get appropriate handler for task type
            handler = self.task_handlers.get(task.task_type, self._handle_generic_task)
            
            # Execute task
            prediction_result = handler(task.input_data)
            
            task.completion_time = datetime.now()
            task.processing_time = (task.completion_time - start_time).total_seconds()
            task.status = 'completed'
            task.result = prediction_result
            
            return {
                'task_id': task.task_id,
                'task_type': task.task_type,
                'status': 'completed',
                'result': prediction_result,
                'processing_time': task.processing_time,
                'confidence': prediction_result.get('confidence', 0.5)
            }
        
        except Exception as e:
            task.status = 'failed'
            task.completion_time = datetime.now()
            task.processing_time = (task.completion_time - start_time).total_seconds()
            
            logger.error(f" Task {task.task_id} execution failed: {e}")
            raise e
    
    def _handle_game_prediction(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle game prediction task"""
        game_data = input_data.get('game_data', {})
        
        # Mock sophisticated game prediction
        home_team = game_data.get('home_team', 'Home')
        away_team = game_data.get('away_team', 'Away')
        
        # Simulate complex analysis
        base_probability = 0.5 + np.random.uniform(-0.2, 0.2)
        spread_prediction = np.random.uniform(-12, 12)
        total_prediction = np.random.uniform(200, 250)
        
        return {
            'prediction_type': 'game_prediction',
            'home_team': home_team,
            'away_team': away_team,
            'win_probability': max(0.1, min(0.9, base_probability)),
            'spread_prediction': spread_prediction,
            'total_prediction': total_prediction,
            'confidence': np.random.uniform(0.6, 0.9),
            'key_factors': [
                'Team form analysis',
                'Head-to-head record',
                'Injury reports',
                'Home court advantage'
            ]
        }
    
    def _handle_player_prop(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle player prop prediction task"""
        game_data = input_data.get('game_data', {})
        
        # Mock player prop analysis
        props = []
        players = ['Star_Player_1', 'Star_Player_2', 'Role_Player_1']
        
        for player in players:
            prop = {
                'player': player,
                'prop_type': np.random.choice(['points', 'assists', 'rebounds']),
                'line': np.random.uniform(15, 30),
                'over_probability': np.random.uniform(0.4, 0.7),
                'confidence': np.random.uniform(0.5, 0.8)
            }
            props.append(prop)
        
        return {
            'prediction_type': 'player_props',
            'titan_clash_id': game_data.get('titan_clash_id', 'unknown'),
            'player_props': props,
            'confidence': np.mean([p['confidence'] for p in props]),
            'top_opportunities': sorted(props, key=lambda p: p['confidence'], reverse=True)[:2]
        }
    
    def _handle_parlay(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle parlay prediction task"""
        game_data = input_data.get('game_data', {})
        
        # Mock parlay analysis
        individual_probs = [np.random.uniform(0.5, 0.8) for _ in range(3)]
        combined_probability = np.prod(individual_probs)
        
        return {
            'prediction_type': 'parlay',
            'titan_clash_id': game_data.get('titan_clash_id', 'unknown'),
            'individual_probabilities': individual_probs,
            'combined_probability': combined_probability,
            'expected_payout': 1.0 / combined_probability if combined_probability > 0 else 10.0,
            'confidence': np.random.uniform(0.4, 0.7),
            'risk_level': 'high' if combined_probability < 0.3 else 'medium'
        }
    
    def _handle_tournament(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle tournament analysis task"""
        games_data = input_data.get('games_data', [])
        
        # Mock tournament analysis
        return {
            'prediction_type': 'tournament',
            'total_games': len(games_data),
            'tournament_winner_probability': {
                'Team_A': 0.35,
                'Team_B': 0.28,
                'Team_C': 0.22,
                'Team_D': 0.15
            },
            'bracket_confidence': np.random.uniform(0.6, 0.8),
            'upset_potential': np.random.uniform(0.2, 0.5),
            'key_matchups': ['Game_1_vs_Game_2', 'Semifinal_1']
        }
    
    def _handle_live_update(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle live update task"""
        game_data = input_data.get('game_data', {})
        
        return {
            'prediction_type': 'live_update',
            'titan_clash_id': game_data.get('titan_clash_id', 'unknown'),
            'live_probability': np.random.uniform(0.3, 0.8),
            'momentum_shift': np.random.uniform(-0.2, 0.2),
            'confidence': np.random.uniform(0.7, 0.9),
            'update_timestamp': datetime.now().isoformat()
        }
    
    def _handle_risk_analysis(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle risk analysis task"""
        return {
            'prediction_type': 'risk_analysis',
            'overall_risk': np.random.uniform(0.3, 0.7),
            'risk_factors': [
                'Market volatility',
                'Injury uncertainty',
                'Weather conditions'
            ],
            'confidence': np.random.uniform(0.6, 0.8),
            'recommended_position_size': np.random.choice(['conservative', 'moderate', 'aggressive'])
        }
    
    def _handle_generic_task(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle generic/unknown task types"""
        return {
            'prediction_type': 'generic',
            'result': 'completed',
            'confidence': 0.5,
            'processing_note': 'Generic handler used'
        }
    
    def _aggregate_results(self, execution_results: Dict[str, Any], 
                           orchestration_id: str) -> ProphecyResults:
        """Aggregate execution results into final prophecy results"""
        completed_tasks = execution_results.get('completed_tasks', [])
        failed_tasks = execution_results.get('failed_tasks', [])
        
        # Calculate metrics
        total_predictions = len(completed_tasks)
        overall_confidence = np.mean([
            task.get('confidence', 0.5) for task in completed_tasks
        ]) if completed_tasks else 0.0
        
        execution_time = execution_results.get('execution_metrics', {}).get('total_duration', 0.0)
        
        # Group predictions by type
        predictions_by_type = {}
        for task in completed_tasks:
            pred_type = task.get('task_type', 'unknown')
            if pred_type not in predictions_by_type:
                predictions_by_type[pred_type] = []
            predictions_by_type[pred_type].append(task)
        
        # Calculate performance metrics
        performance_metrics = {
            'completion_rate': len(completed_tasks) / (len(completed_tasks) + len(failed_tasks)) if (completed_tasks or failed_tasks) else 0.0,
            'average_confidence': overall_confidence,
            'average_processing_time': np.mean([
                task.get('processing_time', 0.0) for task in completed_tasks
            ]) if completed_tasks else 0.0,
            'total_execution_time': execution_time
        }
        
        # Assess risks
        risk_assessment = {
            'prediction_variance': np.std([task.get('confidence', 0.5) for task in completed_tasks]) if len(completed_tasks) > 1 else 0.0,
            'failure_rate': len(failed_tasks) / (len(completed_tasks) + len(failed_tasks)) if (completed_tasks or failed_tasks) else 0.0,
            'processing_efficiency': 1.0 - (execution_time / max(len(completed_tasks) * 3.0, 1.0)) # Efficiency vs baseline
        }
        
        return ProphecyResults(
            orchestration_id=orchestration_id,
            completed_tasks=len(completed_tasks),
            failed_tasks=len(failed_tasks),
            total_predictions=total_predictions,
            overall_confidence=overall_confidence,
            execution_time=execution_time,
            performance_metrics=performance_metrics,
            predictions_by_type=predictions_by_type,
            risk_assessment=risk_assessment
        )
    
    def _generate_task_breakdown(self, tasks: List[ProphecyTask]) -> Dict[str, Any]:
        """Generate detailed task breakdown analysis"""
        return {
            'total_tasks': len(tasks),
            'tasks_by_type': {
                task_type: len([t for t in tasks if t.task_type == task_type])
                for task_type in set(t.task_type for t in tasks)
            },
            'priority_distribution': {
                'high': len([t for t in tasks if t.priority >= 8]),
                'medium': len([t for t in tasks if 5 <= t.priority < 8]),
                'low': len([t for t in tasks if t.priority < 5])
            },
            'dependencies_count': sum(len(t.dependencies) for t in tasks),
            'average_priority': np.mean([t.priority for t in tasks])
        }
    
    def _generate_performance_summary(self, execution_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate performance summary"""
        completed = execution_results.get('completed_tasks', [])
        failed = execution_results.get('failed_tasks', [])
        
        return {
            'success_rate': len(completed) / (len(completed) + len(failed)) if (completed or failed) else 0.0,
            'average_task_time': np.mean([
                task.get('processing_time', 0.0) for task in completed
            ]) if completed else 0.0,
            'fastest_task': min([
                task.get('processing_time', float('inf')) for task in completed
            ], default=0.0),
            'slowest_task': max([
                task.get('processing_time', 0.0) for task in completed
            ], default=0.0),
            'confidence_distribution': {
                'high': len([t for t in completed if t.get('confidence', 0) >= 0.8]),
                'medium': len([t for t in completed if 0.6 <= t.get('confidence', 0) < 0.8]),
                'low': len([t for t in completed if t.get('confidence', 0) < 0.6])
            }
        }
    
    def _generate_recommendations(self, results: ProphecyResults) -> List[str]:
        """Generate recommendations based on orchestration results"""
        recommendations = []
        
        # Performance-based recommendations
        if results.performance_metrics['completion_rate'] < 0.8:
            recommendations.append("🔧 Consider increasing resource allocation for better completion rates")
        
        if results.overall_confidence < 0.6:
            recommendations.append(" Low overall confidence - review prediction models")
        
        if results.execution_time > 30.0:
            recommendations.append(" Long execution time - optimize parallel processing")
        
        # Risk-based recommendations
        if results.risk_assessment['failure_rate'] > 0.2:
            recommendations.append(" High failure rate detected - implement better error handling")
        
        if results.risk_assessment['prediction_variance'] > 0.3:
            recommendations.append(" High prediction variance - review model consistency")
        # Opportunity recommendations
        if results.total_predictions > 10:
            recommendations.append(" Large prediction set - consider ensemble optimization")
        
        if not recommendations:
            recommendations.append(" Orchestration performed well - maintain current configuration")
        
        return recommendations
    
    def _get_real_games_data(self) -> List[Dict[str, Any]]:
        """Get real games data from database instead of mock data (now synchronous)"""
        try:
            
            # Connect to database and get real upcoming/recent games
            conn = sqlite3.connect('medusa_vault.db')
            cursor = conn.cursor()
            
            # Query for recent and upcoming games
            cursor.execute("""
                SELECT home_team, away_team, home_score, away_score, game_date, season
                FROM nba_games 
                WHERE game_date >= date('now', '-7 days') OR game_date <= date('now', '+7 days')
                ORDER BY game_date DESC
                LIMIT 20
            """)
            
            games = cursor.fetchall()
            conn.close()
            
            # Convert to games data format
            games_data = []
            for home_team, away_team, home_score, away_score, game_date, season in games:
                game_data = {
                    'titan_clash_id': f"{home_team}_{away_team}_{game_date}",
                    'home_team': home_team,
                    'away_team': away_team,
                    'game_date': game_date,
                    'season': season or '2023-24',
                    'is_completed': home_score is not None,
                    'home_score': home_score,
                    'away_score': away_score,
                    'venue': f"{home_team} Arena", # Would get real venue data
                    'importance': 'regular', # Would calculate based on standings
                    'data_source': 'real_database'
                }
                games_data.append(game_data)
            
            # If no real data, return basic template
            if not games_data:
                games_data = [{
                    'titan_clash_id': 'default_game_1',
                    'home_team': 'LAL',
                    'away_team': 'GSW',
                    'game_date': '2024-01-15',
                    'season': '2023-24',
                    'is_completed': False,
                    'home_score': None,
                    'away_score': None,
                    'venue': 'Default Arena',
                    'importance': 'regular',
                    'data_source': 'fallback'
                }]
            
            return games_data
        
        except Exception as e:
            logger.warning(f" TITAN PROCESSING FAILED: get real games data: {e}")
            # Return basic fallback
            return [{
                'titan_clash_id': 'error_fallback',
                'home_team': 'HOME',
                'away_team': 'AWAY',
                'game_date': '2024-01-01',
                'season': '2023-24',
                'is_completed': False,
                'home_score': None,
                'away_score': None,
                'venue': 'Error Arena',
                'importance': 'regular',
                'data_source': 'error_fallback'
            }]
    
    def _get_fallback_tasks(self) -> List[ProphecyTask]:
        """Generate fallback tasks when task generation fails"""
        return [
            ProphecyTask(
                task_id="fallback_task_1",
                task_type='game_prediction',
                priority=5,
                input_data={'game_data': {'titan_clash_id': 'fallback_game'}}
            )
        ]
    
    def _get_fallback_execution_plan(self, num_tasks: int, orchestration_id: str) -> OrchestrationPlan:
        """Generate fallback execution plan"""
        return OrchestrationPlan(
            plan_id=f"fallback_plan_{orchestration_id}",
            total_tasks=num_tasks,
            execution_stages=[['fallback_task_1']],
            estimated_duration=5.0,
            resource_requirements={'cpu_cores': 1, 'memory_mb': 100, 'network_bandwidth': 10},
            success_criteria={'minimum_completion_rate': 0.5, 'maximum_error_rate': 0.5, 'target_confidence': 0.5, 'maximum_execution_time': 10.0}
        )
    
    def _get_fallback_orchestration(self) -> Dict[str, Any]:
        """Fallback orchestration when main prediction fails - now returns structured dict"""
        fallback_results = ProphecyResults(
            orchestration_id='fallback_orchestration',
            completed_tasks=0,
            failed_tasks=1,
            total_predictions=0,
            overall_confidence=0.3,
            execution_time=0.1,
            performance_metrics={'completion_rate': 0.0, 'average_confidence': 0.0, 'average_processing_time': 0.0, 'total_execution_time': 0.0},
            predictions_by_type={},
            risk_assessment={'prediction_variance': 0.5, 'failure_rate': 1.0, 'processing_efficiency': 0.0}
        )
        fallback_plan = self._get_fallback_execution_plan(0, 'fallback_plan_id')
        
        return {
            'orchestration_results': fallback_results.__dict__,
            'execution_plan': fallback_plan.__dict__,
            'task_breakdown': {'total_tasks': 0, 'tasks_by_type': {}, 'priority_distribution': {}, 'dependencies_count': 0, 'average_priority': 0.0},
            'performance_summary': {'success_rate': 0.0, 'average_task_time': 0.0, 'fastest_task': 0.0, 'slowest_task': 0.0, 'confidence_distribution': {'high': 0, 'medium': 0, 'low': 0}},
            'recommendations': ['🚨 Orchestration failed - check system status'],
            'processing_time_seconds': 0.01,
            'orchestration_timestamp': datetime.now().isoformat(),
            'orchestrator_version': '2.0_expert_fallback'
        }
    
    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """
        Self-learning and feedback-driven adaptation for ProphecyOrchestrator_Expert.
        Adjusts orchestration logic, task priorities, or other parameters based on feedback from the War Council, spires, or system performance.
        """
        if feedback:
            logger.info(f"[ProphecyOrchestrator_Expert] Received feedback: {feedback}")
            # Example: Adjust task priorities or execution strategies
            if 'priority_updates' in feedback:
                for task_id, priority in feedback['priority_updates'].items():
                    for task in self.active_tasks:
                        if task.task_id == task_id:
                            task.priority = priority
            # Log feedback for meta-learning
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)


# Compatibility functions for legacy integration
def orchestrate_prophecies(prophecy_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Legacy compatibility function"""
    orchestrator = ProphecyOrchestrator_Expert()
    mock_input = {
        'orchestration_type': 'multi_game',
        'games_data': prophecy_data,
        'prediction_types': ['game_prediction', 'player_prop'],
        'priority_level': 6
    }
    return orchestrator.predict(mock_input)


if __name__ == "__main__":
    # Test the expert orchestrator
    orchestrator = ProphecyOrchestrator_Expert()
    
    test_data = {
        'orchestration_type': 'tournament',
        'games_data': [
            {'titan_clash_id': 'test_game_1', 'home_team': 'Lakers', 'away_team': 'Warriors'},
            {'titan_clash_id': 'test_game_2', 'home_team': 'Celtics', 'away_team': 'Heat'}
        ],
        'prediction_types': ['game_prediction', 'player_prop', 'parlay', 'tournament'], # Added tournament type for generation
        'priority_level': 7
    }
    
    result = orchestrator.predict(test_data)
    orch_results = result['orchestration_results']
    
    # Test with empty games_data to trigger _get_real_games_data
    test_data_empty_games = {
        'orchestration_type': 'multi_game',
        'games_data': [], # This will trigger _get_real_games_data
        'prediction_types': ['game_prediction'],
        'priority_level': 5
    }
    result_empty_games = orchestrator.predict(test_data_empty_games)
    orch_results_empty_games = result_empty_games['orchestration_results']
