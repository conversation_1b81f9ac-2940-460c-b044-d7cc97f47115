import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import pytz
from geopy.distance import geodesic
import asyncio
import logging
import sys
import json
from typing import Dict, List, Tuple, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum
from scipy.optimize import minimize
from sklearn.ensemble import GradientBoostingRegressor, IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
from statsmodels.tsa.statespace.sarimax import SARIMAX
from lightgbm import LGBMRegressor
import holidays
import pywt  # PyWavelets for signal processing
from src.features.feature_feedback import FeatureFeedback
# Lazy import to avoid circular dependency
# from src.features.feature_alchemist import SelfLearningFeatureAlchemist
from vault_oracle.core.oracle_focus import oracle_focus
from vault_oracle.core.cosmic_exceptions import BaseCosmicException, ClusterError, PredictionSystemFailure
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator

"""
CHRONOS ULTIMA: Elite Fatigue Oracle with NBA/WNBA Parity
"""

# Configure elite-level logger
logger = logging.getLogger("ChronosUltima")
logger.setLevel(logging.INFO)
handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(logging.Formatter("🏀 %(asctime)s %(levelname)s :: %(message)s"))
logger.addHandler(handler)

# --- Elite Production Dependencies ---
try:
    pass  # All critical dependencies are imported above
except ImportError:
    # In production, these should always be available. If not, raise immediately.
    raise ImportError("Critical dependency missing: vault_oracle core or messaging orchestrator.")

# LEAGUE-SPECIFIC CONSTANTS -----------------------------------------------------
NBA_ARENA_LOCATIONS = {
    'ATL': (33.7573, -84.3963), 'BOS': (42.3662, -71.0621),
    'CHI': (41.8806, -87.6742), 'DAL': (32.7905, -96.8103),
    'DEN': (39.7487, -105.0077), 'GSW': (37.7680, -122.3878),
    'HOU': (29.7508, -95.3621), 'LAL': (34.0430, -118.2673),
    'MIA': (25.7814, -80.1870), 'NYK': (40.7505, -73.9934),
    # ... all NBA teams
}

WNBA_ARENA_LOCATIONS = {
    'ATL': (33.7573, -84.3963), 'CHI': (41.8806, -87.6742),
    'CON': (41.7658, -72.6734), 'DAL': (32.7905, -96.8103),
    'IND': (39.7639, -86.1555), 'LAS': (34.0430, -118.2673),
    'MIN': (44.9795, -93.2761), 'NYL': (40.7505, -73.9934),
    'PHO': (33.4458, -112.0712), 'SEA': (47.6205, -122.3493),
    'WAS': (38.8981, -77.0209)
}

NBA_TIMEZONES = {
    'ATL': 'US/Eastern', 'BOS': 'US/Eastern',
    'CHI': 'US/Central', 'DAL': 'US/Central',
    'DEN': 'US/Mountain', 'GSW': 'US/Pacific',
    'HOU': 'US/Central', 'LAL': 'US/Pacific',
    'MIA': 'US/Eastern', 'NYK': 'US/Eastern',
    # ... all NBA teams
}

WNBA_TIMEZONES = {
    'ATL': 'US/Eastern', 'CHI': 'US/Central',
    'CON': 'US/Eastern', 'DAL': 'US/Central',
    'IND': 'US/Eastern', 'LAS': 'US/Pacific',
    'MIN': 'US/Central', 'NYL': 'US/Eastern',
    'PHO': 'US/Mountain', 'SEA': 'US/Pacific',
    'WAS': 'US/Eastern'
}

ELITE_LEAGUE_CONFIG = {
    'NBA': {
        'max_games': 82,
        'playoff_months': [4, 5, 6],
        'season_start': 10,
        'avg_game_length': 48,
        'physiological_params': {
            'max_hr': 195,
            'rest_hr': 60,
            'vo2_max': 55,
            'muscle_fiber_ratio': 0.6  # Fast-twitch dominant
        },
        'fatigue_model_params': {
            'n_estimators': 1000,
            'learning_rate': 0.02,
            'max_depth': 7
        }
    },
    'WNBA': {
        'max_games': 36,
        'playoff_months': [8, 9],
        'season_start': 5,
        'avg_game_length': 40,
        'physiological_params': {
            'max_hr': 190,
            'rest_hr': 58,
            'vo2_max': 52,
            'muscle_fiber_ratio': 0.55  # More balanced fiber ratio
        },
        'fatigue_model_params': {
            'n_estimators': 800,
            'learning_rate': 0.025,
            'max_depth': 6
        }
    }
}

# CORE DATA STRUCTURES ----------------------------------------------------------
@dataclass
class EliteChronosConfig:
    """Elite Configuration with Biomechanical Parameters"""
    league: str = 'NBA'
    fatigue_threshold: float = 0.7
    neuromuscular_recovery_rate: float = 0.88
    circadian_disruption_factor: float = 1.8
    sleep_quality_weight: float = 0.25
    cognitive_fatigue_decay: float = 0.92
    hormonal_stress_factor: float = 1.3
    dynamic_loading_exponent: float = 2.5
    tissue_remodeling_threshold: float = 0.65
    
    def __post_init__(self):
        """Inject league-specific physiological parameters"""
        league_cfg = ELITE_LEAGUE_CONFIG.get(self.league)
        if not league_cfg:
            raise ValueError(f"Invalid league: {self.league}")
        
        self.max_games = league_cfg['max_games']
        self.playoff_months = league_cfg['playoff_months']
        self.avg_game_length = league_cfg['avg_game_length']
        self.physio_params = league_cfg['physiological_params']
        self.model_params = league_cfg['fatigue_model_params']

@dataclass
class FatigueMetrics:
    """Comprehensive Elite Fatigue Metrics"""
    physical_fatigue: float = 0.0
    neuromuscular_fatigue: float = 0.0
    cognitive_fatigue: float = 0.0
    metabolic_fatigue: float = 0.0
    cumulative_fatigue: float = 0.0
    acwr: float = 0.0                # Acute:Chronic Workload Ratio
    tissue_stress_index: float = 0.0
    recovery_capacity: float = 1.0
    injury_risk: float = 0.0
    performance_capacity: float = 1.0
    confidence_interval: Tuple[float, float] = (0.0, 0.0)

@dataclass
class TemporalContext:
    """High-Resolution Temporal Context"""
    days_since_last_game: int = 0
    games_in_last_7d: int = 0
    games_in_last_21d: int = 0
    back_to_back_index: float = 0.0
    home_ratio: float = 0.5
    travel_distance: float = 0.0
    timezone_disruption: float = 0.0
    season_progress: float = 0.0
    playoff_intensity: float = 0.0
    circadian_alignment: float = 1.0

class FatigueType(Enum):
    PHYSICAL = 1
    NEUROMUSCULAR = 2
    COGNITIVE = 3
    METABOLIC = 4

class ChronosFatigueOracle_Expert:
    """
    🏆 CHRONOS ULTIMA: Elite Fatigue Prediction System
    
    Incorporates:
    - Biomechanical load modeling
    - Circadian rhythm analysis
    - Hormonal stress response
    - Tissue remodeling thresholds
    - Dynamic system optimization
    - League-specific physiology
    """
    
    def __init__(self, config: Optional[EliteChronosConfig] = None):
        self.config = config or EliteChronosConfig()
        self.league = self.config.league
        self.logger = logger.getChild(f"Ultima.{self.league}")
        self.messenger = ExpertMessagingOrchestrator()
        
        # Initialize elite models
        self.fatigue_model = self._init_fatigue_model()
        self.recovery_model = LGBMRegressor(**self.config.model_params)
        self.anomaly_detector = IsolationForest(contamination=0.05)
        
        # Physiological state tracking
        self.team_states = {}

        # Initialize feature alchemist with lazy import
        try:
            from src.features.feature_alchemist import SelfLearningFeatureAlchemist
            self.feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)
        except ImportError:
            self.feature_alchemist = None
        
        self.logger.info(f"⚡ CHRONOS ULTIMA INITIALIZED FOR {self.league}")
    
    def _init_fatigue_model(self):
        """League-specific fatigue model initialization"""
        params = self.config.model_params
        if self.league == 'NBA':
            return GradientBoostingRegressor(
                n_estimators=params['n_estimators'],
                learning_rate=params['learning_rate'],
                max_depth=params['max_depth'],
                subsample=0.7
            )
        else:  # WNBA
            return LGBMRegressor(
                num_leaves=31,
                n_estimators=params['n_estimators'],
                learning_rate=params['learning_rate'],
                max_depth=params['max_depth']
            )
    
    # CORE ANALYSIS ENGINE ------------------------------------------------------
    @oracle_focus
    async def analyze_team_fatigue(
        self,
        team_id: str,
        game_date: datetime,
        game_log: List[Dict[str, Any]],
        biometrics: Optional[Dict] = None
    ) -> FatigueMetrics:
        """Elite multi-dimensional fatigue analysis"""
        try:
            # Create high-resolution temporal context
            context = self._create_temporal_context(team_id, game_date, game_log)
            
            # Advanced fatigue dimension analysis
            physical = self._calculate_physical_fatigue(game_log, context, biometrics)
            neuromuscular = self._calculate_neuromuscular_fatigue(game_log, context, biometrics)
            cognitive = self._calculate_cognitive_fatigue(game_log, context)
            metabolic = self._calculate_metabolic_fatigue(game_log, context, biometrics)
            
            # Calculate ACWR (Acute:Chronic Workload Ratio)
            acwr = self._calculate_acwr(game_log)
            
            # Tissue stress modeling
            tissue_stress = self._model_tissue_stress(physical, neuromuscular, context)
            
            # Integrative fatigue modeling
            cumulative_fatigue = self._integrate_fatigue_dimensions(
                physical, neuromuscular, cognitive, metabolic, context
            )
            
            # Recovery capacity prediction
            recovery_capacity = self._predict_recovery_capacity(team_id, context, biometrics)
            
            # Injury risk assessment
            injury_risk = self._assess_injury_risk(
                cumulative_fatigue, tissue_stress, acwr, context
            )
            
            # Performance capacity modeling
            performance_capacity = self._model_performance_capacity(
                cumulative_fatigue, recovery_capacity, context
            )
            
            # Confidence interval calculation
            confidence = self._calculate_confidence_interval(
                [physical, neuromuscular, cognitive, metabolic]
            )
            
            # Update physiological state
            self._update_team_state(team_id, {
                'last_game': game_date,
                'cumulative_fatigue': cumulative_fatigue,
                'tissue_stress': tissue_stress,
                'recovery_capacity': recovery_capacity
            })
            
            return FatigueMetrics(
                physical_fatigue=physical,
                neuromuscular_fatigue=neuromuscular,
                cognitive_fatigue=cognitive,
                metabolic_fatigue=metabolic,
                cumulative_fatigue=cumulative_fatigue,
                acwr=acwr,
                tissue_stress_index=tissue_stress,
                recovery_capacity=recovery_capacity,
                injury_risk=injury_risk,
                performance_capacity=performance_capacity,
                confidence_interval=confidence
            )
            
        except Exception as e:
            self.logger.error(f"CRITICAL ANALYSIS FAILURE: {e}", exc_info=True)
            await self.messenger.send_alert(
                "chronos_analysis_failure",
                f"Fatigue analysis failed for {team_id}",
                {"error": str(e), "team": team_id, "date": game_date.isoformat()}
            )
            return FatigueMetrics()
    
    # ELITE FATIGUE DIMENSION CALCULATIONS --------------------------------------
    def _calculate_physical_fatigue(self, game_log, context, biometrics):
        """Biomechanical fatigue based on tissue loading models"""
        # Calculate cumulative tissue load using Herzog model
        tissue_load = 0.0
        for game in game_log:
            intensity = game.get('intensity', 0.5)
            duration = game.get('minutes', self.config.avg_game_length) / 60
            eccentric_load = game.get('eccentric_index', 1.2)  # Eccentric movement stress
            
            # Modified Herzog equation
            load = (intensity ** self.config.dynamic_loading_exponent) * duration * eccentric_load
            decay = self.config.neuromuscular_recovery_rate ** (
                (datetime.now() - datetime.fromisoformat(game['date'])).days
            )
            tissue_load += load * decay
        
        # Normalize using league-specific thresholds
        max_load = 120 if self.league == 'NBA' else 100
        return min(1.0, tissue_load / max_load)
    
    def _calculate_neuromuscular_fatigue(self, game_log, context, biometrics):
        """Central nervous system fatigue modeling"""
        # Use biometrics if available
        if biometrics and 'cns_fatigue' in biometrics:
            return biometrics['cns_fatigue']
        
        # Fallback to algorithmic approximation
        neuro_fatigue = 0.0
        recent_intensity = sum(g['intensity'] for g in game_log[:3]) / 3
        neuro_fatigue = min(0.8, recent_intensity * 0.7)
        
        # Circadian disruption amplifies CNS fatigue
        neuro_fatigue *= (2 - context.circadian_alignment)
        return neuro_fatigue
    
    def _calculate_cognitive_fatigue(self, game_log, context):
        """Cognitive load from decision-making complexity"""
        clutch_factor = sum(1 for g in game_log if g.get('clutch', False)) / len(game_log)
        travel_impact = context.timezone_disruption * 0.3
        back_to_back_impact = context.back_to_back_index * 0.2
        
        cognitive_load = min(0.75, clutch_factor * 0.6 + travel_impact + back_to_back_impact)
        
        # Cognitive fatigue decays differently
        return cognitive_load * (self.config.cognitive_fatigue_decay ** context.days_since_last_game)
    
    def _calculate_metabolic_fatigue(self, game_log, context, biometrics):
        """Energy system depletion modeling"""
        if biometrics and 'glycogen' in biometrics:
            glycogen = biometrics['glycogen']
            return max(0, (100 - glycogen) / 100)  # Normalize 0-100% to 0-1
        
        # Algorithmic approximation
        games_last_week = context.games_in_last_7d
        metabolic_stress = min(0.9, games_last_week * 0.25)
        
        # Playoff games increase metabolic demand
        metabolic_stress *= (1 + context.playoff_intensity * 0.3)
        return metabolic_stress
    
    # ADVANCED MODELING TECHNIQUES ----------------------------------------------
    def _integrate_fatigue_dimensions(self, physical, neuro, cognitive, metabolic, context):
        """Non-linear fatigue integration using dynamic weighting"""
        weights = {
            'physical': 0.35,
            'neuro': 0.30,
            'cognitive': 0.20,
            'metabolic': 0.15
        }
        
        # Adjust weights based on context
        if context.playoff_intensity > 0.7:
            weights['physical'] += 0.05
            weights['neuro'] += 0.05
            weights['cognitive'] -= 0.05
            weights['metabolic'] -= 0.05
        
        # Apply non-linear scaling
        integrated = (
            physical ** 1.5 * weights['physical'] +
            neuro * weights['neuro'] +
            cognitive * weights['cognitive'] +
            metabolic ** 0.8 * weights['metabolic']
        )
        
        # Hormonal stress amplification
        cortisol_factor = 1 + (context.playoff_intensity * self.config.hormonal_stress_factor * 0.1)
        return min(1.0, integrated * cortisol_factor)
    
    def _calculate_acwr(self, game_log):
        """Acute:Chronic Workload Ratio with exponential smoothing"""
        if len(game_log) < 10:
            return 0.5  # Default safe value
        
        # Calculate acute load (last 7 days)
        acute_load = sum(
            g['intensity'] * g['minutes'] 
            for g in game_log 
            if (datetime.now() - datetime.fromisoformat(g['date'])).days <= 7
        )
        
        # Calculate chronic load (last 28 days with exponential decay)
        chronic_load = 0
        decay_factor = 0.95
        for i, game in enumerate(game_log[:28]):
            days_ago = (datetime.now() - datetime.fromisoformat(game['date'])).days
            if days_ago <= 28:
                decay = decay_factor ** (28 - days_ago)
                chronic_load += game['intensity'] * game['minutes'] * decay
        
        # Avoid division by zero
        if chronic_load <= 0:
            return 0.5
        
        return acute_load / chronic_load
    
    def _model_tissue_stress(self, physical, neuromuscular, context):
        """Micro-trauma accumulation model"""
        # Combine mechanical and neuromuscular stress
        base_stress = (physical * 0.7) + (neuromuscular * 0.3)
        
        # Tissue remodeling threshold effect
        if base_stress > self.config.tissue_remodeling_threshold:
            excess = base_stress - self.config.tissue_remodeling_threshold
            return base_stress + (excess * 1.5)  # Accelerated damage
        return base_stress
    
    def _predict_recovery_capacity(self, team_id, context, biometrics):
        """Predictive recovery modeling with physiological constraints"""
        # Base recovery decay
        recovery = 1.0 - (0.1 * context.games_in_last_7d)
        
        # Sleep quality effect
        sleep_quality = self._estimate_sleep_quality(context)
        recovery *= sleep_quality
        
        # Circadian alignment bonus
        recovery *= context.circadian_alignment
        
        # Biometric enhancements
        if biometrics:
            if 'hrv' in biometrics:  # Heart Rate Variability
                hrv = biometrics['hrv']
                recovery *= min(1.2, hrv / 60)  # Normalize to elite baseline
            
            if 'testosterone_cortisol' in biometrics:
                t_c_ratio = biometrics['testosterone_cortisol']
                recovery *= min(1.3, t_c_ratio * 1.2)
        
        return max(0.3, min(1.0, recovery))
    
    # RISK AND PERFORMANCE MODELING ---------------------------------------------
    def _assess_injury_risk(self, cumulative_fatigue, tissue_stress, acwr, context):
        """Multi-factor injury risk assessment"""
        base_risk = cumulative_fatigue * 0.4
        
        # ACWR risk profile
        if acwr > 1.2:
            base_risk += 0.25
        elif acwr < 0.8:
            base_risk += 0.1  # Under-training risk
            
        # Tissue stress amplification
        base_risk += tissue_stress * 0.3
        
        # Travel and circadian disruption
        base_risk += context.timezone_disruption * 0.2
        
        return min(0.95, base_risk)  # Cap at 95%
    
    def _model_performance_capacity(self, cumulative_fatigue, recovery_capacity, context):
        """Performance prediction with fatigue-recovery interaction"""
        # Base capacity inverse to fatigue
        capacity = 1.0 - cumulative_fatigue
        
        # Recovery multiplier
        capacity *= recovery_capacity
        
        # Clutch performance enhancement
        if context.playoff_intensity > 0.8:
            capacity *= (1 + context.playoff_intensity * 0.2)
            
        return max(0.1, min(1.0, capacity))
    
    # TEMPORAL CONTEXT ENGINE ---------------------------------------------------
    def _create_temporal_context(self, team_id, game_date, game_log):
        """High-resolution temporal modeling"""
        # Basic metrics
        days_since_last = self._days_since_last_game(game_log, game_date)
        games_7d = self._games_in_period(game_log, game_date, 7)
        games_21d = self._games_in_period(game_log, game_date, 21)
        home_ratio = self._home_game_ratio(game_log)
        
        # Advanced metrics
        travel_distance = self._calculate_travel_distance(game_log, team_id)
        timezone_disruption = self._calculate_timezone_disruption(game_log, team_id)
        back_to_back_index = self._calculate_back_to_back_index(game_log)
        playoff_intensity = self._calculate_playoff_intensity(game_date)
        circadian_alignment = self._calculate_circadian_alignment(game_date, game_log, team_id)
        season_progress = self._calculate_season_progress(game_log)
        
        return TemporalContext(
            days_since_last_game=days_since_last,
            games_in_last_7d=games_7d,
            games_in_last_21d=games_21d,
            back_to_back_index=back_to_back_index,
            home_ratio=home_ratio,
            travel_distance=travel_distance,
            timezone_disruption=timezone_disruption,
            season_progress=season_progress,
            playoff_intensity=playoff_intensity,
            circadian_alignment=circadian_alignment
        )
    
    def _calculate_travel_distance(self, game_log, team_id):
        """Precise geodesic distance calculation"""
        arena_locations = NBA_ARENA_LOCATIONS if self.league == 'NBA' else WNBA_ARENA_LOCATIONS
        home_base = arena_locations.get(team_id)
        if not home_base:
            return 0.0
            
        total_distance = 0.0
        current_location = home_base
        
        for game in game_log:
            if game.get('home'):
                next_location = home_base
            else:
                opponent = game.get('opponent', '')
                next_location = arena_locations.get(opponent, home_base)
                
            if current_location and next_location:
                total_distance += geodesic(current_location, next_location).miles
            current_location = next_location
            
        return total_distance * (0.9 if self.league == 'WNBA' else 1.0)
    
    def _calculate_timezone_disruption(self, game_log, team_id):
        """Circadian disruption scoring"""
        team_timezones = NBA_TIMEZONES if self.league == 'NBA' else WNBA_TIMEZONES
        home_tz = team_timezones.get(team_id)
        if not home_tz:
            return 0.0
            
        disruption_score = 0.0
        prev_tz = home_tz
        
        for game in game_log:
            if game.get('home'):
                current_tz = home_tz
            else:
                opponent = game.get('opponent', '')
                current_tz = team_timezones.get(opponent, home_tz)
                
            if current_tz != prev_tz:
                # Calculate time difference
                prev_tz_obj = pytz.timezone(prev_tz)
                current_tz_obj = pytz.timezone(current_tz)
                tz_diff = current_tz_obj.utcoffset(datetime.utcnow()).seconds/3600 - \
                          prev_tz_obj.utcoffset(datetime.utcnow()).seconds/3600
                
                disruption_score += min(1.0, abs(tz_diff) / 3.0)  # Normalize
                
            prev_tz = current_tz
            
        return min(2.0, disruption_score)  # Cap disruption score
    
    def _calculate_circadian_alignment(self, game_date, game_log, team_id):
        """Circadian rhythm alignment score (0-1)"""
        team_timezones = NBA_TIMEZONES if self.league == 'NBA' else WNBA_TIMEZONES
        home_tz = team_timezones.get(team_id)
        if not home_tz:
            return 1.0
            
        # Calculate game time alignment
        game_times = [datetime.fromisoformat(g['date']).hour for g in game_log[:5]]
        avg_game_time = np.mean(game_times) if game_times else 20.0  # Default prime time
        
        # Ideal circadian window (1pm-7pm local)
        if 13 <= avg_game_time <= 19:
            alignment = 1.0
        elif avg_game_time < 13 or avg_game_time > 21:
            alignment = 0.7  # Suboptimal times
        else:
            alignment = 0.85
            
        # Travel disruption penalty
        travel_disruption = self._calculate_timezone_disruption(game_log[:3], team_id)
        return max(0.6, alignment - (travel_disruption * 0.15))
    
    # ELITE OPTIMIZATION SYSTEM -------------------------------------------------
    @oracle_focus
    async def optimize_rest_schedule(
        self,
        team_id: str,
        upcoming_games: List[Dict[str, Any]],
        player_profiles: Dict[str, Dict],
        strategic_goals: Dict[str, float]
    ) -> Dict[str, Any]:
        """Constrained optimization for elite load management"""
        try:
            # Prepare optimization parameters
            num_games = len(upcoming_games)
            num_players = len(player_profiles)
            x0 = np.zeros(num_games * num_players)  # Initial state (full minutes)
            
            # Define optimization bounds (0-1 for minute reduction)
            bounds = [(0, 0.4) for _ in range(num_games * num_players)]
            
            # Define constraints
            constraints = self._create_optimization_constraints(
                upcoming_games, player_profiles, strategic_goals
            )
            
            # Run optimization
            result = minimize(
                fun=self._optimization_objective,
                x0=x0,
                args=(upcoming_games, player_profiles, strategic_goals),
                method='SLSQP',
                bounds=bounds,
                constraints=constraints
            )
            
            # Interpret results
            return self._interpret_optimization_results(
                result, upcoming_games, player_profiles
            )
            
        except Exception as e:
            self.logger.error(f"OPTIMIZATION FAILURE: {e}", exc_info=True)
            return {
                "status": "failure",
                "recommendations": [],
                "expected_performance_gain": 0.0
            }
    
    def _optimization_objective(self, x, games, players, goals):
        """Objective function to minimize fatigue while maximizing performance"""
        fatigue_cost = 0
        performance_gain = 0
        
        # Implement multi-objective optimization
        # (Complex implementation would follow here)
        
        # Return net cost to minimize
        return fatigue_cost - performance_gain
    
    # UTILITY METHODS -----------------------------------------------------------
    def _estimate_sleep_quality(self, context):
        """Predict sleep quality based on temporal factors"""
        base_quality = 0.85
        # Travel disruption reduces sleep quality
        base_quality -= min(0.3, context.timezone_disruption * 0.15)
        # Back-to-back games reduce sleep quality
        base_quality -= min(0.2, context.back_to_back_index * 0.1)
        return max(0.6, base_quality)
    
    def _calculate_confidence_interval(self, fatigue_components):
        """Calculate prediction confidence interval"""
        mean_val = np.mean(fatigue_components)
        std_dev = np.std(fatigue_components)
        return (
            max(0, mean_val - 1.96 * std_dev),
            min(1.0, mean_val + 1.96 * std_dev)
        )
    
    def _update_team_state(self, team_id, state):
        """Update physiological team state"""
        self.team_states[team_id] = state
        
    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """Enable self-learning and self-correction for the spire."""
        if feedback:
            self.logger.info(f"[ChronosFatigueOracle] Received feedback: {feedback}")
            # Example: Adjust config or model params based on feedback
            for key, value in feedback.get('param_adjustments', {}).items():
                if hasattr(self.config, key):
                    setattr(self.config, key, getattr(self.config, key) + value)
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)
        # Self-diagnosis
        if hasattr(self, 'fatigue_model') and hasattr(self, 'performance_history') and self.performance_history:
            avg_perf = sum(self.performance_history) / len(self.performance_history)
            if avg_perf < 0.5:
                self.logger.warning("[ChronosFatigueOracle] Fatigue model performance low. Triggering self-correction.")
                self._self_correct()

    def _self_correct(self):
        """Internal logic to self-correct or reset parameters if performance is poor."""
        self.logger.info("[ChronosFatigueOracle] Performing self-correction/reset.")
        # Reset config to defaults
        self.config = EliteChronosConfig()
        if not hasattr(self, 'self_correction_log'):
            self.self_correction_log = []
        self.self_correction_log.append({'timestamp': datetime.now().isoformat(), 'action': 'reset_config'})
    
    # TEMPORAL HELPER METHODS ---------------------------------------------------
    def _days_since_last_game(self, game_log, current_date):
        if not game_log:
            return 7  # Default if no games
        last_game_date = max(datetime.fromisoformat(g['date']) for g in game_log)
        return (current_date - last_game_date).days
    
    def _games_in_period(self, game_log, current_date, days):
        cutoff = current_date - timedelta(days=days)
        return sum(1 for g in game_log if datetime.fromisoformat(g['date']) >= cutoff)
    
    def _home_game_ratio(self, game_log):
        if not game_log:
            return 0.5
        home_games = sum(1 for g in game_log if g.get('home', False))
        return home_games / len(game_log)
    
    def _calculate_back_to_back_index(self, game_log):
        """Calculate back-to-back intensity"""
        b2b_count = 0
        for i in range(1, len(game_log)):
            prev_date = datetime.fromisoformat(game_log[i-1]['date'])
            curr_date = datetime.fromisoformat(game_log[i]['date'])
            if (curr_date - prev_date).days == 1:
                b2b_count += 1
        return min(3.0, b2b_count) / 3.0  # Normalize 0-1
    
    def _calculate_playoff_intensity(self, game_date):
        """Calculate playoff intensity factor"""
        if game_date.month in self.config.playoff_months:
            # Later playoff rounds have higher intensity
            if game_date.month == self.config.playoff_months[0]:
                return 0.7
            elif game_date.month == self.config.playoff_months[1]:
                return 0.9
            else:
                return 1.0
        return 0.0
    
    def _calculate_season_progress(self, game_log):
        """Calculate season progression (0-1)"""
        games_played = len(game_log)
        return min(1.0, games_played / self.config.max_games)

    def predict(self, features, *args, **kwargs):
        """Make a prediction and assess confidence"""
        prediction = self.model.predict(features)
        confidence = prediction.get('confidence', 1.0)
        # --- Feedback wiring: send feedback if confidence is low ---
        if confidence < 0.3:
            feedback = FeatureFeedback(self.__class__.__name__, features, confidence, message="Low confidence. Requesting feature improvement.")
            self.feature_alchemist.receive_feedback(feedback)
        return prediction

    def predict_play_outcome(
        self,
        team_fatigue: FatigueMetrics,
        play_context: Dict[str, Any],
        player_fatigue: Optional[Dict[str, float]] = None
    ) -> Dict[str, float]:
        """
        Predict the outcome of a specific play based on fatigue metrics and contextual factors

        Args:
            team_fatigue: Team-level fatigue metrics
            play_context: Contextual information about the play
            player_fatigue: Optional dictionary of player-specific fatigue levels

        Returns:
            Dictionary with outcome probabilities and expected points
        """
        try:
            # Extract play parameters
            play_type = play_context.get('play_type', 'half_court_set')
            quarter = play_context.get('quarter', 3)
            time_remaining = play_context.get('time_remaining', 12.0)
            score_differential = play_context.get('score_differential', 0)
            defensive_pressure = play_context.get('defensive_pressure', 0.5)
            primary_player_fatigue = team_fatigue.cumulative_fatigue

            # Adjust for player-specific fatigue if available
            if player_fatigue and play_context.get('primary_player'):
                player_id = play_context['primary_player']
                primary_player_fatigue = player_fatigue.get(player_id, team_fatigue.cumulative_fatigue)

            # Base probabilities by play type
            base_probabilities = {
                'pick_and_roll': {'success': 0.45, 'turnover': 0.15, 'foul': 0.08},
                'isolation': {'success': 0.42, 'turnover': 0.12, 'foul': 0.10},
                'post_up': {'success': 0.48, 'turnover': 0.10, 'foul': 0.12},
                'fast_break': {'success': 0.55, 'turnover': 0.08, 'foul': 0.05},
                'spot_up': {'success': 0.38, 'turnover': 0.05, 'foul': 0.03},
                'half_court_set': {'success': 0.40, 'turnover': 0.10, 'foul': 0.07}
            }

            # Get base probabilities for this play type
            base = base_probabilities.get(play_type, base_probabilities['half_court_set'])

            # Calculate fatigue adjustments
            fatigue_impact = primary_player_fatigue * 0.3  # Fatigue reduces success probability
            clutch_factor = self._calculate_clutch_factor(quarter, time_remaining, score_differential)

            # Calculate success probability adjustments
            success_prob = max(0.1, min(0.95,
                base['success'] -
                fatigue_impact +
                clutch_factor -
                (defensive_pressure * 0.15)
            ))

            # Fatigue increases turnover probability
            turnover_prob = min(0.5, max(0.05,
                base['turnover'] +
                (primary_player_fatigue * 0.2) +
                (defensive_pressure * 0.1)
            ))

            # Fatigue slightly increases foul probability (tired players make mistakes)
            foul_prob = min(0.3, max(0.01,
                base['foul'] +
                (primary_player_fatigue * 0.05) +
                (defensive_pressure * 0.08)
            ))

            # Normalize probabilities
            total_prob = success_prob + turnover_prob + foul_prob
            if total_prob > 0.95:
                scale_factor = 0.95 / total_prob
                success_prob *= scale_factor
                turnover_prob *= scale_factor
                foul_prob *= scale_factor

            # Calculate expected points
            point_value = play_context.get('point_value', 2)
            expected_points = success_prob * point_value + foul_prob * 1.5  # FT = 1.5 expected points

            # Fatigue reduces expected points
            expected_points *= (1.0 - (primary_player_fatigue * 0.2))

            # Return outcome prediction
            return {
                'success_probability': round(success_prob, 3),
                'expected_points': round(expected_points, 2),
                'turnover_probability': round(turnover_prob, 3),
                'foul_probability': round(foul_prob, 3),
                'fatigue_impact': round(fatigue_impact, 3),
                'clutch_factor': round(clutch_factor, 3),
                'spire_type': 'chronos_play_prediction',
                'play_type': play_type
            }

        except Exception as e:
            self.logger.error(f"Play outcome prediction failed: {e}")
            # Fallback probabilities
            return {
                'success_probability': 0.4,
                'expected_points': 0.8,
                'turnover_probability': 0.15,
                'foul_probability': 0.05,
                'fatigue_impact': 0.0,
                'clutch_factor': 0.0,
                'spire_type': 'chronos_play_prediction',
                'play_type': 'fallback'
            }

    def _calculate_clutch_factor(self, quarter: int, time_remaining: float, score_differential: int) -> float:
        """
        Calculate clutch performance multiplier based on game context

        Args:
            quarter: Current quarter (1-4, OT=5+)
            time_remaining: Time remaining in quarter (minutes)
            score_differential: Current score differential

        Returns:
            Clutch performance multiplier (0.0 - 0.3)
        """
        # Late game scenarios
        if quarter >= 4 and time_remaining < 2.0 and abs(score_differential) <= 5:
            return 0.25  # Significant clutch boost
        elif quarter >= 4 and time_remaining < 5.0 and abs(score_differential) <= 8:
            return 0.15
        # High-pressure situations
        elif abs(score_differential) <= 3:
            if time_remaining < 3.0:
                return 0.10
            elif quarter >= 4:
                return 0.08
        return 0.0

# FACTORY FUNCTION --------------------------------------------------------------
def create_chronos_expert(league: str = 'NBA') -> ChronosFatigueOracle_Expert:
    """Create an elite Chronos instance for specified league"""
    config = EliteChronosConfig(league=league)
    return ChronosFatigueOracle_Expert(config)

# DEMONSTRATION -----------------------------------------------------------------
if __name__ == "__main__":
    
    # Create NBA oracle
    nba_oracle = create_chronos_expert('NBA')
    
    # Sample game log
    game_log = [
        {'date': '2024-05-15', 'home': True, 'minutes': 240, 'intensity': 0.85, 'clutch': True},
        {'date': '2024-05-13', 'home': False, 'minutes': 242, 'intensity': 0.92, 'clutch': False},
        {'date': '2024-05-12', 'home': False, 'minutes': 238, 'intensity': 0.88, 'clutch': True},
    ]
    
    # Analyze fatigue
    analysis_date = datetime(2024, 5, 18)
    metrics = asyncio.run(nba_oracle.analyze_team_fatigue(
        "LAL", analysis_date, game_log
    ))
    
    

