import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta
import numpy as np
import random

try:
    from vault_oracle.analysis.unified_temporal_anomaly import UnifiedTemporalAnomalyDetector
    UNIFIED_TEMPORAL_ANOMALY_AVAILABLE = True
except ImportError:
    UNIFIED_TEMPORAL_ANOMALY_AVAILABLE = False
    UnifiedTemporalAnomalyDetector = None

try:
    from vault_oracle.analysis.temporal_stabilizer import TemporalStabilizer
    TEMPORAL_STABILIZER_AVAILABLE = True
except ImportError:
    TEMPORAL_STABILIZER_AVAILABLE = False
    TemporalStabilizer = None




"""
Temporal Models Module - Bridge to Unified Temporal Analysis

This module provides backward compatibility for the old vault_oracle.ai.temporal_models
imports by bridging to the new unified temporal analysis system.

The temporal models have been unified and moved to:
- vault_oracle.analysis.unified_temporal_anomaly
- vault_oracle.analysis.temporal_stabilizer

This module maintains compatibility while directing to the new unified system.
"""


logger = logging.getLogger(__name__)

# Import from the unified temporal analysis system
try:
    from vault_oracle.analysis.unified_temporal_anomaly import (
        UnifiedTemporalAnomalyDetector,
        unified_temporal_anomaly_detector
    )
    from vault_oracle.analysis.temporal_stabilizer import (
        ExpertTemporalFluxStabilizer,
        QuantumTemporalAnalyzer
    )
    UNIFIED_TEMPORAL_AVAILABLE = True
    logger.info("🧠 MEDUSA VAULT: Successfully imported unified temporal analysis system")
except ImportError as e:
    UNIFIED_TEMPORAL_AVAILABLE = False
    logger.warning(f"🧠 MEDUSA VAULT: Unified temporal system unavailable: {e}")
    # Set fallback values
    unified_temporal_anomaly_detector = None
    ExpertTemporalFluxStabilizer = None
    QuantumTemporalAnalyzer = None


def detect_anomalies(data_stream: Any, feature: str = "entropy_level", z_thresh: float = 2.5, 
                    context: Optional[Dict] = None) -> List[Dict[str, Any]]:
    """
    Detect temporal anomalies in data stream.
    
    This function bridges to the unified temporal anomaly detection system.
    
    Args:
        data_stream: List of data points or dictionaries
        feature: Feature name to analyze (for dict data)
        z_thresh: Z-score threshold for anomaly detection
        context: Optional context for enhanced analysis
        
    Returns:
        List of detected anomalies with metadata
    """
    if UNIFIED_TEMPORAL_AVAILABLE:
        # Use the unified temporal anomaly detector
        enhanced_context = context or {}
        enhanced_context.update({
            'feature': feature,
            'z_threshold': z_thresh,
            'analysis_type': 'temporal_anomaly'
        })
        return unified_temporal_anomaly_detector.detect_anomalies(data_stream, enhanced_context)
    else:
        # Fallback implementation
        logger.warning("🧠 MEDUSA VAULT: Using fallback anomaly detection")
        return _fallback_detect_anomalies(data_stream, feature, z_thresh)


def _fallback_detect_anomalies(data_stream: Any, feature: str = "entropy_level", 
                              z_thresh: float = 2.5) -> List[Dict[str, Any]]:
    """
    Fallback implementation for temporal anomaly detection.
    Uses basic statistical z-score analysis.
    """
    if not data_stream or len(data_stream) < 3:
        return []
    
    try:
        
        # Extract values based on data type
        if isinstance(data_stream[0], dict):
            if feature in data_stream[0]:
                values = [item[feature] for item in data_stream if feature in item]
            else:
                # Try common numeric fields
                for field in ['value', 'score', 'metric', 'entropy_level']:
                    if field in data_stream[0]:
                        values = [item[field] for item in data_stream if field in item]
                        break
                else:
                    return []
        else:
            values = list(data_stream)
        
        if len(values) < 3:
            return []
        
        # Calculate z-scores
        values = np.array(values, dtype=float)
        mean_val = np.mean(values)
        std_val = np.std(values)
        
        if std_val == 0:
            return []
        
        z_scores = np.abs((values - mean_val) / std_val)
        anomalies = []
        
        for i, (value, z_score) in enumerate(zip(values, z_scores)):
            if z_score > z_thresh:
                anomaly = {
                    'index': i,
                    'value': float(value),
                    'z_score': float(z_score),
                    'threshold': z_thresh,
                    'feature': feature,
                    'type': 'statistical_outlier',
                    'timestamp': datetime.now().isoformat(),
                    'severity': 'high' if z_score > z_thresh * 1.5 else 'medium'
                }
                anomalies.append(anomaly)
        
        return anomalies
        
    except Exception as e:
        logger.error(f"🧠 MEDUSA VAULT: Fallback anomaly detection failed: {e}")
        return []


class TemporalPredictor:
    """
    Temporal Predictor - Bridge to Unified Temporal Analysis
    
    This class provides backward compatibility for temporal prediction
    by bridging to the new unified temporal analysis system.
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize the temporal predictor."""
        self.config = config or {}
        self.logger = logger
        
        if UNIFIED_TEMPORAL_AVAILABLE:
            self.temporal_stabilizer = ExpertTemporalFluxStabilizer(
                enable_quantum_analysis=True,
                basketball_awareness=True
            )
            self.quantum_analyzer = QuantumTemporalAnalyzer()
            self.logger.info("🧠 MEDUSA VAULT: TemporalPredictor initialized with unified system")
        else:
            self.temporal_stabilizer = None
            self.quantum_analyzer = None
            self.logger.warning("🧠 MEDUSA VAULT: TemporalPredictor using fallback mode")
    
    def predict_temporal_evolution(self, data: Any, prediction_horizon: Union[timedelta, int] = None) -> Dict[str, Any]:
        """
        Predict temporal evolution of data patterns.
        
        Args:
            data: Input data for temporal analysis
            prediction_horizon: Time horizon for prediction
            
        Returns:
            Dictionary containing temporal evolution predictions
        """
        if self.temporal_stabilizer:
            # Use unified temporal system
            if isinstance(prediction_horizon, int):
                prediction_horizon = timedelta(hours=prediction_horizon)
            elif prediction_horizon is None:
                prediction_horizon = timedelta(hours=24)
            
            return self.temporal_stabilizer.predict_temporal_evolution(data, prediction_horizon)
        else:
            # Fallback prediction
            return self._fallback_predict_evolution(data, prediction_horizon)
    
    def analyze_temporal_patterns(self, data: Any, timeframe: str = "auto") -> Dict[str, Any]:
        """
        Analyze temporal patterns in data.
        
        Args:
            data: Input data for pattern analysis
            timeframe: Analysis timeframe
            
        Returns:
            Dictionary containing temporal pattern analysis
        """
        if self.temporal_stabilizer:
            return self.temporal_stabilizer.analyze_temporal_patterns(data, timeframe)
        else:
            return self._fallback_pattern_analysis(data, timeframe)
    
    def _fallback_predict_evolution(self, data: Any, prediction_horizon: Any) -> Dict[str, Any]:
        """Fallback temporal evolution prediction."""
        
        return {
            "evolution_probability": random.uniform(0.3, 0.8),
            "stability_forecast": random.uniform(0.4, 0.9),
            "confidence": random.uniform(0.5, 0.7),
            "prediction_horizon": str(prediction_horizon) if prediction_horizon else "24h",
            "method": "fallback",
            "warning": "Using fallback prediction - unified temporal system unavailable"
        }
    
    def _fallback_pattern_analysis(self, data: Any, timeframe: str) -> Dict[str, Any]:
        """Fallback temporal pattern analysis."""
        
        return {
            "pattern_strength": random.uniform(0.3, 0.8),
            "temporal_stability": random.uniform(0.4, 0.9),
            "pattern_type": "linear_trend",
            "confidence": random.uniform(0.5, 0.7),
            "timeframe": timeframe,
            "method": "fallback",
            "warning": "Using fallback analysis - unified temporal system unavailable"
        }


# Provide backward compatibility aliases
TemporalAnomalyDetector = TemporalPredictor  # Legacy alias
ExpertTemporalAnalyzer = TemporalPredictor   # Legacy alias

# Export main functions and classes
__all__ = [
    'detect_anomalies',
    'TemporalPredictor',
    'TemporalAnomalyDetector',  # Legacy alias
    'ExpertTemporalAnalyzer'    # Legacy alias
]
