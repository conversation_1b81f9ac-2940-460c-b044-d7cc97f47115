#!/usr/bin/env python3
"""
Expert-Level Cluster Realms Management System

Advanced cluster routing, load balancing, and realm management for the Oracle
prediction system with quantum-inspired load distribution, professional-grade
failover management, and intelligent realm discovery.

Features:
- Quantum-inspired load balancing algorithms
- Professional health monitoring and failover
- Advanced realm discovery and auto-scaling
- Basketball-specific routing optimizations
- Real-time performance analytics
- Expert-level cluster orchestration

"""


from pydantic import BaseModel, Field, field_validator, model_validator, ValidationInfo
from typing import Dict, Any, List, Optional, Union, Tuple, Set
from datetime import datetime, timedelta, timezone # Added timezone for consistent UTC usage
from enum import Enum
from dataclasses import dataclass, field
import numpy as np
import asyncio
import logging
import json
import hashlib
import os
import sys
from pathlib import Path
from backend.config.production_config import ProductionConfig
from vault_oracle.core.oracle_focus import oracle_focus


# Try to import production components
try:
    PRODUCTION_IMPORTS_AVAILABLE = True
except ImportError:
    PRODUCTION_IMPORTS_AVAILABLE = False

#!/usr/bin/env python3

"""
Expert-Level Cluster Realms Management System

Advanced cluster routing, load balancing, and realm management for the Oracle
prediction system with quantum-inspired load distribution, professional-grade
failover management, and intelligent realm discovery.

Features:
- Quantum-inspired load balancing algorithms
- Professional health monitoring and failover
- Advanced realm discovery and auto-scaling
- Basketball-specific routing optimizations
- Real-time performance analytics
- Expert-level cluster orchestration

"""


# Configure logger for cluster management
logger = logging.getLogger(__name__)

# Use production imports if available, otherwise create fallbacks
if not PRODUCTION_IMPORTS_AVAILABLE:
    logger.warning("Could not import ProductionConfig or oracle_focus. Using mock implementations.")

    # Mock oracle_focus decorator
    def oracle_focus(func):
        """Fallback decorator for oracle focus (does nothing)"""
        return func

    # Production-ready database configuration with environment variable support
    @dataclass
    class ProductionDatabaseConfig:
        driver: str = "postgresql+asyncpg"
        user: str = "test_user"
        password: Optional[str] = None
        host: str = "localhost"
        port: int = 5432
        name: str = "test_db"
        pool_size: int = 10
        max_overflow: int = 20
        pool_timeout: int = 30
        pool_recycle: int = 3600
        pool_pre_ping: bool = True
        ssl_mode: str = "disable"
        ssl_cert: Optional[str] = None
        ssl_key: Optional[str] = None
        ssl_ca: Optional[str] = None

    @dataclass
    class ProductionAppConfig:
        """Production application configuration with environment variable support"""
        debug: bool = field(default_factory=lambda: os.getenv('DEBUG', 'false').lower() == 'true')
        environment: str = field(default_factory=lambda: os.getenv('ENVIRONMENT', 'production'))
        log_level: str = field(default_factory=lambda: os.getenv('LOG_LEVEL', 'INFO'))
        secret_key: str = field(default_factory=lambda: os.getenv('SECRET_KEY', 'production-secret-key'))

        def __post_init__(self):
            logger.info(f"🔧 Production App Config initialized: env={self.environment}, debug={self.debug}")

    @dataclass
    class ProductionRedisConfig:
        """Production Redis configuration with environment variable support"""
        host: str = field(default_factory=lambda: os.getenv('REDIS_HOST', 'localhost'))
        port: int = field(default_factory=lambda: int(os.getenv('REDIS_PORT', '6379')))
        db: int = field(default_factory=lambda: int(os.getenv('REDIS_DB', '0')))
        password: Optional[str] = field(default_factory=lambda: os.getenv('REDIS_PASSWORD'))
        socket_timeout: int = field(default_factory=lambda: int(os.getenv('REDIS_SOCKET_TIMEOUT', '5')))
        socket_connect_timeout: int = field(default_factory=lambda: int(os.getenv('REDIS_CONNECT_TIMEOUT', '5')))
        default_ttl: int = field(default_factory=lambda: int(os.getenv('REDIS_DEFAULT_TTL', '300')))

        def __post_init__(self):
            logger.info(f"🔧 Production Redis Config initialized: {self.host}:{self.port}/{self.db}")

    @dataclass
    class ProductionCacheConfig:
        """Production cache configuration with environment variable support"""
        enabled: bool = field(default_factory=lambda: os.getenv('CACHE_ENABLED', 'true').lower() == 'true')
        default_ttl: int = field(default_factory=lambda: int(os.getenv('CACHE_DEFAULT_TTL', '300')))
        max_size: int = field(default_factory=lambda: int(os.getenv('CACHE_MAX_SIZE', '1000')))

        def __post_init__(self):
            logger.info(f"🔧 Production Cache Config initialized: enabled={self.enabled}, ttl={self.default_ttl}")

    @dataclass
    class ProductionConfig:
        """Production configuration using all production config classes"""
        database: ProductionDatabaseConfig = field(default_factory=ProductionDatabaseConfig)
        app: ProductionAppConfig = field(default_factory=ProductionAppConfig)
        redis: ProductionRedisConfig = field(default_factory=ProductionRedisConfig)
        cache: ProductionCacheConfig = field(default_factory=ProductionCacheConfig)

        def __post_init__(self):
            logger.info("🔧 Production Configuration initialized with all production components")

# Forward declaration for type hinting to avoid circular imports if needed
# Not strictly necessary here as models are defined top-down, but good practice.
# from __main__ import ExpertClusterRealm # If ExpertClusterRealm was defined later

class LoadBalancingStrategy(Enum):
    """Advanced load balancing strategies for cluster routing"""
    QUANTUM_WEIGHTED = "QUANTUM_WEIGHTED" # Quantum-inspired weighting
    LEAST_CONNECTIONS = "LEAST_CONNECTIONS" # Route to least busy realm
    RESPONSE_TIME = "RESPONSE_TIME" # Route based on response times
    ROUND_ROBIN = "ROUND_ROBIN" # Simple round-robin
    WEIGHTED_ROUND_ROBIN = "WEIGHTED_ROUND_ROBIN" # Weighted round-robin
    GEOGRAPHICAL = "GEOGRAPHICAL" # Geographic proximity
    ADAPTIVE_LEARNING = "ADAPTIVE_LEARNING" # ML-based routing decisions
    BASKETBALL_OPTIMIZED = "BASKETBALL_OPTIMIZED" # NBA/WNBA specific optimization

class RealmHealthStatus(Enum):
    """Health status classifications for cluster realms"""
    OPTIMAL = "OPTIMAL" # Perfect health
    HEALTHY = "HEALTHY" # Good performance
    DEGRADED = "DEGRADED" # Reduced performance
    CRITICAL = "CRITICAL" # Severe issues
    UNREACHABLE = "UNREACHABLE" # Connection failed
    MAINTENANCE = "MAINTENANCE" # Scheduled maintenance

class PredictionWorkloadType(Enum):
    """Types of prediction workloads for specialized routing"""
    REAL_TIME_ODDS = "REAL_TIME_ODDS" # Live betting odds
    GAME_PREDICTION = "GAME_PREDICTION" # Full game predictions
    PLAYER_PROPS = "PLAYER_PROPS" # Player performance props
    LIVE_ANALYSIS = "LIVE_ANALYSIS" # Live game analysis
    HISTORICAL_ANALYSIS = "HISTORICAL_ANALYSIS" # Historical data analysis
    ML_TRAINING = "ML_TRAINING" # Model training workloads
    BATCH_PROCESSING = "BATCH_PROCESSING" # Bulk data processing

class RealmType(Enum):
    """Types of cluster realms based on their specialization"""
    PREDICTION_FOCUSED = "PREDICTION_FOCUSED" # Optimized for predictions
    ANALYTICS_FOCUSED = "ANALYTICS_FOCUSED" # Optimized for analytics
    HYBRID = "HYBRID" # Balanced capabilities
    TRAINING_FOCUSED = "TRAINING_FOCUSED" # Optimized for ML training

class RealmCapability(Enum):
    """Capabilities that realms can provide"""
    REAL_TIME_PREDICTION = "REAL_TIME_PREDICTION"
    HISTORICAL_ANALYSIS = "HISTORICAL_ANALYSIS"
    PLAYER_ANALYTICS = "PLAYER_ANALYTICS"
    TEAM_ANALYTICS = "TEAM_ANALYTICS"
    LIVE_GAME_TRACKING = "LIVE_GAME_TRACKING"
    ML_MODEL_TRAINING = "ML_MODEL_TRAINING"
    DATA_AGGREGATION = "DATA_AGGREGATION"

@dataclass
class RealmPerformanceMetrics:
    """Performance metrics for a cluster realm"""
    realm_id: str
    response_time: float
    success_rate: float
    active_connections: int
    cpu_utilization: float
    memory_utilization: float
    prediction_accuracy: float
    throughput: float
    error_rate: float
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc)) # Use timezone-aware datetime

@dataclass
class LoadBalancingDecision:
    """Represents a load balancing decision"""
    selected_realm: str
    strategy_used: LoadBalancingStrategy
    decision_factors: Dict[str, float]
    confidence: float
    alternative_realms: List[str]
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc)) # Use timezone-aware datetime

class QuantumLoadBalancer(BaseModel):
    """Quantum-inspired load balancer for cluster realm routing"""
    strategy: LoadBalancingStrategy = Field(default=LoadBalancingStrategy.QUANTUM_WEIGHTED)
    quantum_coherence_factor: float = Field(default=0.8, ge=0.1, le=1.0)
    adaptation_learning_rate: float = Field(default=0.1, ge=0.01, le=0.5)
    performance_history_limit: int = Field(default=1000, ge=100, le=10000)

    # Performance tracking
    realm_performance_history: Dict[str, List[RealmPerformanceMetrics]] = Field(default_factory=dict)
    routing_decisions_history: List[LoadBalancingDecision] = Field(default_factory=list)

    # Quantum parameters (for entanglement and coherence scores)
    quantum_entanglement_matrix: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    realm_coherence_scores: Dict[str, float] = Field(default_factory=dict)

    model_config = { # For Pydantic V2, use model_config instead of Config
        "arbitrary_types_allowed": True
    }

    @oracle_focus
    async def select_optimal_realm(self, available_realms: Dict[str, 'ExpertClusterRealm'],
                                   workload_type: PredictionWorkloadType,
                                   context: Optional[Dict[str, Any]] = None) -> LoadBalancingDecision:
        """Select optimal realm using quantum-inspired load balancing"""
        logger.info(f" Selecting optimal realm for {workload_type.value} workload")

        if not available_realms:
            raise ValueError("No available realms for load balancing")

        context = context or {}

        # Filter healthy realms first
        healthy_realms = {
            realm_id: realm for realm_id, realm in available_realms.items()
            if realm.health_status in [RealmHealthStatus.OPTIMAL, RealmHealthStatus.HEALTHY]
        }

        if not healthy_realms:
            # Fallback to degraded realms if no strictly healthy ones
            logger.warning("No optimal or healthy realms found, falling back to degraded realms.")
            healthy_realms = {
                realm_id: realm for realm_id, realm in available_realms.items()
                if realm.health_status == RealmHealthStatus.DEGRADED
            }

        if not healthy_realms:
            raise ValueError("No healthy or degraded realms available for load balancing")

        # Apply strategy-specific selection
        if self.strategy == LoadBalancingStrategy.QUANTUM_WEIGHTED:
            decision = await self._quantum_weighted_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            decision = await self._least_connections_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.RESPONSE_TIME:
            decision = await self._response_time_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.BASKETBALL_OPTIMIZED:
            decision = await self._basketball_optimized_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.ADAPTIVE_LEARNING:
            decision = await self._adaptive_learning_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.ROUND_ROBIN: # Explicitly handle round-robin
            decision = await self._round_robin_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN: # Added for completeness
            decision = await self._weighted_round_robin_selection(healthy_realms, workload_type, context)
        elif self.strategy == LoadBalancingStrategy.GEOGRAPHICAL: # Added for completeness
             decision = await self._geographical_selection(healthy_realms, workload_type, context)
        else:
            # Fallback to a default if an unknown strategy is set
            logger.warning(f"Unknown strategy '{self.strategy.value}'. Falling back to ROUND_ROBIN.")
            decision = await self._round_robin_selection(healthy_realms, workload_type, context)


        # Store decision for learning
        self.routing_decisions_history.append(decision)

        # Limit history size
        if len(self.routing_decisions_history) > self.performance_history_limit:
            self.routing_decisions_history = self.routing_decisions_history[-self.performance_history_limit:]

        logger.info(f" Selected realm {decision.selected_realm} with {decision.confidence:.2f} confidence using {decision.strategy_used.value}")

        return decision

    async def _quantum_weighted_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                          workload_type: PredictionWorkloadType,
                                          context: Dict[str, Any]) -> LoadBalancingDecision:
        """Quantum-inspired weighted selection algorithm"""
        realm_scores = {}
        decision_factors_map: Dict[str, Dict[str, float]] = {} # Store factors for each realm

        for realm_id, realm in realms.items():
            # Base quantum coherence for the realm
            coherence = self.realm_coherence_scores.get(realm_id, 0.5)

            # Performance factor (from realm's current metrics)
            performance = realm.get_current_performance_score()

            # Workload specialization factor
            specialization = realm.get_workload_specialization_score(workload_type)

            # Quantum entanglement with other realms
            # Pass all realm IDs including current one to ensure `all_realm_ids` has the full set
            entanglement = self._calculate_realm_entanglement(realm_id, set(realms.keys()))

            # Uncertainty principle application
            # Uncertainty increases as coherence decreases. quantum_coherence_factor scales this.
            uncertainty = np.sqrt(1 - coherence**2) * self.quantum_coherence_factor

            # Quantum-inspired score calculation
            quantum_score = (
                coherence * 0.3 +
                performance * 0.3 +
                specialization * 0.2 +
                entanglement * 0.1 +
                uncertainty * 0.1 # Small positive contribution for uncertainty, implying exploration
            )

            realm_scores[realm_id] = quantum_score
            decision_factors_map[realm_id] = {
                'coherence': coherence,
                'performance': performance,
                'specialization': specialization,
                'entanglement': entanglement,
                'uncertainty': uncertainty,
                'quantum_score': quantum_score
            }

        # Select realm with highest quantum score
        selected_realm_id, confidence = max(realm_scores.items(), key=lambda x: x[1])

        # Prepare alternative realms (top 3 alternatives, excluding the selected one)
        sorted_realms = sorted(realm_scores.items(), key=lambda x: x[1], reverse=True)
        alternatives = [realm_id for realm_id, _ in sorted_realms if realm_id != selected_realm_id][:3]

        return LoadBalancingDecision(
            selected_realm=selected_realm_id,
            strategy_used=LoadBalancingStrategy.QUANTUM_WEIGHTED,
            decision_factors=decision_factors_map[selected_realm_id],
            confidence=confidence,
            alternative_realms=alternatives,
            timestamp=datetime.now(timezone.utc) # Use timezone-aware datetime
        )

    async def _basketball_optimized_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                              workload_type: PredictionWorkloadType,
                                              context: Dict[str, Any]) -> LoadBalancingDecision:
        """Basketball-specific optimized selection"""
        realm_scores = {}
        decision_factors_map: Dict[str, Dict[str, float]] = {}

        # Extract basketball context (with sensible defaults)
        league = context.get('league', 'NBA')
        game_importance = context.get('game_importance', 'regular') # regular, playoffs, finals
        time_sensitivity = context.get('time_sensitivity', 'normal') # urgent, normal, batch

        for realm_id, realm in realms.items():
            # Basketball-specific scoring factors
            league_expertise = realm.get_league_expertise_score(league)
            game_context_score = realm.get_game_context_performance(game_importance)
            latency_score = realm.get_latency_score_for_urgency(time_sensitivity)
            basketball_accuracy = realm.get_basketball_prediction_accuracy()

            # Combine scores with basketball weights
            basketball_score = (
                league_expertise * 0.25 +
                game_context_score * 0.25 +
                latency_score * 0.25 +
                basketball_accuracy * 0.25
            )

            realm_scores[realm_id] = basketball_score
            decision_factors_map[realm_id] = {
                'league_expertise': league_expertise,
                'game_context_score': game_context_score,
                'latency_score': latency_score,
                'basketball_accuracy': basketball_accuracy,
                'basketball_score': basketball_score
            }

        selected_realm_id, confidence = max(realm_scores.items(), key=lambda x: x[1])

        sorted_realms = sorted(realm_scores.items(), key=lambda x: x[1], reverse=True)
        alternatives = [realm_id for realm_id, _ in sorted_realms if realm_id != selected_realm_id][:3]

        return LoadBalancingDecision(
            selected_realm=selected_realm_id,
            strategy_used=LoadBalancingStrategy.BASKETBALL_OPTIMIZED,
            decision_factors=decision_factors_map[selected_realm_id],
            confidence=confidence,
            alternative_realms=alternatives,
            timestamp=datetime.now(timezone.utc) # Use timezone-aware datetime
        )

    async def _adaptive_learning_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                           workload_type: PredictionWorkloadType,
                                           context: Dict[str, Any]) -> LoadBalancingDecision:
        """Machine learning-based adaptive selection"""
        # Analyze historical performance for similar workloads
        # Note: Filtering history by 'workload_type.value in str(decision.decision_factors)' is heuristic.
        # A more robust system would store workload_type directly in LoadBalBalancingDecision for easier filtering.
        workload_history = [
            decision for decision in self.routing_decisions_history
            if workload_type.value in str(decision.decision_factors) # This assumes workload_type is in factors
        ]

        realm_scores = {}
        decision_factors_map: Dict[str, Dict[str, float]] = {}

        for realm_id, realm in realms.items():
            # Historical success rate for this workload type
            historical_success = self._calculate_historical_success_rate(realm_id, workload_type)

            # Current performance trends
            performance_trend = self._calculate_performance_trend(realm_id)

            # Learning-based prediction of success
            predicted_performance = self._predict_realm_performance(realm_id, workload_type, context)

            # Adaptive score combining factors
            adaptive_score = (
                historical_success * 0.4 +
                performance_trend * 0.3 +
                predicted_performance * 0.3
            )

            realm_scores[realm_id] = adaptive_score
            decision_factors_map[realm_id] = {
                'historical_success': historical_success,
                'performance_trend': performance_trend,
                'predicted_performance': predicted_performance,
                'adaptive_score': adaptive_score
            }

        selected_realm_id, confidence = max(realm_scores.items(), key=lambda x: x[1])

        sorted_realms = sorted(realm_scores.items(), key=lambda x: x[1], reverse=True)
        alternatives = [realm_id for realm_id, _ in sorted_realms if realm_id != selected_realm_id][:3]

        return LoadBalancingDecision(
            selected_realm=selected_realm_id,
            strategy_used=LoadBalancingStrategy.ADAPTIVE_LEARNING,
            decision_factors=decision_factors_map[selected_realm_id],
            confidence=confidence,
            alternative_realms=alternatives,
            timestamp=datetime.now(timezone.utc) # Use timezone-aware datetime
        )

    async def _least_connections_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                           workload_type: PredictionWorkloadType,
                                           context: Dict[str, Any]) -> LoadBalancingDecision:
        """Select realm with least active connections"""
        realm_connections = {
            realm_id: realm.get_active_connections_count()
            for realm_id, realm in realms.items()
        }

        if not realm_connections: # Handle case where realm_connections might be empty after filtering
            raise ValueError("No realms with connection data available for Least Connections strategy.")

        selected_realm_id = min(realm_connections.items(), key=lambda x: x[1])[0]
        min_connections = realm_connections[selected_realm_id]
        max_connections = max(realm_connections.values()) if realm_connections else 1

        # Calculate confidence based on connection distribution: closer to 1 if selected realm has significantly fewer connections
        confidence = 1.0 - (min_connections / max_connections) if max_connections > 0 else 1.0
        # If min_connections is 0 and max_connections is also 0, confidence should be high (e.g., 1.0)

        decision_factors = {
            'active_connections': float(min_connections), # Ensure float for Pydantic/JSON
            'connection_advantage': float(max_connections - min_connections),
            'confidence': confidence
        }

        sorted_realms = sorted(realm_connections.items(), key=lambda x: x[1])
        alternatives = [realm_id for realm_id, _ in sorted_realms if realm_id != selected_realm_id][:3]

        return LoadBalancingDecision(
            selected_realm=selected_realm_id,
            strategy_used=LoadBalancingStrategy.LEAST_CONNECTIONS,
            decision_factors=decision_factors,
            confidence=confidence,
            alternative_realms=alternatives,
            timestamp=datetime.now(timezone.utc) # Use timezone-aware datetime
        )

    async def _response_time_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                       workload_type: PredictionWorkloadType,
                                       context: Dict[str, Any]) -> LoadBalancingDecision:
        """Select realm with best response time"""
        realm_response_times = {
            realm_id: realm.get_average_response_time()
            for realm_id, realm in realms.items()
        }

        if not realm_response_times: # Handle case where realm_response_times might be empty
            raise ValueError("No realms with response time data available for Response Time strategy.")

        selected_realm_id = min(realm_response_times.items(), key=lambda x: x[1])[0]
        best_time = realm_response_times[selected_realm_id]
        worst_time = max(realm_response_times.values()) if realm_response_times else best_time

        # Calculate confidence based on response time advantage
        # Higher advantage (worst_time - best_time) means higher confidence
        time_advantage = worst_time - best_time
        confidence = min(1.0, time_advantage / worst_time) if worst_time > 0 else 1.0
        # If all response times are 0 or equal, confidence should be 1.0 or based on other factors.

        decision_factors = {
            'response_time': best_time,
            'time_advantage': time_advantage,
            'confidence': confidence
        }

        sorted_realms = sorted(realm_response_times.items(), key=lambda x: x[1])
        alternatives = [realm_id for realm_id, _ in sorted_realms if realm_id != selected_realm_id][:3]

        return LoadBalancingDecision(
            selected_realm=selected_realm_id,
            strategy_used=LoadBalancingStrategy.RESPONSE_TIME,
            decision_factors=decision_factors,
            confidence=confidence,
            alternative_realms=alternatives,
            timestamp=datetime.now(timezone.utc) # Use timezone-aware datetime
        )

    async def _round_robin_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                     workload_type: PredictionWorkloadType,
                                     context: Dict[str, Any]) -> LoadBalancingDecision:
        """Simple round-robin selection"""
        realm_ids = sorted(list(realms.keys())) # Ensure consistent ordering and copy

        if not realm_ids:
            raise ValueError("No realms available for Round Robin selection.")

        # This simple round-robin uses a fixed state (not truly stateful within the class, but within the function scope)
        # For a truly stateful round-robin, you'd need to store an index within the QuantumLoadBalancer instance.
        # For demo purposes, using hash of time makes it "deterministic" per minute, but not classic round-robin.
        # A more traditional round robin would involve a `self._last_round_robin_index` and incrementing it.
        # Sticking to the provided hash-based approach for consistency with original code intent,
        # but noting it's not a true round-robin.
        current_time_hash = hash(datetime.now(timezone.utc).strftime('%Y-%m-%d-%H-%M-%S')) # Include seconds for more granularity
        selected_index = current_time_hash % len(realm_ids)
        selected_realm_id = realm_ids[selected_index]

        decision_factors = {
            'selection_method': 'round_robin',
            'realm_index': float(selected_index), # Ensure float for Pydantic/JSON
            'total_realms': float(len(realm_ids)) # Ensure float for Pydantic/JSON
        }

        alternatives = [realm_id for realm_id in realm_ids if realm_id != selected_realm_id][:3]

        return LoadBalancingDecision(
            selected_realm=selected_realm_id,
            strategy_used=LoadBalancingStrategy.ROUND_ROBIN,
            decision_factors=decision_factors,
            confidence=0.5, # Medium confidence for round-robin as it doesn't optimize
            alternative_realms=alternatives,
            timestamp=datetime.now(timezone.utc) # Use timezone-aware datetime
        )

    # Adding stub for Weighted Round Robin
    async def _weighted_round_robin_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                              workload_type: PredictionWorkloadType,
                                              context: Dict[str, Any]) -> LoadBalancingDecision:
        """Weighted round-robin selection (stub)"""
        # In a full implementation, realms would have weights, and you'd cycle based on those.
        logger.warning("Weighted Round Robin not fully implemented, falling back to Round Robin logic.")
        return await self._round_robin_selection(realms, workload_type, context)

    # Adding stub for Geographical selection
    async def _geographical_selection(self, realms: Dict[str, 'ExpertClusterRealm'],
                                     workload_type: PredictionWorkloadType,
                                     context: Dict[str, Any]) -> LoadBalancingDecision:
        """Geographical selection (stub)"""
        # In a full implementation, realms and context would have location data.
        logger.warning("Geographical selection not fully implemented, falling back to Round Robin logic.")
        return await self._round_robin_selection(realms, workload_type, context)

    def _calculate_realm_entanglement(self, realm_id: str, all_realm_ids: Set[str]) -> float:
        """Calculate quantum entanglement score with other realms"""
        if realm_id not in self.quantum_entanglement_matrix:
            return 0.5 # Default entanglement if not explicitly defined

        entanglement_scores = []
        for other_realm_id in all_realm_ids:
            if other_realm_id != realm_id:
                # Ensure realm_id is a key in the matrix before accessing its inner dict
                if realm_id in self.quantum_entanglement_matrix:
                    entanglement = self.quantum_entanglement_matrix[realm_id].get(other_realm_id, 0.5)
                    entanglement_scores.append(entanglement)

        # Handle case where entanglement_scores might be empty
        return np.mean(entanglement_scores) if entanglement_scores else 0.5

    def _calculate_historical_success_rate(self, realm_id: str, workload_type: PredictionWorkloadType) -> float:
        """Calculate historical success rate for specific workload type"""
        relevant_decisions = [
            decision for decision in self.routing_decisions_history
            if decision.selected_realm == realm_id and
            workload_type.value in str(decision.decision_factors) # Heuristic check for workload type in factors
        ]

        if not relevant_decisions:
            return 0.5 # Default score if no relevant history
        
        # Filter for float values as confidence and ensure not empty
        confidence_values = [d.confidence for d in relevant_decisions if isinstance(d.confidence, (int, float))]
        if not confidence_values:
            return 0.5 # Default score if no valid confidence values

        avg_confidence = np.mean(confidence_values)
        return float(avg_confidence) # Ensure float return

    def _calculate_performance_trend(self, realm_id: str) -> float:
        """Calculate recent performance trend for realm"""
        if realm_id not in self.realm_performance_history:
            return 0.5 # Default score if no performance history

        recent_metrics = self.realm_performance_history[realm_id][-10:] # Last 10 metrics
        if len(recent_metrics) < 2:
            return 0.5 # Need at least two points to calculate a trend

        # Calculate trend in success rate
        success_rates = [metric.success_rate for metric in recent_metrics]
        # Use polyfit to get slope; handles cases where data might be constant
        trend = np.polyfit(range(len(success_rates)), success_rates, 1)[0] # Linear trend coefficient

        # Normalize trend to 0-1 range. Assuming trend can be between -1 (bad) and 1 (good).
        normalized_trend = (trend + 1) / 2
        return np.clip(normalized_trend, 0.0, 1.0)

    def _predict_realm_performance(self, realm_id: str, workload_type: PredictionWorkloadType,
                                   context: Dict[str, Any]) -> float:
        """Predict realm performance using simple ML-like logic"""
        if realm_id not in self.realm_performance_history:
            return 0.5 # Default prediction if no performance history

        # Use recent performance metrics
        recent_metrics = self.realm_performance_history[realm_id][-5:]
        if not recent_metrics:
            return 0.5 # Default if no recent metrics

        # Simple performance prediction based on historical averages
        avg_response_time = np.mean([m.response_time for m in recent_metrics])
        avg_success_rate = np.mean([m.success_rate for m in recent_metrics])
        avg_cpu = np.mean([m.cpu_utilization for m in recent_metrics])

        # Combine factors for prediction (ensure division by zero not an issue)
        # Lower response time is better, lower CPU usage is better
        predicted_score = (
            (1.0 - min(avg_response_time / 10.0, 1.0)) * 0.4 + # Assuming max response time 10 for normalization
            avg_success_rate * 0.4 +
            (1.0 - avg_cpu) * 0.2
        )

        return np.clip(predicted_score, 0.0, 1.0)

class ExpertClusterRealm(BaseModel):
    """Expert-level cluster realm with advanced capabilities"""
    realm_id: str = Field(..., description="Unique identifier for the realm")
    realm_name: str = Field(..., description="Human-readable name for the realm")
    endpoint_url: str = Field(..., description="Primary endpoint URL")
    failover_endpoints: List[str] = Field(default_factory=list, description="Backup endpoint URLs")
    encryption_key: str = Field(..., description="Encryption key for secure communication")

    # Realm classification
    realm_type: RealmType = Field(default=RealmType.HYBRID)
    capabilities: List[RealmCapability] = Field(default_factory=list)

    # Core configuration
    max_connections: int = Field(default=100, ge=1, le=10000)
    timeout_seconds: float = Field(default=30.0, ge=1.0, le=300.0)
    retry_attempts: int = Field(default=3, ge=0, le=10)

    # Health and performance
    health_status: RealmHealthStatus = Field(default=RealmHealthStatus.HEALTHY)
    current_connections: int = Field(default=0, ge=0)
    last_health_check: Optional[datetime] = Field(default=None)

    # Specialization scores (defaults to empty dicts)
    league_expertise: Dict[str, float] = Field(default_factory=dict) # NBA: 0.9, WNBA: 0.8, etc.
    workload_specialization: Dict[str, float] = Field(default_factory=dict)
    performance_metrics: Dict[str, float] = Field(default_factory=dict)

    # Advanced features
    auto_scaling_enabled: bool = Field(default=False)
    basketball_optimization_level: float = Field(default=0.5, ge=0.0, le=1.0)
    quantum_coherence_level: float = Field(default=0.5, ge=0.0, le=1.0)

    model_config = { # For Pydantic V2, use model_config instead of Config
        "arbitrary_types_allowed": True
    }

    @oracle_focus
    def get_current_performance_score(self) -> float:
        """Get overall current performance score (0.0 to 1.0)"""
        # Combine various performance metrics
        # Lower response_time is better, higher success_rate is better, lower cpu_utilization is better
        response_time_score = 1.0 - min(self.performance_metrics.get('avg_response_time', 5.0) / 10.0, 1.0)
        success_rate = self.performance_metrics.get('success_rate', 0.8)
        cpu_score = 1.0 - self.performance_metrics.get('cpu_utilization', 0.5)
        
        # Avoid division by zero for connection_score if max_connections is 0 or less
        connection_score = 1.0 - min(self.current_connections / self.max_connections, 1.0) if self.max_connections > 0 else 0.0

        overall_score = (
            response_time_score * 0.3 +
            success_rate * 0.3 +
            cpu_score * 0.2 +
            connection_score * 0.2
        )

        return np.clip(overall_score, 0.0, 1.0)

    @oracle_focus
    def get_workload_specialization_score(self, workload_type: PredictionWorkloadType) -> float:
        """Get specialization score for specific workload type (0.0 to 1.0)"""
        # Default to 0.5 if no specific specialization is defined
        return self.workload_specialization.get(workload_type.value, 0.5)

    @oracle_focus
    def get_league_expertise_score(self, league: str) -> float:
        """Get expertise score for specific basketball league (0.0 to 1.0)"""
        # Normalize league string to uppercase for consistency
        return self.league_expertise.get(league.upper(), 0.5)

    @oracle_focus
    def get_game_context_performance(self, game_importance: str) -> float:
        """Get performance score for specific game context (0.0 to 1.0)"""
        context_scores = {
            'regular': 0.8,
            'playoffs': 0.9,
            'finals': 1.0
        }
        base_score = context_scores.get(game_importance, 0.8)
        # Adjust base score by realm's basketball optimization level
        return base_score * self.basketball_optimization_level

    @oracle_focus
    def get_latency_score_for_urgency(self, time_sensitivity: str) -> float:
        """Get latency score based on time sensitivity requirements (0.0 to 1.0)"""
        avg_response_time = self.performance_metrics.get('avg_response_time', 5.0)
        urgency_thresholds = {
            'urgent': 2.0, # Must respond within 2 seconds
            'normal': 5.0, # Normal 5 second threshold
            'batch': 30.0 # Batch processing can be slower
        }
        threshold = urgency_thresholds.get(time_sensitivity, 5.0)

        # Handle division by zero for threshold
        if threshold <= 0:
            return 0.0 # Cannot determine meaningful score if threshold is invalid

        if avg_response_time <= threshold:
            # If within threshold, higher score for faster response
            return 1.0 - (avg_response_time / threshold) * 0.3 # Penalize lightly if close to threshold
        else:
            # If over threshold, penalize more heavily
            return 0.7 * (threshold / avg_response_time) # Score drops significantly if avg_response_time is high

    @oracle_focus
    def get_basketball_prediction_accuracy(self) -> float:
        """Get basketball-specific prediction accuracy (0.0 to 1.0)"""
        return self.performance_metrics.get('basketball_accuracy', 0.7)

    @oracle_focus
    def get_active_connections_count(self) -> int:
        """Get current active connections count"""
        return self.current_connections

    @oracle_focus
    def get_average_response_time(self) -> float:
        """Get average response time"""
        return self.performance_metrics.get('avg_response_time', 5.0)

    @oracle_focus
    async def update_performance_metrics(self, metrics: RealmPerformanceMetrics):
        """Update realm performance metrics and health status"""
        # Ensure that `metrics.realm_id` matches `self.realm_id` if this method is called externally
        if metrics.realm_id != self.realm_id:
            logger.warning(f"Attempted to update metrics for {self.realm_id} with metrics for {metrics.realm_id}. Skipping.")
            return

        self.performance_metrics.update({
            'avg_response_time': metrics.response_time,
            'success_rate': metrics.success_rate,
            'cpu_utilization': metrics.cpu_utilization,
            'memory_utilization': metrics.memory_utilization,
            'basketball_accuracy': metrics.prediction_accuracy,
            'throughput': metrics.throughput,
            'error_rate': metrics.error_rate
        })
        self.current_connections = metrics.active_connections
        self.last_health_check = metrics.last_updated

        # Update health status based on the new metrics
        self._update_health_status(metrics)
        logger.info(f"Realm {self.realm_id} performance metrics updated. New status: {self.health_status.value}")

    def _update_health_status(self, metrics: RealmPerformanceMetrics):
        """Update health status based on performance metrics"""
        if metrics.error_rate > 0.2 or metrics.cpu_utilization > 0.9 or metrics.response_time > 15.0:
            self.health_status = RealmHealthStatus.CRITICAL
        elif metrics.error_rate > 0.1 or metrics.cpu_utilization > 0.8 or metrics.response_time > 10.0:
            self.health_status = RealmHealthStatus.DEGRADED
        elif metrics.success_rate > 0.95 and metrics.response_time < 2.0 and metrics.cpu_utilization < 0.6:
            self.health_status = RealmHealthStatus.OPTIMAL
        else:
            self.health_status = RealmHealthStatus.HEALTHY

class ExpertClusterManager(BaseModel):
    """Expert-level cluster management system"""
    cluster_name: str = Field(..., description="Name of the cluster")
    load_balancer: QuantumLoadBalancer = Field(default_factory=QuantumLoadBalancer)
    realms: Dict[str, ExpertClusterRealm] = Field(default_factory=dict)

    # Configuration
    health_check_interval: float = Field(default=30.0, ge=5.0, le=300.0)
    auto_discovery_enabled: bool = Field(default=True)
    failover_threshold: float = Field(default=0.8, ge=0.1, le=1.0) # Threshold for triggering failover

    # Monitoring
    cluster_metrics: Dict[str, Any] = Field(default_factory=dict)
    alert_history: List[Dict[str, Any]] = Field(default_factory=list)

    model_config = { # For Pydantic V2
        "arbitrary_types_allowed": True
    }

    # Background tasks management
    _health_check_task: Optional[asyncio.Task] = None
    _running: bool = False

    async def startup(self):
        """Starts background tasks for the cluster manager."""
        logger.info(f"Starting cluster manager for {self.cluster_name}...")
        self._running = True
        self._health_check_task = asyncio.create_task(self._periodic_health_check())
        logger.info(f"Cluster manager for {self.cluster_name} started.")

    async def shutdown(self):
        """Stops background tasks and performs cleanup for the cluster manager."""
        logger.info(f"Shutting down cluster manager for {self.cluster_name}...")
        self._running = False
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                logger.info("Health check task cancelled.")
        logger.info(f"Cluster manager for {self.cluster_name} shutdown complete.")

    async def _periodic_health_check(self):
        """Internal task to periodically perform health checks on all realms."""
        while self._running:
            try:
                await self.health_check_all_realms()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                logger.info("Periodic health check task received cancellation request.")
                break # Exit the loop cleanly
            except Exception as e:
                logger.error(f"Error in periodic health check task: {e}")
                await asyncio.sleep(self.health_check_interval * 2) # Back off on error

    @oracle_focus
    async def route_prediction_request(self, workload_type: PredictionWorkloadType,
                                       context: Optional[Dict[str, Any]] = None) -> str:
        """Route a prediction request to the optimal realm"""
        logger.info(f" Routing {workload_type.value} request")

        if not self.realms:
            raise ValueError("No realms available in cluster to route request.")

        # Get healthy and degraded realms to consider
        available_realms = {
            realm_id: realm for realm_id, realm in self.realms.items()
            if realm.health_status != RealmHealthStatus.UNREACHABLE and
               realm.health_status != RealmHealthStatus.MAINTENANCE # Exclude realms in maintenance
        }

        if not available_realms:
            raise ValueError("No available realms (healthy, degraded) in cluster to route request.")

        # Use load balancer to select optimal realm
        try:
            decision = await self.load_balancer.select_optimal_realm(
                available_realms, workload_type, context
            )
            logger.info(f" Routed to realm: {decision.selected_realm} (Strategy: {decision.strategy_used.value})")
            return decision.selected_realm
        except ValueError as e:
            logger.error(f"Load balancing failed for {workload_type.value}: {e}. No realm selected.")
            raise # Re-raise the error to the caller

    @oracle_focus
    async def register_realm(self, realm: ExpertClusterRealm):
        """Register a new realm with the cluster"""
        if realm.realm_id in self.realms:
            logger.warning(f"Realm {realm.realm_id} already registered. Updating existing entry.")
        self.realms[realm.realm_id] = realm
        logger.info(f"➕ Registered/Updated realm {realm.realm_id} to cluster {self.cluster_name}")

    @oracle_focus
    async def unregister_realm(self, realm_id: str):
        """Unregister a realm from the cluster"""
        if realm_id in self.realms:
            del self.realms[realm_id]
            logger.info(f"➖ Unregistered realm {realm_id} from cluster {self.cluster_name}")
        else:
            logger.warning(f"Attempted to unregister non-existent realm: {realm_id}")

    @oracle_focus
    async def health_check_all_realms(self):
        """Perform health checks on all realms"""
        logger.info(f"🏥 Performing health checks on {len(self.realms)} realms in {self.cluster_name}")

        health_check_tasks = []
        for realm_id, realm in self.realms.items():
            task = asyncio.create_task(self._check_and_update_realm_health(realm_id, realm))
            health_check_tasks.append(task)
        
        # Wait for all health checks to complete
        await asyncio.gather(*health_check_tasks, return_exceptions=True)
        
        # After checks, update overall cluster metrics
        self.cluster_metrics = self.get_cluster_analytics()

    async def _check_and_update_realm_health(self, realm_id: str, realm: ExpertClusterRealm):
        """Performs health check for a single realm and updates its status."""
        try:
            # Simulate health check (in production, make actual HTTP requests to realm's health endpoint)
            # For now, generate mock performance metrics and update the realm
            mock_metrics = RealmPerformanceMetrics(
                realm_id=realm_id,
                response_time=np.random.uniform(1.0, 10.0),
                success_rate=np.random.uniform(0.7, 0.99),
                active_connections=np.random.randint(0, realm.max_connections * 0.8),
                cpu_utilization=np.random.uniform(0.1, 0.95),
                memory_utilization=np.random.uniform(0.1, 0.9),
                prediction_accuracy=np.random.uniform(0.6, 0.95),
                throughput=np.random.uniform(100, 1000),
                error_rate=np.random.uniform(0.0, 0.15),
                last_updated=datetime.now(timezone.utc)
            )
            await realm.update_performance_metrics(mock_metrics)

            if realm.health_status in [RealmHealthStatus.CRITICAL, RealmHealthStatus.UNREACHABLE]:
                logger.warning(f"🚨 Realm {realm_id} is in {realm.health_status.value} state. Initiating failover procedures.")
                await self._handle_realm_failure(realm_id)
            elif realm.health_status == RealmHealthStatus.DEGRADED:
                logger.warning(f" Realm {realm_id} is DEGRADED. Monitoring closely.")
            else:

        except Exception as e:
            logger.error(f"Health check failed for realm {realm_id}: {e}")
            if realm.health_status != RealmHealthStatus.UNREACHABLE: # Only update if not already unreachable
                realm.health_status = RealmHealthStatus.UNREACHABLE
                await self._handle_realm_failure(realm_id)


    async def _handle_realm_failure(self, realm_id: str):
        """Handle realm failure, including logging and potential failover/recovery actions"""
        logger.critical(f"🔥 Realm {realm_id} has failed or is critical. Implementing failover procedures.")

        # Log alert
        alert = {
            'timestamp': datetime.now(timezone.utc).isoformat(), # Use timezone-aware datetime
            'type': 'REALM_FAILURE',
            'realm_id': realm_id,
            'cluster': self.cluster_name,
            'current_realm_status': self.realms[realm_id].health_status.value if realm_id in self.realms else "UNKNOWN"
        }
        self.alert_history.append(alert)

        # Trigger actual failover logic
        # This is where you would spin up new instances, redirect traffic away,
        # update DNS records, or notify other services.
        logger.info(f"Initiating traffic redirection for {realm_id} and alerting ops.")
        # Example: if auto_scaling_enabled, trigger scaling event
        if realm_id in self.realms and self.realms[realm_id].auto_scaling_enabled:
            logger.info(f"Auto-scaling enabled for {realm_id}. Triggering scale-out action.")
            # In a real system, call an auto-scaling API here (e.g., Kubernetes, AWS Auto Scaling)
            pass

    @oracle_focus
    def get_cluster_analytics(self) -> Dict[str, Any]:
        """Get comprehensive cluster analytics"""
        total_realms = len(self.realms)
        
        if total_realms == 0:
            return {
                'cluster_name': self.cluster_name,
                'total_realms': 0,
                'healthy_realms': 0,
                'health_percentage': 0,
                'average_performance': 0.0,
                'load_balancing_strategy': self.load_balancer.strategy.value,
                'recent_alerts': len([a for a in self.alert_history if datetime.fromisoformat(a['timestamp']) > datetime.now(timezone.utc) - timedelta(hours=24)]),
                'routing_decisions_count': len(self.load_balancer.routing_decisions_history)
            }

        healthy_realms_count = len([r for r in self.realms.values()
                                    if r.health_status in [RealmHealthStatus.OPTIMAL, RealmHealthStatus.HEALTHY]])
        
        # Calculate average performance only for realms that have performance metrics
        realms_with_metrics = [realm for realm in self.realms.values() if realm.performance_metrics]
        avg_performance = np.mean([realm.get_current_performance_score()
                                   for realm in realms_with_metrics]) if realms_with_metrics else 0.0

        return {
            'cluster_name': self.cluster_name,
            'total_realms': total_realms,
            'healthy_realms': healthy_realms_count,
            'health_percentage': (healthy_realms_count / total_realms * 100) if total_realms > 0 else 0,
            'average_performance': avg_performance,
            'load_balancing_strategy': self.load_balancer.strategy.value,
            'recent_alerts': len([a for a in self.alert_history
                                  if datetime.fromisoformat(a['timestamp']).replace(tzinfo=timezone.utc) > datetime.now(timezone.utc) - timedelta(hours=24)]),
            'routing_decisions_count': len(self.load_balancer.routing_decisions_history)
        }

# Factory functions for easy setup
@oracle_focus
def create_basketball_optimized_cluster(cluster_name: str) -> ExpertClusterManager:
    """Create a cluster optimized for basketball predictions"""
    load_balancer = QuantumLoadBalancer(
        strategy=LoadBalancingStrategy.BASKETBALL_OPTIMIZED,
        quantum_coherence_factor=0.9,
        adaptation_learning_rate=0.15
    )
    cluster = ExpertClusterManager(
        cluster_name=cluster_name,
        load_balancer=load_balancer,
        auto_discovery_enabled=True,
        failover_threshold=0.9
    )
    logger.info(f" Created basketball-optimized cluster: {cluster_name}")
    return cluster

@oracle_focus
def create_real_time_cluster(cluster_name: str) -> ExpertClusterManager:
    """Create a cluster optimized for real-time predictions"""
    load_balancer = QuantumLoadBalancer(
        strategy=LoadBalancingStrategy.RESPONSE_TIME,
        quantum_coherence_factor=0.7,
        adaptation_learning_rate=0.2
    )
    cluster = ExpertClusterManager(
        cluster_name=cluster_name,
        load_balancer=load_balancer,
        health_check_interval=10.0, # More frequent health checks
        failover_threshold=0.95
    )
    logger.info(f" Created real-time optimized cluster: {cluster_name}")
    return cluster

@oracle_focus
def create_adaptive_learning_cluster(cluster_name: str) -> ExpertClusterManager:
    """Create a cluster with machine learning-based routing"""
    load_balancer = QuantumLoadBalancer(
        strategy=LoadBalancingStrategy.ADAPTIVE_LEARNING,
        quantum_coherence_factor=0.85,
        adaptation_learning_rate=0.1,
        performance_history_limit=2000 # Larger history for better learning
    )
    cluster = ExpertClusterManager(
        cluster_name=cluster_name,
        load_balancer=load_balancer,
        auto_discovery_enabled=True
    )
    logger.info(f" Created adaptive learning cluster: {cluster_name}")
    return cluster

# Example usage and testing
if __name__ == "__main__":
    # Configure logging for better visibility during testing
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.setLevel(logging.DEBUG) # Set to DEBUG for detailed output of internal workings

    async def demo_expert_cluster_management():
        """Demonstrate expert-level cluster management capabilities"""

        # Create expert cluster manager
        manager = ExpertClusterManager(cluster_name="demo_cluster")
        # Start the background health check task
        await manager.startup()

        # Create sample realms
        realm1 = ExpertClusterRealm(
            realm_id="atlantis",
            realm_name="Atlantis Prediction Realm",
            endpoint_url="https://atlantis.oracle.com",
            encryption_key="demo_key_atlantis_123456789012345",
            realm_type=RealmType.PREDICTION_FOCUSED,
            capabilities=[RealmCapability.REAL_TIME_PREDICTION, RealmCapability.PLAYER_ANALYTICS]
        )
        realm2 = ExpertClusterRealm(
            realm_id="elysium",
            realm_name="Elysium Analytics Realm",
            endpoint_url="https://elysium.oracle.com",
            encryption_key="demo_key_elysium_123456789012345",
            realm_type=RealmType.ANALYTICS_FOCUSED,
            capabilities=[RealmCapability.HISTORICAL_ANALYSIS, RealmCapability.TEAM_ANALYTICS]
        )
        realm3 = ExpertClusterRealm(
            realm_id="olympus",
            realm_name="Olympus Hybrid Realm",
            endpoint_url="https://olympus.oracle.com",
            encryption_key="demo_key_olympus_123456789012345",
            realm_type=RealmType.HYBRID,
            capabilities=[RealmCapability.LIVE_GAME_TRACKING, RealmCapability.DATA_AGGREGATION],
            max_connections=200, # Higher capacity
            basketball_optimization_level=0.95 # Highly optimized for basketball
        )


        # Register realms
        await manager.register_realm(realm1)
        await manager.register_realm(realm2)
        await manager.register_realm(realm3)

        # Simulate some initial performance metrics for realms for better load balancing decisions
        await realm1.update_performance_metrics(RealmPerformanceMetrics(
            realm_id="atlantis", response_time=2.5, success_rate=0.98, active_connections=10,
            cpu_utilization=0.3, memory_utilization=0.4, prediction_accuracy=0.90, throughput=500, error_rate=0.01
        ))
        await realm2.update_performance_metrics(RealmPerformanceMetrics(
            realm_id="elysium", response_time=5.0, success_rate=0.85, active_connections=50,
            cpu_utilization=0.7, memory_utilization=0.8, prediction_accuracy=0.75, throughput=200, error_rate=0.05
        ))
        await realm3.update_performance_metrics(RealmPerformanceMetrics(
            realm_id="olympus", response_time=1.2, success_rate=0.99, active_connections=5,
            cpu_utilization=0.2, memory_utilization=0.3, prediction_accuracy=0.98, throughput=800, error_rate=0.005
        ))

        # Allow health checks to run once to update initial status
        await asyncio.sleep(manager.health_check_interval / 2) # Wait half interval for first check
        # For direct testing, you might call manager.health_check_all_realms() here

        # Demonstrate load balancing with various strategies
        strategies = [
            LoadBalancingStrategy.QUANTUM_WEIGHTED,
            LoadBalancingStrategy.LEAST_CONNECTIONS,
            LoadBalancingStrategy.RESPONSE_TIME,
            LoadBalancingStrategy.BASKETBALL_OPTIMIZED,
            LoadBalancingStrategy.ADAPTIVE_LEARNING,
            LoadBalancingStrategy.ROUND_ROBIN
        ]

        for strategy in strategies:
            manager.load_balancer.strategy = strategy
            try:
                decision = await manager.load_balancer.select_optimal_realm(
                    manager.realms,
                    PredictionWorkloadType.GAME_PREDICTION,
                    {"league": "NBA", "game_importance": "playoffs", "time_sensitivity": "urgent"}
                )
                logger.info(f"Strategy {strategy.value}: Selected realm {decision.selected_realm} with confidence {decision.confidence:.2f}")
            except ValueError as e:
                logger.error(f"Strategy {strategy.value}: {e}")

        # Simulate a realm going critical/unreachable
        await realm2.update_performance_metrics(RealmPerformanceMetrics(
            realm_id="elysium", response_time=30.0, success_rate=0.2, active_connections=100,
            cpu_utilization=0.98, memory_utilization=0.99, prediction_accuracy=0.1, throughput=10, error_rate=0.5
        ))
        # Give some time for the periodic health check to detect this
        await asyncio.sleep(manager.health_check_interval + 1) # Wait for next health check cycle

        # Check cluster analytics after failure
        analytics = manager.get_cluster_analytics()
        if analytics['recent_alerts'] > 0:


        # Demonstrate routing after a realm is critical
        try:
            decision_after_failure = await manager.route_prediction_request(
                PredictionWorkloadType.GAME_PREDICTION,
                {"league": "NBA"}
            )
        except ValueError as e:


        # Shutdown the cluster manager
        await manager.shutdown()

    # Run the demo
    asyncio.run(demo_expert_cluster_management())
