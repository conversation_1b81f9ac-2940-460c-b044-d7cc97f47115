import logging
from typing import Dict, Any, Optional
from src.model_forge.UnifiedModelForge import UnifiedModelForge
from src.model_forge.FateArchetypeStrategy import FateArchetypeStrategy

"""
FateForge_Expert.py
==================

DEPRECATED: FateForge is now part of UnifiedModelForge with FateArchetypeStrategy.
This module provides backward compatibility by redirecting to the unified system.

Author: Cognitive Spires Expert System
"""


logger = logging.getLogger(__name__)

# Import the unified model forge system
try:
    UNIFIED_FORGE_AVAILABLE = True
    logger.info("🧠 MEDUSA VAULT: Successfully imported unified model forge system")
except ImportError as e:
    logger.warning(f"🧠 MEDUSA VAULT: Unified model forge unavailable: {e}")
    UNIFIED_FORGE_AVAILABLE = False

class FateForge_Expert:
    """
    DEPRECATED: FateForge_Expert is now part of UnifiedModelForge.

    This class provides backward compatibility by wrapping the unified system.
    Use UnifiedModelForge with FateArchetypeStrategy for new implementations.
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize FateForge_Expert with backward compatibility."""
        self.config = config or {}
        self.logger = logger.getChild(self.__class__.__name__)

        if UNIFIED_FORGE_AVAILABLE:
            # Use the unified system with FateArchetypeStrategy
            self.strategy = FateArchetypeStrategy()
            self.forge = UnifiedModelForge(strategy=self.strategy)
            self.logger.info("🧠 MEDUSA VAULT: FateForge_Expert initialized with unified system")
        else:
            # Fallback mode
            self.forge = None
            self.strategy = None
            self.logger.warning("🧠 MEDUSA VAULT: FateForge_Expert using fallback mode")

    def forge_model(self, model_type: str = "fate", **kwargs) -> Dict[str, Any]:
        """
        Forge a model using fate-based strategies.

        Args:
            model_type: Type of model to forge
            **kwargs: Additional parameters

        Returns:
            Dictionary containing model information
        """
        if self.forge:
            try:
                return self.forge.create_model(model_type=model_type, **kwargs)
            except Exception as e:
                self.logger.error(f"🧠 MEDUSA VAULT: Unified forge failed: {e}")
                return self._fallback_forge(model_type, **kwargs)
        else:
            return self._fallback_forge(model_type, **kwargs)

    def _fallback_forge(self, model_type: str, **kwargs) -> Dict[str, Any]:
        """Fallback model forging when unified system is unavailable."""
        self.logger.warning("🧠 MEDUSA VAULT: Using fallback fate forging")
        return {
            'model_type': model_type,
            'strategy': 'fate_fallback',
            'status': 'fallback_mode',
            'config': kwargs,
            'timestamp': str(logger.handlers[0].formatter.formatTime(logger.makeRecord('', 0, '', 0, '', (), None)) if logger.handlers else 'unknown')
        }

    def get_forge_status(self) -> Dict[str, Any]:
        """Get the current status of the forge."""
        return {
            'unified_available': UNIFIED_FORGE_AVAILABLE,
            'forge_active': self.forge is not None,
            'strategy_type': 'FateArchetypeStrategy' if self.strategy else 'fallback',
            'config': self.config
        }

# Backward compatibility alias
FateForge = FateForge_Expert

logger.info("🧠 MEDUSA VAULT: FateForge_Expert (deprecated compatibility module) loaded")