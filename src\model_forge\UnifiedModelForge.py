import logging
from typing import Dict, Any, Optional, Type
from datetime import datetime
from src.model_forge.ModelArchetypeStrategy import ModelArchetypeStrategy
from src.model_forge.DivineArchetypeStrategy import DivineArchetypeStrategy
from src.model_forge.HephaestusArchetypeStrategy import HephaestusArchetypeStrategy
from src.model_forge.FateArchetypeStrategy import FateArchetypeStrategy

logger = logging.getLogger("UnifiedModelForge")

# Import additional strategies from model_archetype_strategy.py
try:
    from src.models.model_archetype_strategy import (
        NeuralNetworkStrategy as BaseNeuralNetworkStrategy,
        RandomForestStrategy,
        GradientBoostingStrategy,
        SVMStrategy,
        LogisticRegressionStrategy
    )
    MODEL_ARCHETYPE_STRATEGIES_AVAILABLE = True
except ImportError:
    MODEL_ARCHETYPE_STRATEGIES_AVAILABLE = False
    BaseNeuralNetworkStrategy = None
    RandomForestStrategy = None
    GradientBoostingStrategy = None
    SVMStrategy = None
    LogisticRegressionStrategy = None

try:
    from src.models.model_archetype_strategy import (
        EnsembleStrategy as BaseEnsembleStrategy,
        TransformerStrategy as BaseTransformerStrategy,
        QuantumEnhancedStrategy as BaseQuantumEnhancedStrategy,
        HybridStrategy as BaseHybridStrategy
    )
    ADDITIONAL_STRATEGIES_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Additional strategies not available: {e}")
    ADDITIONAL_STRATEGIES_AVAILABLE = False
    BaseEnsembleStrategy = None
    BaseTransformerStrategy = None
    BaseQuantumEnhancedStrategy = None
    BaseHybridStrategy = None

# Adapter classes to bridge the interface gap
class StrategyAdapter(ModelArchetypeStrategy):
    """Adapter to bridge create_model() to forge() interface"""

    def __init__(self, base_strategy):
        self.base_strategy = base_strategy

    def forge(self, input_dim: int, **kwargs) -> Any:
        """Adapt forge() call to create_model()"""
        config = {'input_size': input_dim, **kwargs}
        return self.base_strategy.create_model(config)

    def retrain(self, model: Any, data: Any, **kwargs) -> Any:
        """Basic retrain implementation"""
        # For now, just return the model - could be enhanced later
        logger.info(f"Retrain called for {type(self.base_strategy).__name__}")
        return model

    def get_status(self) -> Dict[str, Any]:
        """Get strategy status"""
        return {
            'strategy_type': type(self.base_strategy).__name__,
            'archetype': getattr(self.base_strategy, 'archetype', 'unknown')
        }

"""
UnifiedModelForge.py
===================
A unified, intelligent, self-healing model forge for the HYPER MEDUSA NEURAL VAULT.
This system centralizes all model creation, retraining, meta-learning, and feedback logic.
Supports multiple archetypes/strategies (Divine, Hephaestus, Quantum, etc.) via plugins.
"""


logger = logging.getLogger("UnifiedModelForge")

class UnifiedModelForge:
    """
    Centralized, intelligent model forge supporting multiple archetypes.
    Handles model creation, retraining, feedback, and meta-learning.
    """
    def __init__(self):
        self.strategies: Dict[str, ModelArchetypeStrategy] = {}
        self.model_registry: Dict[str, Dict[str, Any]] = {}
        self.active_models: Dict[str, Any] = {}
        self.feedback_log = []
        logger.info("UnifiedModelForge initialized.")

    def register_strategy(self, name: str, strategy: ModelArchetypeStrategy):
        self.strategies[name] = strategy
        logger.info(f"Registered model archetype strategy: {name}")

    def forge_model(self, archetype: str, input_dim: int, **kwargs) -> Any:
        if archetype not in self.strategies:
            raise ValueError(f"Unknown archetype: {archetype}")
        model = self.strategies[archetype].forge(input_dim, **kwargs)
        model_id = f"{archetype}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        self.model_registry[model_id] = {
            'archetype': archetype,
            'created_at': datetime.now(),
            'model': model
        }
        self.active_models[archetype] = model
        logger.info(f"Forged new model: {model_id}")
        return model

    def retrain_model(self, archetype: str, model: Any, data: Any, **kwargs) -> Any:
        if archetype not in self.strategies:
            raise ValueError(f"Unknown archetype: {archetype}")
        retrained = self.strategies[archetype].retrain(model, data, **kwargs)
        logger.info(f"Retrained model for archetype: {archetype}")
        return retrained

    def receive_feedback(self, feedback: Dict[str, Any]):
        self.feedback_log.append(feedback)
        logger.info(f"Received feedback: {feedback}")
        # Optionally trigger self-healing/meta-learning here

    def get_status(self) -> Dict[str, Any]:
        return {
            'registered_archetypes': list(self.strategies.keys()),
            'active_models': list(self.active_models.keys()),
            'model_count': len(self.model_registry),
            'feedback_count': len(self.feedback_log)
        }

def get_unified_model_forge():
    """
    Create and configure a unified model forge with all available strategies.

    Returns:
        UnifiedModelForge: Configured forge with all registered strategies
    """
    forge = UnifiedModelForge()

    # Register core archetype strategies
    forge.register_strategy('divine', DivineArchetypeStrategy())
    forge.register_strategy('hephaestus', HephaestusArchetypeStrategy())
    forge.register_strategy('fate', FateArchetypeStrategy())

    # Register additional strategies if available
    if ADDITIONAL_STRATEGIES_AVAILABLE:
        try:
            # Create adapted strategies that bridge the interface gap
            forge.register_strategy('neural_network', StrategyAdapter(BaseNeuralNetworkStrategy()))
            forge.register_strategy('ensemble', StrategyAdapter(BaseEnsembleStrategy()))
            forge.register_strategy('transformer', StrategyAdapter(BaseTransformerStrategy()))
            forge.register_strategy('quantum_enhanced', StrategyAdapter(BaseQuantumEnhancedStrategy()))
            forge.register_strategy('hybrid', StrategyAdapter(BaseHybridStrategy()))

            logger.info("✅ Registered all additional strategies: neural_network, ensemble, transformer, quantum_enhanced, hybrid")
        except Exception as e:
            logger.error(f"❌ Failed to register additional strategies: {e}")
    else:
        logger.warning("⚠️ Additional strategies not available - only core strategies registered")

    # Log registered strategies
    status = forge.get_status()
    logger.info(f"🔧 Unified Model Forge initialized with {len(status['registered_archetypes'])} strategies: {status['registered_archetypes']}")

    return forge

# Example: DivineArchetypeStrategy, HephaestusArchetypeStrategy, etc. would be implemented separately and registered here.
