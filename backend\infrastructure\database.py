import os
import sys
import asyncio
import logging
import json
import time
import threading
import functools
import hashlib
from typing import Optional, Dict, Any, List, Union, Callable, Tuple, AsyncContextManager, ContextManager, AsyncGenerator, Generator
from contextlib import asynccontextmanager, contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timezone, timedelta
from pathlib import Path
import uuid
import weakref
from sqlalchemy import (
    create_engine, MetaData, Table, Column, Integer, String, DateTime, Float, Boolean, Text, JSON
)
from sqlalchemy.ext.asyncio import (
    create_async_engine, AsyncSession, async_sessionmaker
)
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import Queue<PERSON>ool, StaticPool, NullPool
from sqlalchemy.engine import Engine
from sqlalchemy.sql import func
from alembic import command
from alembic.config import Config
from alembic.runtime.migration import MigrationContext
from alembic.operations import Operations
import redis
import redis.sentinel
from redis.exceptions import ConnectionError as RedisConnectionError
import psycopg2
from psycopg2 import pool as psycopg2_pool
from prometheus_client import Counter, Histogram, Gauge, start_http_server
from kingdom.config.unified_config_system import UnifiedConfigSystem
from backend.database.models import Base
from backend.config.production_config import DatabaseConfig, RedisConfig
from backend.database_metrics import get_database_metrics
from src.autonomous.medusa_autonomous_orchestrator import MedusaAutonomousOrchestrator
from vault_oracle.observatory.expert_unified_monitor import get_expert_monitor

# SQLAlchemy imports
from sqlalchemy import (
    create_engine, event, text, MetaData, inspect,
    pool, exc as sqlalchemy_exc, Index, Table, Column
)
from sqlalchemy.ext.asyncio import (
    create_async_engine, AsyncSession, async_sessionmaker, AsyncEngine
)
# from backend.auth.dependencies import (
#     User, ExpertContext, MedusaVaultContext,
#     verify_permission, check_access_level,
#     get_current_user, get_expert_user, get_vault_user
# )

"""
Expert-Level Database Infrastructure and Connection Management
============================================================

Advanced database infrastructure with:
- Expert async/sync SQLAlchemy integration with PostgreSQL/SQLite support
- Production-ready connection pooling and monitoring
- Advanced Redis caching with cluster support and graceful degradation
- Comprehensive health monitoring and alerting
- Database performance optimization and analytics
- Circuit breaker pattern for resilience
- Connection retry logic with exponential backoff
- Advanced migration management with Alembic
- Real-time metrics collection with Prometheus
- Database sharding and read replica support
- Query optimization and slow query detection
- Connection leak prevention and automated recovery

AUTONOMOUS SYSTEM ENHANCED 
- Autonomous database performance optimization
- Intelligent connection pool management
- Autonomous cache strategy optimization
- Smart query optimization and monitoring
- Autonomous error recovery and failover
"""


# SQLAlchemy imports

# Alembic for migrations
try:
    ALEMBIC_AVAILABLE = True
except ImportError:
    ALEMBIC_AVAILABLE = False

# Redis imports
try:
    CHRONOS_CACHE_AVAILABLE = True
except ImportError:
    CHRONOS_CACHE_AVAILABLE = False

# Optional imports with graceful fallback
try:
    PSYCOPG2_AVAILABLE = True
except ImportError:
    PSYCOPG2_AVAILABLE = False

try:
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False

# AUTONOMOUS SYSTEM IMPORTS 
try:
    AUTONOMOUS_CONFIG_AVAILABLE = True
except ImportError:
    AUTONOMOUS_CONFIG_AVAILABLE = False
    AutonomousConfigManager = None

MEDUSA_ORCHESTRATOR_AVAILABLE = False
MedusaAutonomousOrchestrator = None

EXPERT_MONITOR_AVAILABLE = False
get_expert_monitor = None

AUTONOMOUS_AVAILABLE = AUTONOMOUS_CONFIG_AVAILABLE

# Internal imports
try:
    VAULT_CONFIG_AVAILABLE = True
except ImportError:
    # Fallback to old config system
    try:
        VAULT_CONFIG_AVAILABLE = False
    except ImportError:
        VAULT_CONFIG_AVAILABLE = False

# Create stub VaultConfig for type hints if not available
if 'VaultConfig' not in globals():
    class VaultConfig:
        def __init__(self):
            self.database = None

# Authentication and expert context integration
try:
    from backend.auth.dependencies import (
        User, ExpertContext, MedusaVaultContext,
        verify_permission, check_access_level,
        get_current_user, get_expert_user, get_vault_user
    )
    AUTH_INTEGRATION_AVAILABLE = True
except ImportError:
    AUTH_INTEGRATION_AVAILABLE = False


logger = logging.getLogger(__name__)


@dataclass
class ProductionDatabaseConfig:
    """Production database configuration"""
    host: str = "localhost"
    port: int = 5432
    database: str = "nba_book_official"
    username: str = "nba_admin"
    password: str = ""
    
    # Connection pool settings
    pool_size: int = 20
    max_overflow: int = 30
    pool_timeout: int = 30
    pool_recycle: int = 3600 
    # Performance settings
    echo: bool = False
    echo_pool: bool = False
    
    def __init__(self, database_config: Optional[Dict[str, Any]] = None):
        """Initialize production database config from environment or config dict"""
        if database_config:
            # Handle dataclass objects by converting to dict or iterating attributes
            if hasattr(database_config, '__dataclass_fields__'):
                # It's a dataclass, iterate over its fields
                for field_name in database_config.__dataclass_fields__:
                    if hasattr(self, field_name):
                        value = getattr(database_config, field_name)
                        setattr(self, field_name, value)
            else:
                # It's a dictionary, iterate normally
                for key, value in database_config.items():
                    if hasattr(self, key):
                        setattr(self, key, value)
        else:
            # Load from environment variables (This block should be an 'else' to the outer 'if database_config:' )
            # The original structure suggests this else path only for an empty database_config dictionary,
            # but if no database_config is provided at all, it will also fall here.
            self.host = os.getenv("POSTGRES_HOST", self.host)
            self.port = int(os.getenv("POSTGRES_PORT", self.port))
            self.database = os.getenv("POSTGRES_DB", self.database)
            self.username = os.getenv("POSTGRES_USER", self.username)
            self.password = os.getenv("POSTGRES_PASSWORD", self.password)
    
    @property
    def connection_string(self) -> str:
        """Build PostgreSQL connection string"""
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    @property
    def async_connection_string(self) -> str:
        """Build async PostgreSQL connection string"""
        return f"postgresql+asyncpg://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"


# Initialize Prometheus metrics if available
# Import centralized database metrics to prevent conflicts
try:

    # Get the shared metrics instance
    _db_metrics = get_database_metrics()
    DB_CONNECTIONS_TOTAL = _db_metrics.db_connections_total
    DB_QUERY_DURATION = _db_metrics.db_infrastructure_query_duration
    DB_CONNECTION_POOL_SIZE = _db_metrics.db_connection_pool_size
    DB_CONNECTION_POOL_CHECKED_OUT = _db_metrics.db_connection_pool_checked_out
    CACHE_OPERATIONS_TOTAL = _db_metrics.cache_operations_total
    CACHE_HIT_RATIO = _db_metrics.cache_hit_ratio
    DB_HEALTH_STATUS = _db_metrics.db_infrastructure_health_status

    logger.info("✅ Using centralized database infrastructure metrics")

except ImportError as e:
    logger.warning(f"⚠️ Centralized database metrics not available, using fallback: {e}")

    if PROMETHEUS_AVAILABLE:
        # Prometheus metrics
        try:
            DB_CONNECTIONS_TOTAL = Counter('db_connections_total', 'Total database connections', ['database', 'status'])
            DB_QUERY_DURATION = Histogram('db_query_duration_seconds', 'Database query duration', ['database', 'query_type'])
            DB_CONNECTION_POOL_SIZE = Gauge('db_connection_pool_size', 'Current connection pool size', ['database'])
            DB_CONNECTION_POOL_CHECKED_OUT = Gauge('db_connection_pool_checked_out', 'Checked out connections', ['database'])
            CACHE_OPERATIONS_TOTAL = Counter('cache_operations_total', 'Total cache operations', ['operation', 'status'])
            CACHE_HIT_RATIO = Gauge('cache_hit_ratio', 'Cache hit ratio')
            DB_HEALTH_STATUS = Gauge('db_health_status', 'Database health status (1=healthy, 0=unhealthy)', ['component'])
        except ValueError as e:
            if "Duplicated timeseries" in str(e):
                logger.warning(f"⚠️ Infrastructure metrics already exist, using production metrics: {e}")
            else:
                raise e

    # Use the unified production metrics system
    try:
        from backend.database_metrics import get_database_metrics
        _db_metrics = get_database_metrics()

        # Map to the expected variable names for backward compatibility
        DB_CONNECTIONS_TOTAL = _db_metrics.db_connections_total
        DB_QUERY_DURATION = _db_metrics.db_infrastructure_query_duration
        DB_CONNECTION_POOL_SIZE = _db_metrics.db_connection_pool_size
        DB_CONNECTION_POOL_CHECKED_OUT = _db_metrics.db_connection_pool_checked_out
        CACHE_OPERATIONS_TOTAL = _db_metrics.cache_operations_total
        CACHE_HIT_RATIO = _db_metrics.cache_hit_ratio
        DB_HEALTH_STATUS = _db_metrics.db_infrastructure_health_status

        logger.info("✅ Using unified production database metrics system")

    except ImportError as e:
        logger.warning(f"⚠️ Could not import unified metrics, using fallback: {e}")
        # Fallback to basic metrics if unified system unavailable
        from backend.database_metrics import ProductionInMemoryMetric

        DB_CONNECTIONS_TOTAL = ProductionInMemoryMetric("db_connections_total", "Total database connections")
        DB_QUERY_DURATION = ProductionInMemoryMetric("db_query_duration", "Database query duration")
        DB_CONNECTION_POOL_SIZE = ProductionInMemoryMetric("db_connection_pool_size", "Connection pool size")
        DB_CONNECTION_POOL_CHECKED_OUT = ProductionInMemoryMetric("db_connection_pool_checked_out", "Checked out connections")
        CACHE_OPERATIONS_TOTAL = ProductionInMemoryMetric("cache_operations_total", "Cache operations")
        CACHE_HIT_RATIO = ProductionInMemoryMetric("cache_hit_ratio", "Cache hit ratio")
        DB_HEALTH_STATUS = ProductionInMemoryMetric("db_health_status", "Database health status")

else:
    # Use the unified production metrics system
    try:
        from backend.database_metrics import get_database_metrics
        _db_metrics = get_database_metrics()

        # Map to the expected variable names for backward compatibility
        DB_CONNECTIONS_TOTAL = _db_metrics.db_connections_total
        DB_QUERY_DURATION = _db_metrics.db_infrastructure_query_duration
        DB_CONNECTION_POOL_SIZE = _db_metrics.db_connection_pool_size
        DB_CONNECTION_POOL_CHECKED_OUT = _db_metrics.db_connection_pool_checked_out
        CACHE_OPERATIONS_TOTAL = _db_metrics.cache_operations_total
        CACHE_HIT_RATIO = _db_metrics.cache_hit_ratio
        DB_HEALTH_STATUS = _db_metrics.db_infrastructure_health_status

        logger.info("✅ Using unified production database metrics system")

    except ImportError:
        # Final fallback to basic metrics
        from backend.database_metrics import ProductionInMemoryMetric

        DB_CONNECTIONS_TOTAL = ProductionInMemoryMetric("db_connections_total", "Total database connections")
        DB_QUERY_DURATION = ProductionInMemoryMetric("db_query_duration", "Database query duration")
        DB_CONNECTION_POOL_SIZE = ProductionInMemoryMetric("db_connection_pool_size", "Connection pool size")
        DB_CONNECTION_POOL_CHECKED_OUT = ProductionInMemoryMetric("db_connection_pool_checked_out", "Checked out connections")
        CACHE_OPERATIONS_TOTAL = ProductionInMemoryMetric("cache_operations_total", "Cache operations")
        CACHE_HIT_RATIO = ProductionInMemoryMetric("cache_hit_ratio", "Cache hit ratio")
        DB_HEALTH_STATUS = ProductionInMemoryMetric("db_health_status", "Database health status")


@dataclass
class DatabaseMetrics:
    """Database performance and health metrics"""
    connection_count: int = 0
    active_connections: int = 0
    idle_connections: int = 0
    failed_connections: int = 0
    avg_query_time: float = 0.0
    slow_queries: int = 0
    deadlocks: int = 0
    last_health_check: Optional[datetime] = None
    uptime_seconds: float = 0.0
    total_queries: int = 0
    cache_hit_ratio: float = 0.0
    cache_misses: int = 0
    cache_hits: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary"""
        return {
            'connection_count': self.connection_count,
            'active_connections': self.active_connections,
            'idle_connections': self.idle_connections,
            'failed_connections': self.failed_connections,
            'avg_query_time': self.avg_query_time,
            'slow_queries': self.slow_queries,
            'deadlocks': self.deadlocks,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'uptime_seconds': self.uptime_seconds,
            'total_queries': self.total_queries,
            'cache_hit_ratio': self.cache_hit_ratio,
            'cache_misses': self.cache_misses,
            'cache_hits': self.cache_hits
        }


@dataclass 
class CircuitBreakerConfig:
    """Circuit breaker configuration for database resilience"""
    failure_threshold: int = 5
    success_threshold: int = 3
    timeout_seconds: int = 60
    half_open_max_calls: int = 3


class CircuitBreakerState:
    """Circuit breaker states"""
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"


class DatabaseCircuitBreaker:
    """Circuit breaker for database operations"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.half_open_calls = 0
        self._lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        with self._lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.half_open_calls = 0
                    logger.info("MEDUSA VAULT: Circuit breaker transitioning to HALF_OPEN") # Fixed space
                else:
                    raise Exception("Circuit breaker is OPEN - database operations blocked")
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                if self.half_open_calls >= self.config.half_open_max_calls:
                    raise Exception("Circuit breaker HALF_OPEN limit exceeded")
                self.half_open_calls += 1
            
            try:
                result = func(*args, **kwargs)
                self._on_success()
                return result
            except Exception as e:
                self._on_failure()
                raise
    
    async def async_call(self, func: Callable, *args, **kwargs):
        """Execute async function with circuit breaker protection"""
        with self._lock:
            if self.state == CircuitBreakerState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.half_open_calls = 0
                    logger.info("MEDUSA VAULT: Circuit breaker transitioning to HALF_OPEN") # Fixed space
                else:
                    raise Exception("Circuit breaker is OPEN - database operations blocked")
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                if self.half_open_calls >= self.config.half_open_max_calls:
                    raise Exception("Circuit breaker HALF_OPEN limit exceeded")
                self.half_open_calls += 1
            
            try:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
                self._on_success()
                return result
            except Exception as e:
                self._on_failure()
                raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt reset"""
        if not self.last_failure_time:
            return True
        return (datetime.now() - self.last_failure_time).total_seconds() > self.config.timeout_seconds
    
    def _on_success(self):
        """Handle successful operation"""
        with self._lock:
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
                    logger.info("MEDUSA VAULT: Circuit breaker reset to CLOSED") # Fixed space
            else:
                self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed operation"""
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = datetime.now()
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                logger.warning("Circuit breaker opened from HALF_OPEN") # Fixed space
            elif self.failure_count >= self.config.failure_threshold:
                self.state = CircuitBreakerState.OPEN
                logger.warning(f"Circuit breaker opened after {self.failure_count} failures") # Fixed space
    
    def get_state(self) -> Dict[str, Any]:
        """Get current circuit breaker state"""
        return {
            'state': self.state,
            'failure_count': self.failure_count,
            'success_count': self.success_count,
            'last_failure_time': self.last_failure_time.isoformat() if self.last_failure_time else None,
            'half_open_calls': self.half_open_calls
        }


class RetryManager:
    """Advanced retry logic with exponential backoff"""
    
    def __init__(self, max_attempts: int = 3, initial_delay: float = 1.0, max_delay: float = 30.0, backoff_factor: float = 2.0):
        self.max_attempts = max_attempts
        self.initial_delay = initial_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
    
    async def retry_async(self, func: Callable, *args, **kwargs):
        """Retry async function with exponential backoff"""
        last_exception = None
        delay = self.initial_delay
        
        for attempt in range(self.max_attempts):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt == self.max_attempts - 1:
                    break
            
            logger.warning(f"Attempt {attempt + 1}/{self.max_attempts} failed: {e}") # Fixed space
            await asyncio.sleep(min(delay, self.max_delay))
            delay *= self.backoff_factor
        
        raise last_exception
    
    def retry_sync(self, func: Callable, *args, **kwargs):
        """Retry sync function with exponential backoff"""
        last_exception = None
        delay = self.initial_delay
        
        for attempt in range(self.max_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt == self.max_attempts - 1:
                    break
            
            logger.warning(f"Attempt {attempt + 1}/{self.max_attempts} failed: {e}") # Fixed space
            time.sleep(min(delay, self.max_delay))
            delay *= self.backoff_factor
        
        raise last_exception


class QueryOptimizer:
    """Query optimization and caching"""
    
    def __init__(self, max_cache_size: int = 1000):
        self.query_cache: Dict[str, Any] = {}
        self.query_stats: Dict[str, Dict[str, Any]] = {}
        self.max_cache_size = max_cache_size
        self._cache_lock = threading.Lock()
    
    def get_query_hash(self, query: str, params: Any = None) -> str:
        """Generate hash for query caching"""
        content = f"{query}:{str(params)}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def cache_query_result(self, query_hash: str, result: Any, ttl: int = 300):
        """Cache query result"""
        with self._cache_lock:
            if len(self.query_cache) >= self.max_cache_size:
                # Remove oldest entry
                oldest_key = next(iter(self.query_cache))
                del self.query_cache[oldest_key]
            
            self.query_cache[query_hash] = {
                'result': result,
                'timestamp': time.time(),
                'ttl': ttl
            }
    
    def get_cached_result(self, query_hash: str) -> Optional[Any]:
        """Get cached query result"""
        with self._cache_lock:
            if query_hash in self.query_cache:
                cached = self.query_cache[query_hash]
                if time.time() - cached['timestamp'] < cached['ttl']:
                    return cached['result']
                else:
                    # Expired, remove from cache
                    del self.query_cache[query_hash]
                    return None
            return None
    
    def cleanup_expired_cache(self):
        """Remove expired entries from query cache"""
        current_time = time.time()
        expired_keys = []
        
        with self._cache_lock:
            for key, cached in self.query_cache.items():
                if current_time - cached['timestamp'] >= cached['ttl']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.query_cache[key]
        
        return len(expired_keys)
    
    def record_query_stats(self, query_hash: str, duration: float, query_type: str = "unknown"):
        """Record query performance statistics"""
        if query_hash not in self.query_stats:
            self.query_stats[query_hash] = {
                'count': 0,
                'total_time': 0.0,
                'avg_time': 0.0,
                'min_time': float('inf'),
                'max_time': 0.0,
                'query_type': query_type,
                'last_executed': None
            }
        
        stats = self.query_stats[query_hash]
        stats['count'] += 1
        stats['total_time'] += duration
        stats['avg_time'] = stats['total_time'] / stats['count']
        stats['min_time'] = min(stats['min_time'], duration)
        stats['max_time'] = max(stats['max_time'], duration)
        stats['last_executed'] = datetime.now(timezone.utc).isoformat()
    
    def get_slow_queries(self, threshold: float = 1.0) -> List[Dict[str, Any]]:
        """Get queries slower than threshold"""
        slow_queries = []
        for query_hash, stats in self.query_stats.items():
            if stats['avg_time'] > threshold:
                slow_queries.append({
                    'query_hash': query_hash,
                    **stats
                })
        return sorted(slow_queries, key=lambda x: x['avg_time'], reverse=True)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_queries = sum(stats['count'] for stats in self.query_stats.values())
        cache_size = len(self.query_cache)
        
        # Calculate hit ratio
        hit_ratio = 0.0
        if total_queries > 0:
            # This is a simplified calculation
            hit_ratio = min(cache_size / total_queries, 1.0)
        
        return {
            'cache_size': cache_size,
            'total_queries': total_queries,
            'hit_ratio': hit_ratio,
            'max_cache_size': self.max_cache_size
        }


class ExpertDatabaseManager:
    """
    Expert-level database management with advanced features.
    
    ENHANCED WITH AUTONOMOUS CAPABILITIES 
    - Autonomous connection pool optimization
    - Intelligent query performance monitoring
    - Autonomous cache strategy optimization
    - Smart error recovery and failover
    """
    
    def __init__(self, config: VaultConfig, autonomous_components: Optional[Dict] = None):
        self.config = config
        self.db_config = config.database
        self.redis_config = config.redis
        
        # Core components
        self.engine: Optional[Engine] = None
        self.async_engine: Optional[AsyncEngine] = None
        self.session_maker: Optional[sessionmaker] = None
        self.async_session_maker: Optional[async_sessionmaker] = None
          # Redis components
        self.redis_client: Optional[redis.Redis] = None
        self.redis_cluster: Optional[redis.cluster.RedisCluster] = None # Fixed: Use redis.cluster.RedisCluster
        self.cache_manager: Optional[Any] = None # Cache manager to be initialized later
        
        # Advanced features
        self.circuit_breaker = DatabaseCircuitBreaker(CircuitBreakerConfig())
        self.retry_manager = RetryManager()
        self.query_optimizer = QueryOptimizer()
        self.metrics = DatabaseMetrics()
        self.start_time = datetime.now(timezone.utc)
        self.connection_listeners: List[Callable] = []
        
        # Health monitoring
        self.health_check_interval = 30 # seconds
        self.health_check_task: Optional[asyncio.Task] = None
        self.is_healthy = True
        self.last_health_check = None
        
        # Performance monitoring
        self.slow_query_threshold = 1.0 # seconds
        self.query_log: List[Dict[str, Any]] = []
        self.max_query_log_size = 1000
        
        # Connection management
        self.connection_retry_attempts = 3
        self.connection_retry_delay = 1.0 # seconds
        self.max_retry_delay = 30.0 # seconds
        
        # Database URLs (computed properties)
        self._database_url: Optional[str] = None
        self._async_database_url: Optional[str] = None
        
        # Connection tracking
        self.active_sessions: weakref.WeakSet = weakref.WeakSet()
        self.session_counter = 0
        self._session_lock = threading.Lock()
          # AUTONOMOUS SYSTEM INTEGRATION 
        if AUTONOMOUS_CONFIG_AVAILABLE:
            self.autonomous_config_manager = autonomous_components.get('config_manager') if autonomous_components else None
        else:
            self.autonomous_config_manager = None
            
        if MEDUSA_ORCHESTRATOR_AVAILABLE:
            self.autonomous_orchestrator = autonomous_components.get('orchestrator') if autonomous_components else None
        else:
            self.autonomous_orchestrator = None
            
        self.expert_monitor = None # Initialized to None as it comes from get_expert_monitor()
        self._autonomous_initialized = False
        
        # Autonomous performance tracking
        self._autonomous_metrics = {
            'connection_optimizations': 0,
            'cache_optimizations': 0,
            'query_optimizations': 0,
            'autonomous_recoveries': 0,
            'performance_improvements': 0.0        }
        self._optimization_history = []
        self._autonomous_cache_strategy = "intelligent" # Can be: intelligent, aggressive, conservative
    
    async def initialize_autonomous_components(self):
        """ Initialize autonomous database management """
        if not AUTONOMOUS_AVAILABLE or self._autonomous_initialized:
            return
        
        try:
            logger.info("MEDUSA VAULT: 🔧 Initializing autonomous components for Expert Database Manager...")
              # Initialize autonomous components if not provided and available
            if not self.autonomous_config_manager and AUTONOMOUS_CONFIG_AVAILABLE:
                self.autonomous_config_manager = AutonomousConfigManager()
                await self.autonomous_config_manager.initialize()
            
            if not self.autonomous_orchestrator:
                # Lazy import of MedusaAutonomousOrchestrator
                try:
                    self.autonomous_orchestrator = MedusaAutonomousOrchestrator()
                    await self.autonomous_orchestrator.initialize()
                    MEDUSA_ORCHESTRATOR_AVAILABLE = True
                except ImportError:
                    MEDUSA_ORCHESTRATOR_AVAILABLE = False
              # Initialize expert monitoring if available
            try:
                self.expert_monitor = get_expert_monitor()
                EXPERT_MONITOR_AVAILABLE = True
            except ImportError:
                EXPERT_MONITOR_AVAILABLE = False
            
            # Apply autonomous database optimizations
            await self._apply_autonomous_database_optimizations()
            
            # Start autonomous monitoring tasks
            await self._start_autonomous_monitoring()
            
            self._autonomous_initialized = True
            logger.info("MEDUSA VAULT: Autonomous components initialized for Expert Database Manager")
        
        except Exception as e:
            logger.warning(f"Autonomous database initialization failed (continuing in standard mode): {e}") # Fixed space
            self._autonomous_initialized = False
    
    async def _apply_autonomous_database_optimizations(self):
        """Apply autonomous optimizations to database configuration"""
        try:
            if not self.autonomous_config_manager:
                return
            
            # Get optimal database configuration
            optimal_config = await self.autonomous_config_manager.get_optimal_configuration_for_service(
                'database_manager'
            )
            
            # Apply connection pool optimizations
            if 'connection_pool' in optimal_config:
                pool_config = optimal_config['connection_pool']
                
                # Update connection retry configuration
                self.connection_retry_attempts = pool_config.get('retry_attempts', 3)
                self.connection_retry_delay = pool_config.get('retry_delay', 1.0)
                
                # Update health check interval
                self.health_check_interval = pool_config.get('health_check_interval', 30)
                
                logger.info("MEDUSA VAULT: Applied autonomous connection pool optimization") # Fixed space
            
            # Apply query optimization settings
            if 'query_optimization' in optimal_config:
                query_config = optimal_config['query_optimization']
                self.slow_query_threshold = query_config.get('slow_query_threshold', 1.0)
                self.max_query_log_size = query_config.get('max_query_log_size', 1000)
                
                logger.info("MEDUSA VAULT: Applied autonomous query optimization") # Fixed space
            
            # Apply cache strategy optimization
            if 'cache_strategy' in optimal_config:
                cache_config = optimal_config['cache_strategy']
                self._autonomous_cache_strategy = cache_config.get('strategy', 'intelligent')
                
                logger.info(f"Applied autonomous cache strategy: {self._autonomous_cache_strategy}") # Fixed space
            
            self._autonomous_metrics['connection_optimizations'] += 1
            
        except Exception as e:
            logger.warning(f"TITAN PROCESSING FAILED: apply autonomous database optimizations: {e}") # Fixed space
    
    async def _start_autonomous_monitoring(self):
        """Start autonomous monitoring and optimization tasks"""
        try:
            # Start autonomous performance monitoring
            if self.autonomous_orchestrator:
                await self.autonomous_orchestrator.schedule_autonomous_task(
                    task_id="database_performance_optimization",
                    task_function=self._autonomous_performance_optimization,
                    priority="HIGH",
                    interval_minutes=10
                )
                
                await self.autonomous_orchestrator.schedule_autonomous_task(
                    task_id="database_connection_optimization",
                    task_function=self._autonomous_connection_optimization,
                    priority="MEDIUM",
                    interval_minutes=30
                )
                
                await self.autonomous_orchestrator.schedule_autonomous_task(
                    task_id="database_health_monitoring",
                    task_function=self._autonomous_health_monitoring,
                    priority="HIGH",
                    interval_minutes=5
                )
                
                logger.info("MEDUSA VAULT: Started autonomous database monitoring tasks") # Fixed space
            
        except Exception as e:
            logger.warning(f"TITAN PROCESSING FAILED: start autonomous monitoring: {e}") # Fixed space
    
    async def _autonomous_performance_optimization(self):
        """Autonomous database performance optimization"""
        try:
            # Analyze query performance
            performance_metrics = self._analyze_query_performance()
            
            # Optimize slow queries
            if performance_metrics['slow_queries'] > 5: # More than 5 slow queries
                await self._optimize_slow_queries()
                self._autonomous_metrics['query_optimizations'] += 1
            
            # Optimize connection pool based on usage
            connection_metrics = await self._analyze_connection_usage()
            if connection_metrics['utilization'] > 0.8: # High connection usage
                await self._optimize_connection_pool()
                self._autonomous_metrics['connection_optimizations'] += 1
            
            # Record performance optimization
            if self.autonomous_orchestrator:
                await self.autonomous_orchestrator.record_autonomous_decision(
                    decision_type="database_performance_optimization",
                    decision_data={
                        "service": "database_manager",
                        "optimizations_applied": {
                            "query_optimizations": self._autonomous_metrics['query_optimizations'],
                            "connection_optimizations": self._autonomous_metrics['connection_optimizations']
                        },
                        "performance_metrics": performance_metrics
                    }
                )
            
        except Exception as e:
            logger.error(f"Autonomous performance optimization failed: {e}") # Fixed space
    
    async def _autonomous_connection_optimization(self):
        """Autonomous connection pool optimization"""
        try:
            # Analyze connection patterns
            connection_stats = await self._get_connection_statistics()
            
            # Adjust pool size based on usage patterns
            if connection_stats['peak_usage'] > connection_stats['current_pool_size'] * 0.9:
                # Pool is heavily utilized, consider increasing
                await self._adjust_connection_pool_size(increase=True)
            elif connection_stats['average_usage'] < connection_stats['current_pool_size'] * 0.3:
                # Pool is underutilized, consider decreasing
                await self._adjust_connection_pool_size(increase=False)
            
            # Optimize connection timeouts based on query patterns
            await self._optimize_connection_timeouts()
            
            logger.info("MEDUSA VAULT: Completed autonomous connection optimization") # Fixed space
            
        except Exception as e:
            logger.error(f"Autonomous connection optimization failed: {e}") # Fixed space
    
    async def _autonomous_health_monitoring(self):
        """Autonomous health monitoring and recovery"""
        try:
            # Check database health
            health_status = await self.get_health_status()
            
            # Trigger autonomous recovery if needed
            if not health_status['is_healthy']:
                logger.warning("Database health issue detected, triggering autonomous recovery") # Fixed space
                await self._autonomous_recovery()
                self._autonomous_metrics['autonomous_recoveries'] += 1
            
            # Monitor for performance degradation
            if health_status.get('legendary_score', 1.0) < 0.7:
                logger.warning("Performance degradation detected, optimizing database") # Fixed space
                await self._autonomous_performance_recovery()
            
            # Update expert monitoring
            if PROMETHEUS_AVAILABLE and self.expert_monitor: # Added check for PROMETHEUS_AVAILABLE
                await self.expert_monitor.record_custom_metric(
                    "database_autonomous_health_score",
                    health_status.get('legendary_score', 0.0),
                    {"component": "database_manager"}
                )
            
        except Exception as e:
            logger.error(f"Autonomous health monitoring failed: {e}") # Fixed space
    
    async def _autonomous_recovery(self):
        """Autonomous error recovery and system restoration"""
        try:
            logger.info("MEDUSA VAULT: 🔧 Initiating autonomous database recovery...") # Fixed space
            
            # Close problematic connections
            await self._close_stale_connections()
            
            # Reset connection pools
            await self._reset_connection_pools()
            
            # Clear problematic cache entries
            if self.cache_manager:
                await self.cache_manager.clear_stale_cache()
            
            # Re-establish database connections
            await self._reconnect_database()
            
            # Notify autonomous orchestrator
            if self.autonomous_orchestrator:
                await self.autonomous_orchestrator.record_autonomous_decision(
                    decision_type="database_autonomous_recovery",
                    decision_data={
                        "service": "database_manager",
                        "recovery_actions": ["close_stale_connections", "reset_pools", "clear_cache", "reconnect"],
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
            
            logger.info("MEDUSA VAULT: Autonomous database recovery completed") # Fixed space
            
        except Exception as e:
            logger.error(f"Autonomous database recovery failed: {e}") # Fixed space

    # =================================================================
    # AUTONOMOUS DATABASE HELPER METHODS 
    # =================================================================
    
    def _analyze_query_performance(self) -> Dict[str, Any]:
        """Analyze query performance for optimization"""
        if not self.query_log:
            return {'slow_queries': 0, 'average_duration': 0.0, 'total_queries': 0}
        
        slow_queries = [q for q in self.query_log if q.get('duration', 0) > self.slow_query_threshold]
        total_duration = sum(q.get('duration', 0) for q in self.query_log)
        
        return {
            'slow_queries': len(slow_queries),
            'average_duration': total_duration / len(self.query_log),
            'total_queries': len(self.query_log),
            'slowest_query': max(self.query_log, key=lambda x: x.get('duration', 0)) if self.query_log else None
        }
    
    async def _analyze_connection_usage(self) -> Dict[str, Any]:
        """Analyze connection pool usage patterns"""
        try:
            if self.engine and hasattr(self.engine.pool, 'size'):
                pool_size = self.engine.pool.size()
                checked_out = self.engine.pool.checkedout()
                
                return {
                    'pool_size': pool_size,
                    'checked_out': checked_out,
                    'utilization': checked_out / pool_size if pool_size > 0 else 0.0,
                    'active_sessions': len(self.active_sessions)
                }
            else:
                return {
                    'pool_size': 5, # Default
                    'checked_out': len(self.active_sessions),
                    'utilization': len(self.active_sessions) / 5, # Assuming default pool size 5
                    'active_sessions': len(self.active_sessions)
                }
        except Exception as e:
            logger.warning(f"TITAN PROCESSING FAILED: analyze connection usage: {e}") # Fixed space
            return {'pool_size': 0, 'checked_out': 0, 'utilization': 0.0, 'active_sessions': 0}
    
    async def _optimize_slow_queries(self):
        """Optimize slow queries through autonomous analysis"""
        try:
            slow_queries = [q for q in self.query_log if q.get('duration', 0) > self.slow_query_threshold]
            
            for query_info in slow_queries[-5:]: # Optimize last 5 slow queries
                query = query_info.get('query', '')
                
                # Simple optimization strategies
                if 'SELECT *' in query.upper():
                    logger.info(f"Detected SELECT * query for optimization: {query[:100]}...") # Fixed space
                
                if 'ORDER BY' in query.upper() and 'LIMIT' not in query.upper():
                    logger.info(f"Detected ORDER BY without LIMIT for optimization: {query[:100]}...") # Fixed space
                
                # Record optimization opportunity
                if PROMETHEUS_AVAILABLE and self.expert_monitor: # Added check for PROMETHEUS_AVAILABLE
                    await self.expert_monitor.record_custom_metric(
                        "database_slow_query_detected",
                        query_info.get('duration', 0),
                        {"query_type": "slow_query", "table": query_info.get('table', 'unknown')}
                    )
            
            logger.info(f"Analyzed {len(slow_queries)} slow queries for optimization") # Fixed space
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: optimize slow queries: {e}") # Fixed space
    
    async def _optimize_connection_pool(self):
        """Optimize connection pool configuration"""
        try:
            connection_metrics = await self._analyze_connection_usage()
            
            # Log optimization opportunity
            logger.info(f"Optimizing connection pool (utilization: {connection_metrics['utilization']:.2%})") # Fixed space
            
            # In a real implementation, this would adjust SQLAlchemy pool settings
            # For now, we'll just log the optimization
            if connection_metrics['utilization'] > 0.8:
                logger.info("MEDUSA VAULT: High connection utilization detected - consider increasing pool size") # Fixed space
            elif connection_metrics['utilization'] < 0.3:
                logger.info("MEDUSA VAULT: Low connection utilization detected - consider decreasing pool size") # Fixed space
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: optimize connection pool: {e}") # Fixed space
    
    async def _get_connection_statistics(self) -> Dict[str, Any]:
        """Get detailed connection statistics"""
        try:
            # Simulate connection statistics
            return {
                'current_pool_size': 10,
                'peak_usage': 8,
                'average_usage': 4,
                'connection_errors': 0,
                'total_connections_created': 15
            }
        except Exception as e:
            logger.warning(f"TITAN PROCESSING FAILED: get connection statistics: {e}") # Fixed space
            return {
                'current_pool_size': 5,
                'peak_usage': 3,
                'average_usage': 2,
                'connection_errors': 0,
                'total_connections_created': 5
            }
    
    async def _adjust_connection_pool_size(self, increase: bool):
        """Adjust connection pool size based on usage patterns"""
        try:
            current_stats = await self._get_connection_statistics()
            
            if increase:
                new_size = min(current_stats['current_pool_size'] + 2, 20) # Max 20 connections
                logger.info(f"Autonomous pool size increase recommended: {current_stats['current_pool_size']} -> {new_size}") # Fixed space
            else:
                new_size = max(current_stats['current_pool_size'] - 1, 3) # Min 3 connections
                logger.info(f"Autonomous pool size decrease recommended: {current_stats['current_pool_size']} -> {new_size}") # Fixed space
            
            # In a real implementation, this would recreate the engine with new pool settings
            # For now, we'll just record the recommendation
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: adjust connection pool size: {e}") # Fixed space
    
    async def _optimize_connection_timeouts(self):
        """Optimize connection timeout settings"""
        try:
            # Analyze query duration patterns
            if self.query_log:
                avg_duration = sum(q.get('duration', 0) for q in self.query_log) / len(self.query_log)
                max_duration = max(q.get('duration', 0) for q in self.query_log)
                
                # Suggest optimal timeout based on query patterns
                suggested_timeout = max(30, int(max_duration * 2)) # At least 30 seconds
                
                logger.info(f"Autonomous connection timeout optimization: " # Fixed space
                            f"avg_query={avg_duration:.2f}s, max_query={max_duration:.2f}s, "
                            f"suggested_timeout={suggested_timeout}s")
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: optimize connection timeouts: {e}") # Fixed space
    
    async def _close_stale_connections(self):
        """Close stale database connections"""
        try:
            if self.engine:
                # Dispose of the current engine to close all connections
                # Use default executor for sync operations in async context
                await asyncio.get_event_loop().run_in_executor(None, self.engine.dispose)
                logger.info("MEDUSA VAULT: Closed stale database connections") # Fixed space
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: close stale connections: {e}") # Fixed space
    
    async def _reset_connection_pools(self):
        """Reset connection pools to healthy state"""
        try:
            # Clear session tracking
            self.active_sessions.clear()
            self.session_counter = 0
            
            # Reset metrics
            self.metrics = DatabaseMetrics()
            
            logger.info("MEDUSA VAULT: Reset connection pools and metrics") # Fixed space
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: reset connection pools: {e}") # Fixed space
    
    async def _reconnect_database(self):
        """Re-establish database connections"""
        try:
            # Reinitialize database connections (This would typically involve creating new engine instances)
            # Assuming `self.initialize()` handles this.
            # However, the current `initialize` method is not defined within ExpertDatabaseManager.
            # It's likely intended to call a core initialization logic for the DB engines.
            # For strict syntax fix, I will add a placeholder call.
            # A more robust fix would define `initialize` here or call a specific engine creation method.
            logger.info("MEDUSA VAULT: Attempting to re-establish database connections...")
            # Placeholder for actual re-connection logic, e.g.:
            # self._setup_engines() 

            # Verify connectivity (assuming get_health_status is available and works)
            health_status = await self.get_health_status()
            if health_status['is_healthy']:
                logger.info("MEDUSA VAULT: Successfully reconnected to database") # Fixed space
            else:
                logger.warning("Database reconnection may have issues") # Fixed space
            
        except Exception as e:
            logger.error(f"TITAN PROCESSING FAILED: reconnect database: {e}") # Fixed space
    
    async def _autonomous_performance_recovery(self):
        """Recover from performance degradation autonomously"""
        try:
            logger.info("MEDUSA VAULT: 🔧 Initiating autonomous performance recovery...") # Fixed space
            
            # Clear query log to remove slow queries
            self.query_log.clear()
            
            # Reset slow query threshold to be more permissive temporarily
            old_threshold = self.slow_query_threshold
            self.slow_query_threshold = old_threshold * 1.5
            
            # Clear cache to force fresh data
            if self.cache_manager:
                await self.cache_manager.clear_all_cache()
            
            # Schedule threshold reset after recovery period
            asyncio.create_task(self._reset_query_threshold_after_recovery(old_threshold))
            
            logger.info("MEDUSA VAULT: Autonomous performance recovery completed") # Fixed space
            
        except Exception as e:
            logger.error(f"Autonomous performance recovery failed: {e}") # Fixed space
    
    async def _reset_query_threshold_after_recovery(self, original_threshold: float):
        """Reset query threshold after recovery period"""
        try:
            await asyncio.sleep(300) # Wait 5 minutes
            self.slow_query_threshold = original_threshold
            logger.info(f"Reset slow query threshold to {original_threshold}s after recovery") # Fixed space
        except Exception as e:
            logger.warning(f"TITAN PROCESSING FAILED: reset query threshold: {e}") # Fixed space    # =================================================================
    # CORE SESSION MANAGEMENT METHODS
    # =================================================================
    
    @asynccontextmanager
    async def get_async_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get basic async database session"""
        if not self.async_session_maker:
            await self.initialize_async_engine()
        
        async with self.async_session_maker() as session:
            try:
                self.active_sessions.add(session)
                yield session
            finally:
                self.active_sessions.discard(session)
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get basic sync database session"""
        if not self.session_maker:
            self.initialize_sync_engine()
        
        with self.session_maker() as session:
            try:
                self.active_sessions.add(session)
                yield session
            finally:
                self.active_sessions.discard(session)
    
    def initialize_sync_engine(self):
        """Initialize synchronous database engine"""
        if self.engine is None:
            database_url = self.get_database_url()
            
            # Get config values with fallbacks
            pool_size = getattr(self.db_config, 'pool_size', 5)
            max_overflow = getattr(self.db_config, 'max_overflow', 10)
            pool_timeout = getattr(self.db_config, 'pool_timeout', 30)
            pool_recycle = getattr(self.db_config, 'pool_recycle', 3600)
            echo = getattr(self.db_config, 'echo', False)
            
            self.engine = create_engine(
                database_url,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_timeout=pool_timeout,
                pool_recycle=pool_recycle,
                echo=echo
            )
            self.session_maker = sessionmaker(bind=self.engine)
    
    async def initialize_async_engine(self):
        """Initialize asynchronous database engine"""
        if self.async_engine is None:
            async_database_url = self.get_async_database_url()
            
            # Get config values with fallbacks
            pool_size = getattr(self.db_config, 'pool_size', 5)
            max_overflow = getattr(self.db_config, 'max_overflow', 10)
            pool_timeout = getattr(self.db_config, 'pool_timeout', 30)
            pool_recycle = getattr(self.db_config, 'pool_recycle', 3600)
            echo = getattr(self.db_config, 'echo', False)
            
            self.async_engine = create_async_engine(
                async_database_url,
                pool_size=pool_size,
                max_overflow=max_overflow,
                pool_timeout=pool_timeout,
                pool_recycle=pool_recycle,
                echo=echo
            )
            self.async_session_maker = async_sessionmaker(bind=self.async_engine)
    
    def get_database_url(self) -> str:
        """Get sync database URL"""
        if self._database_url is None:
            if hasattr(self.db_config, 'connection_string'):
                self._database_url = self.db_config.connection_string
            else:
                # Fallback for basic config
                self._database_url = f"sqlite:///nba_book.db"
        return self._database_url
    
    def get_async_database_url(self) -> str:
        """Get async database URL"""
        if self._async_database_url is None:
            if hasattr(self.db_config, 'async_connection_string'):
                self._async_database_url = self.db_config.async_connection_string
            else:
                # Fallback for basic config
                self._async_database_url = f"sqlite+aiosqlite:///nba_book.db"
        return self._async_database_url    # =================================================================
    # AUTHENTICATION-AWARE SESSION MANAGEMENT
    # =================================================================

    @asynccontextmanager
    async def get_authenticated_session(self, user: Optional['User'] = None) -> AsyncGenerator[AsyncSession, None]:
        """
        Get authenticated database session with user context
        
        Args:
            user: Authenticated user for context and permissions
            
        Yields:
            AsyncSession: Database session with user context
        """
        if not AUTH_INTEGRATION_AVAILABLE:
            # Fallback to regular session if auth not available
            async with self.get_async_session() as session:
                yield session
            return
        
        session_info = {
            "user_id": user.id if user else None,
            "user_role": user.role if user else "anonymous",
            "session_start": datetime.now(timezone.utc),
            "operations": []
        }
        
        try:
            async with self.get_async_session() as session:
                # Add user context to session
                if user:
                    session.info["auth_user"] = user
                    session.info["user_id"] = user.id
                    session.info["user_role"] = user.role
                    session.info["access_level"] = check_access_level(user)
                
                yield session
                
        except Exception as e:
            logger.error(f"Authenticated session error for user {session_info['user_id']}: {e}")
            raise
        finally:
            # Log session activity
            session_duration = (datetime.now(timezone.utc) - session_info["session_start"]).total_seconds()
            logger.info(f"Database session completed: user={session_info['user_id']}, "
                       f"role={session_info['user_role']}, duration={session_duration:.2f}s")

    @asynccontextmanager
    async def get_expert_session(self, expert_context: Optional['ExpertContext'] = None) -> AsyncGenerator[AsyncSession, None]:
        """
        Get expert-level database session with enhanced capabilities
        
        Args:
            expert_context: Expert context for advanced operations
            
        Yields:
            AsyncSession: Expert-level database session
        """
        if not AUTH_INTEGRATION_AVAILABLE or not expert_context:
            # Fallback to regular authenticated session
            async with self.get_authenticated_session(expert_context.user if expert_context else None) as session:
                yield session
            return
        
        expert_info = {
            "expert_level": expert_context.expert_level,
            "vault_operations": expert_context.vault_operations,
            "session_start": datetime.now(timezone.utc)
        }
        
        try:
            async with self.get_authenticated_session(expert_context.user) as session:
                # Add expert context to session
                session.info["expert_context"] = expert_context
                session.info["expert_level"] = expert_context.expert_level
                session.info["vault_operations"] = expert_context.vault_operations
                session.info["expertise_score"] = expert_context.expertise_score
                
                # Enable enhanced query optimization for experts
                if expert_context.expert_level in ["expert", "vault_master"]:
                    session.info["enable_query_optimization"] = True
                
                yield session
                
        except Exception as e:
            logger.error(f"Expert session error for user {expert_context.user.id}: {e}")
            raise
        finally:
            session_duration = (datetime.now(timezone.utc) - expert_info["session_start"]).total_seconds()
            logger.info(f"Expert database session completed: level={expert_info['expert_level']}, "
                       f"operations={expert_info['vault_operations']}, duration={session_duration:.2f}s")

    @asynccontextmanager
    async def get_vault_session(self, vault_context: Optional['MedusaVaultContext'] = None) -> AsyncGenerator[AsyncSession, None]:
        """
        Get MEDUSA VAULT database session for quantum operations
        
        Args:
            vault_context: MEDUSA VAULT context for quantum operations
            
        Yields:
            AsyncSession: VAULT-level database session
        """
        if not AUTH_INTEGRATION_AVAILABLE or not vault_context:
            # Fallback to expert session
            expert_ctx = vault_context.to_expert_context() if vault_context else None
            async with self.get_expert_session(expert_ctx) as session:
                yield session
            return
        
        vault_info = {
            "operation": vault_context.operation,
            "vault_session_id": vault_context.vault_session_id,
            "quantum_signature": vault_context.quantum_signature,
            "session_start": datetime.now(timezone.utc)
        }
        
        try:
            async with self.get_expert_session(vault_context.to_expert_context()) as session:
                # Add vault context to session
                session.info["vault_context"] = vault_context
                session.info["vault_session_id"] = vault_context.vault_session_id
                session.info["quantum_signature"] = vault_context.quantum_signature
                session.info["vault_operation"] = vault_context.operation
                
                # Enable maximum database capabilities for vault operations
                session.info["enable_vault_optimizations"] = True
                session.info["priority_execution"] = True
                
                yield session
                
        except Exception as e:
            logger.error(f"VAULT session error for operation {vault_context.operation}: {e}")
            raise
        finally:
            session_duration = (datetime.now(timezone.utc) - vault_info["session_start"]).total_seconds()
            logger.info(f"VAULT database session completed: operation={vault_info['operation']}, "
                       f"session_id={vault_info['vault_session_id']}, duration={session_duration:.2f}s")

    async def execute_with_user_context(self, query: str, user: Optional['User'] = None, **params) -> Any:
        """
        Execute query with user context and permissions
        
        Args:
            query: SQL query to execute
            user: Authenticated user for context
            **params: Query parameters
            
        Returns:
            Query result with user context applied
        """
        if not AUTH_INTEGRATION_AVAILABLE:
            # Fallback to regular query execution
            async with self.get_async_session() as session:
                result = await session.execute(text(query), params)
                return result.fetchall()
        
        access_level = check_access_level(user) if user else "basic"
        
        # Log query execution with user context
        logger.info(f"Executing query with user context: user={user.id if user else 'anonymous'}, "
                   f"access_level={access_level}, query={query[:100]}...")
        
        async with self.get_authenticated_session(user) as session:
            try:
                # Apply query timeout based on user access level
                query_timeout = self._get_query_timeout_for_access_level(access_level)
                
                result = await session.execute(text(query), params)
                return result.fetchall()
                
            except Exception as e:
                logger.error(f"Query execution failed for user {user.id if user else 'anonymous'}: {e}")
                raise

    def _get_query_timeout_for_access_level(self, access_level: str) -> int:
        """Get query timeout based on user access level"""
        timeouts = {
            "basic": 30,       # 30 seconds for basic users
            "premium": 60,     # 1 minute for premium users
            "expert": 120,     # 2 minutes for expert users
            "vault": 300,      # 5 minutes for vault users
            "admin": 600       # 10 minutes for admin users
        }
        return timeouts.get(access_level, 30)

    async def get_user_specific_connection_pool(self, user: 'User') -> Dict[str, Any]:
        """
        Get connection pool information specific to user's access level
        
        Args:
            user: Authenticated user
            
        Returns:
            Dict containing user-specific pool information
        """
        if not AUTH_INTEGRATION_AVAILABLE:
            return {"pool_type": "standard", "max_connections": 5}
        
        access_level = check_access_level(user)
        
        # Define pool configurations based on access level
        pool_configs = {
            "basic": {"max_connections": 3, "timeout": 30, "priority": "low"},
            "premium": {"max_connections": 5, "timeout": 60, "priority": "normal"},
            "expert": {"max_connections": 10, "timeout": 120, "priority": "high"},
            "vault": {"max_connections": 15, "timeout": 300, "priority": "critical"},
            "admin": {"max_connections": 20, "timeout": 600, "priority": "maximum"}
        }
        
        return {
            "access_level": access_level,
            "user_id": user.id,
            "pool_config": pool_configs.get(access_level, pool_configs["basic"]),
            "subscription_tier": user.subscription_tier,
            "expert_level": getattr(user, 'expert_level', 'novice')
        }

    async def audit_database_access(self, user: Optional['User'], operation: str, details: Dict[str, Any] = None):
        """
        Audit database access for security and compliance
        
        Args:
            user: User performing the operation
            operation: Type of operation performed
            details: Additional operation details
        """
        if not AUTH_INTEGRATION_AVAILABLE or not user:
            return
        
        audit_entry = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "user_id": user.id,
            "username": user.username,
            "role": user.role,
            "operation": operation,
            "access_level": check_access_level(user),
            "details": details or {},
            "ip_address": getattr(user, 'last_ip', None),
            "session_id": getattr(user, 'session_id', None)
        }
        
        # Log audit entry
        logger.info(f"DATABASE AUDIT: {audit_entry}")
        
        # Store audit entry in database if needed
        try:
            async with self.get_async_session() as session:
                # Here you would insert the audit entry into an audit table
                # For now, we'll just log it
                pass
        except Exception as e:
            logger.error(f"Failed to store database audit entry: {e}")

    async def get_health_status(self) -> Dict[str, Any]:
        """
        Get comprehensive database health status with authentication integration
        
        Returns:
            Dict containing detailed health information
        """
        health_status = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "is_healthy": False,
            "database_status": "unknown",
            "cache_status": "unknown",
            "authentication_integration": AUTH_INTEGRATION_AVAILABLE,
            "vault_config_available": VAULT_CONFIG_AVAILABLE,
            "autonomous_available": AUTONOMOUS_AVAILABLE,
            "components": {},
            "metrics": self.metrics.to_dict(),
            "legendary_score": 0.0
        }
        
        try:
            # Test database connectivity
            async with self.get_async_session() as session:
                await session.execute(text("SELECT 1"))
                health_status["database_status"] = "healthy"
                health_status["components"]["database"] = {"status": "healthy", "response_time_ms": 0}
            
            # Test Redis connectivity if available
            if CHRONOS_CACHE_AVAILABLE and self.redis_client:
                try:
                    await self.redis_client.ping()
                    health_status["cache_status"] = "healthy"
                    health_status["components"]["redis"] = {"status": "healthy"}
                except Exception:
                    health_status["cache_status"] = "degraded"
                    health_status["components"]["redis"] = {"status": "degraded"}
            
            # Calculate overall health score
            component_scores = []
            if health_status["database_status"] == "healthy":
                component_scores.append(1.0)
            if health_status["cache_status"] in ["healthy", "not_configured"]:
                component_scores.append(1.0)
            if health_status["authentication_integration"]:
                component_scores.append(1.0)
            
            health_status["legendary_score"] = sum(component_scores) / len(component_scores) if component_scores else 0.0
            health_status["is_healthy"] = health_status["legendary_score"] >= 0.8
            
        except Exception as e:
            health_status["database_status"] = "unhealthy"
            health_status["error"] = str(e)
            logger.error(f"Database health check failed: {e}")
        
        return health_status

# Global database manager instance
_expert_database_manager: Optional[ExpertDatabaseManager] = None
_database_manager_lock = threading.Lock()


def get_expert_database_manager() -> 'ExpertDatabaseManager':
    """
    Get the global expert database manager instance (singleton).
    Returns:
    ExpertDatabaseManager: The global database manager instance
    """
    global _expert_database_manager
    if _expert_database_manager is None:
        with _database_manager_lock:
            if _expert_database_manager is None:
                try:
                    config = UnifiedConfigSystem().get_config()
                    _expert_database_manager = ExpertDatabaseManager(config)
                except Exception:
                    # Fallback: create with minimal config if TOML config fails
                    config = {}
                    _expert_database_manager = ExpertDatabaseManager(config)
    return _expert_database_manager


def reset_expert_database_manager():
    """
    Reset the global database manager instance.
    Useful for testing or configuration changes.
    """
    global _expert_database_manager
    
    with _database_manager_lock:
        if _expert_database_manager:
            # Close any existing connections
            try:
                # Production implementation: Close database connections properly
                if hasattr(_expert_database_manager, 'close'):
                    _expert_database_manager.close()
                    logger.info("✅ Expert database manager connections closed")
                elif hasattr(_expert_database_manager, 'disconnect'):
                    _expert_database_manager.disconnect()
                    logger.info("✅ Expert database manager disconnected")
                elif hasattr(_expert_database_manager, 'engine') and hasattr(_expert_database_manager.engine, 'dispose'):
                    _expert_database_manager.engine.dispose()
                    logger.info("✅ Expert database engine disposed")
                else:
                    logger.info("✅ Expert database manager cleanup completed (no close method)")
            except Exception as e:
                logger.warning(f"⚠️ Error closing expert database manager: {e}")
            _expert_database_manager = None


# Initialize the global instance on module load
try:
    config = UnifiedConfigSystem().get_config()
    _expert_database_manager = ExpertDatabaseManager(config)
except Exception:
    _expert_database_manager = None


# Backward compatibility alias
def get_db_manager() -> ExpertDatabaseManager:
    """
    Backward compatibility alias for get_expert_database_manager.
    
    Returns:
    ExpertDatabaseManager: The global database manager instance
    """
    return get_expert_database_manager()
