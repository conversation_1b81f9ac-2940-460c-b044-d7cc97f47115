import logging
import asyncio
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import deque
import hashlib
from src.features.feature_feedback import FeatureFeedback

try:
    from src.features.feature_alchemist import SelfLearningFeatureAlchemist
    FEATURE_ALCHEMIST_AVAILABLE = True
except ImportError:
    FEATURE_ALCHEMIST_AVAILABLE = False
    SelfLearningFeatureAlchemist = None

# Optional import to avoid circular dependency
try:
    pass  # Add specific imports here if needed
except ImportError:
    # Fallback for circular import
    SelfLearningFeatureAlchemist = None

"""
GorgonWeave_Expert.py
====================
Expert-level GorgonWeave cognitive spire for advanced pattern recognition and stream fusion.

The GorgonWeave_Expert represents the most sophisticated pattern recognition system,
capable of detecting complex patterns across multiple data streams and weaving
them into cohesive predictions.
"""


logger = logging.getLogger(__name__)

@dataclass
class PatternSignature:
    """Unique signature for a detected pattern"""
    pattern_id: str
    complexity: float
    frequency: int
    last_seen: datetime
    confidence: float
    stability: float

@dataclass
class StreamData:
    """Data from a single stream"""
    stream_id: str
    data: np.ndarray
    timestamp: datetime
    quality: float
    noise_level: float

@dataclass
class WeavingResult:
    """Result of pattern weaving"""
    primary_pattern: PatternSignature
    supporting_patterns: List[PatternSignature]
    fusion_strength: float
    prediction: float
    confidence: float
    pattern_coherence: float
    stream_alignment: float

class GorgonWeave_Expert:
    """
    Expert-level GorgonWeave cognitive spire
    
    Advanced pattern recognition and stream fusion system that detects complex
    patterns across multiple data streams and weaves them into unified predictions.
    Inspired by the mythological Gorgon's ability to petrify with a glance - 
    this system 'petrifies' chaos into ordered patterns.
    """
    
    def __init__(self, max_streams: int = 12, pattern_depth: int = 20, enable_advanced_processing: bool = True, **kwargs):
        super().__init__() # Added missing super().__init__() call
        self.max_streams = max_streams
        self.pattern_depth = pattern_depth
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Pattern library
        self.pattern_library: Dict[str, PatternSignature] = {}
        self.max_patterns = 1000
        
        # Active data streams
        self.active_streams: Dict[str, deque] = {}
        self.stream_weights: Dict[str, float] = {}
        
        # Weaving matrix for stream fusion
        self.fusion_matrix = np.eye(max_streams) * 0.8 + np.random.normal(0, 0.1, (max_streams, max_streams))
        np.fill_diagonal(self.fusion_matrix, 1.0)
        
        # Pattern detection parameters
        self.detection_threshold = 0.65
        self.noise_tolerance = 0.3
        self.temporal_window = 50
        
        # Gorgon state
        self.gaze_intensity = 0.5 # How intensely we're looking for patterns
        self.petrification_strength = 0.7 # How well we convert chaos to order
        
        self.logger.info(" MEDUSA VAULT: 👁️ GorgonWeave_Expert initialized with advanced pattern recognition")
        self.feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)
    
    def weave_patterns(self, streams: Dict[str, Any]) -> WeavingResult:
        """
        Main pattern weaving method - analyzes streams and weaves patterns
        """
        try:
            # Process incoming streams
            processed_streams = self._process_streams(streams)
            
            # Detect patterns in each stream
            stream_patterns = self._detect_stream_patterns(processed_streams)
            
            # Find cross-stream correlations
            cross_patterns = self._find_cross_correlations(processed_streams)
            
            # Select primary pattern
            primary_pattern = self._select_primary_pattern(stream_patterns, cross_patterns)
            
            # Find supporting patterns
            supporting_patterns = self._find_supporting_patterns(
                primary_pattern, stream_patterns, cross_patterns
            )
            
            # Calculate fusion strength
            fusion_strength = self._calculate_fusion_strength(
                primary_pattern, supporting_patterns, processed_streams
            )
            
            # Generate prediction
            prediction = self._generate_pattern_prediction(
                primary_pattern, supporting_patterns, fusion_strength
            )
            
            # Calculate confidence
            confidence = self._calculate_pattern_confidence(
                primary_pattern, supporting_patterns, fusion_strength
            )
            
            # Assess pattern coherence
            pattern_coherence = self._assess_pattern_coherence(
                primary_pattern, supporting_patterns
            )
            
            # Calculate stream alignment
            stream_alignment = self._calculate_stream_alignment(processed_streams)
            
            # Update pattern library
            self._update_pattern_library(primary_pattern, supporting_patterns)
            
            result = WeavingResult(
                primary_pattern=primary_pattern,
                supporting_patterns=supporting_patterns,
                fusion_strength=fusion_strength,
                prediction=prediction,
                confidence=confidence,
                pattern_coherence=pattern_coherence,
                stream_alignment=stream_alignment
            )
            
            self.logger.info(f"🕸️ Patterns woven: {prediction:.3f} (confidence: {confidence:.3f})")
            return result
        
        except Exception as e:
            self.logger.error(f" Error weaving patterns: {e}")
            return self._fallback_weaving()
    
    def predict(self, game_data: Dict[str, Any]) -> Dict[str, Any]:
        """Standard prediction interface"""
        # Convert game data to streams
        streams = self._convert_to_streams(game_data)
        
        # Weave patterns
        weaving_result = self.weave_patterns(streams)
        confidence = weaving_result.confidence if hasattr(weaving_result, 'confidence') else 1.0
        # --- Feedback wiring: send feedback if confidence is low ---
        if confidence < 0.3:
            feedback = FeatureFeedback(self.__class__.__name__, game_data, confidence, message="Low confidence. Requesting feature improvement.")
            self.feature_alchemist.receive_feedback(feedback)
        return {
            'prediction': weaving_result.prediction,
            'confidence': confidence,
            'primary_pattern_id': weaving_result.primary_pattern.pattern_id,
            'primary_pattern_complexity': weaving_result.primary_pattern.complexity,
            'supporting_patterns_count': len(weaving_result.supporting_patterns),
            'fusion_strength': weaving_result.fusion_strength,
            'pattern_coherence': weaving_result.pattern_coherence,
            'stream_alignment': weaving_result.stream_alignment,
            'gaze_intensity': self.gaze_intensity,
            'petrification_strength': self.petrification_strength,
            'active_streams': list(self.active_streams.keys()),
            'pattern_library_size': len(self.pattern_library),
            'spire_type': 'GorgonWeave_Expert'
        }
    
    async def predict_async(self, game_data: Dict[str, Any]) -> Dict[str, Any]:
        """Async prediction interface"""
        return await asyncio.get_event_loop().run_in_executor(
            None, self.predict, game_data
        )
    
    def _process_streams(self, streams: Dict[str, Any]) -> List[StreamData]:
        """Process incoming data streams"""
        processed = []
        
        for stream_id, stream_value in streams.items():
            # Convert to numpy array
            if isinstance(stream_value, (int, float)):
                data = np.array([stream_value])
            elif isinstance(stream_value, list):
                data = np.array(stream_value)
            else:
                data = np.array([0.5]) # Default
            
            # Calculate stream quality
            quality = self._calculate_stream_quality(data)
            
            # Calculate noise level
            noise_level = self._calculate_noise_level(data)
            
            stream_data = StreamData(
                stream_id=stream_id,
                data=data,
                timestamp=datetime.now(),
                quality=quality,
                noise_level=noise_level
            )
            processed.append(stream_data)
            
            # Update active streams
            if stream_id not in self.active_streams:
                self.active_streams[stream_id] = deque(maxlen=self.temporal_window)
                self.stream_weights[stream_id] = 1.0
            
            self.active_streams[stream_id].append(stream_data)
            
        return processed
    
    def _detect_stream_patterns(self, streams: List[StreamData]) -> Dict[str, List[PatternSignature]]:
        """Detect patterns within individual streams"""
        stream_patterns = {}
        
        for stream in streams:
            patterns = []
            
            # Get historical data for this stream
            if stream.stream_id in self.active_streams and len(self.active_streams[stream.stream_id]) > 5:
                historical_data = [s.data for s in self.active_streams[stream.stream_id]]
                
                # Various pattern detection methods
                patterns.extend(self._detect_trend_patterns(historical_data, stream.stream_id))
                patterns.extend(self._detect_cyclical_patterns(historical_data, stream.stream_id))
                patterns.extend(self._detect_anomaly_patterns(historical_data, stream.stream_id))
            
            stream_patterns[stream.stream_id] = patterns
            
        return stream_patterns
    
    def _find_cross_correlations(self, streams: List[StreamData]) -> List[PatternSignature]:
        """Find correlations between different streams"""
        cross_patterns = []
        
        if len(streams) < 2:
            return cross_patterns
        
        # Calculate cross-correlations between streams
        for i, stream1 in enumerate(streams):
            for j, stream2 in enumerate(streams[i+1:], i+1):
                if (stream1.stream_id in self.active_streams and 
                    stream2.stream_id in self.active_streams and
                    len(self.active_streams[stream1.stream_id]) > 3 and
                    len(self.active_streams[stream2.stream_id]) > 3):
                    
                    correlation = self._calculate_stream_correlation(
                        stream1.stream_id, stream2.stream_id
                    )
                    
                    if abs(correlation) > 0.6: # Strong correlation
                        pattern_id = self._generate_pattern_id(f"cross_{stream1.stream_id}_{stream2.stream_id}")
                        
                        cross_pattern = PatternSignature(
                            pattern_id=pattern_id,
                            complexity=abs(correlation),
                            frequency=1,
                            last_seen=datetime.now(),
                            confidence=abs(correlation),
                            stability=min(stream1.quality, stream2.quality)
                        )
                        cross_patterns.append(cross_pattern)
        
        return cross_patterns
    
    def _select_primary_pattern(self, stream_patterns: Dict[str, List[PatternSignature]], 
                                cross_patterns: List[PatternSignature]) -> PatternSignature:
        """Select the primary pattern from all detected patterns"""
        all_patterns = []
        
        # Collect all patterns
        for patterns in stream_patterns.values():
            all_patterns.extend(patterns)
        all_patterns.extend(cross_patterns)
        
        if not all_patterns:
            # Create default pattern
            return PatternSignature(
                pattern_id="default_neutral",
                complexity=0.5,
                frequency=1,
                last_seen=datetime.now(),
                confidence=0.3,
                stability=0.5
            )
        
        # Score patterns by confidence * complexity * stability
        best_pattern = max(all_patterns, 
                           key=lambda p: p.confidence * p.complexity * p.stability)
        
        return best_pattern
    
    def _find_supporting_patterns(self, primary_pattern: PatternSignature,
                                  stream_patterns: Dict[str, List[PatternSignature]],
                                  cross_patterns: List[PatternSignature]) -> List[PatternSignature]:
        """Find patterns that support the primary pattern"""
        supporting = []
        
        # Collect all patterns except primary
        all_patterns = []
        for patterns in stream_patterns.values():
            all_patterns.extend(patterns)
        all_patterns.extend(cross_patterns)
        
        for pattern in all_patterns:
            if pattern.pattern_id != primary_pattern.pattern_id:
                # Check compatibility with primary pattern
                compatibility = self._calculate_pattern_compatibility(primary_pattern, pattern)
                
                if compatibility > 0.5:
                    supporting.append(pattern)
        
        # Sort by compatibility and take top 3
        supporting.sort(key=lambda p: self._calculate_pattern_compatibility(primary_pattern, p), 
                        reverse=True)
        
        return supporting[:3]
    
    def _calculate_fusion_strength(self, primary_pattern: PatternSignature,
                                   supporting_patterns: List[PatternSignature],
                                   streams: List[StreamData]) -> float:
        """Calculate the strength of pattern fusion"""
        base_strength = primary_pattern.confidence * primary_pattern.stability
        
        # Add support from supporting patterns
        support_strength = 0.0
        for pattern in supporting_patterns:
            support_strength += pattern.confidence * 0.3
        
        # Stream quality factor
        avg_quality = np.mean([s.quality for s in streams]) if streams else 0.5
        
        # Gorgon state factor
        gorgon_factor = self.gaze_intensity * self.petrification_strength
        
        fusion_strength = (base_strength + support_strength) * avg_quality * gorgon_factor
        return np.clip(fusion_strength, 0.0, 1.0)
    
    def _generate_pattern_prediction(self, primary_pattern: PatternSignature,
                                     supporting_patterns: List[PatternSignature],
                                     fusion_strength: float) -> float:
        """Generate prediction based on detected patterns"""
        # Base prediction from primary pattern complexity
        base_pred = 0.5 + (primary_pattern.complexity - 0.5) * 0.4
        
        # Adjustments from supporting patterns
        for pattern in supporting_patterns:
            adjustment = (pattern.complexity - 0.5) * 0.1
            base_pred += adjustment
        
        # Apply fusion strength
        final_pred = 0.5 + (base_pred - 0.5) * fusion_strength
        
        return np.clip(final_pred, 0.0, 1.0)
    
    def _calculate_pattern_confidence(self, primary_pattern: PatternSignature,
                                      supporting_patterns: List[PatternSignature],
                                      fusion_strength: float) -> float:
        """Calculate confidence in the pattern-based prediction"""
        # Primary pattern confidence
        primary_conf = primary_pattern.confidence * primary_pattern.stability
        
        # Supporting pattern contributions
        support_conf = 0.0
        for pattern in supporting_patterns:
            support_conf += pattern.confidence * 0.2
        
        # Fusion quality
        fusion_conf = fusion_strength * 0.5
        
        total_conf = (primary_conf + support_conf + fusion_conf) / 2.0
        return np.clip(total_conf, 0.0, 1.0)
    
    def _assess_pattern_coherence(self, primary_pattern: PatternSignature,
                                  supporting_patterns: List[PatternSignature]) -> float:
        """Assess how coherent the patterns are together"""
        if not supporting_patterns:
            return primary_pattern.stability
        
        # Calculate average compatibility between all patterns
        compatibilities = []
        
        for pattern in supporting_patterns:
            compat = self._calculate_pattern_compatibility(primary_pattern, pattern)
            compatibilities.append(compat)
        
        # Inter-supporting pattern compatibility
        for i, pattern1 in enumerate(supporting_patterns):
            for pattern2 in supporting_patterns[i+1:]:
                compat = self._calculate_pattern_compatibility(pattern1, pattern2)
                compatibilities.append(compat)
        
        return np.mean(compatibilities) if compatibilities else 0.5
    
    def _calculate_stream_alignment(self, streams: List[StreamData]) -> float:
        """Calculate how well aligned the streams are"""
        if len(streams) < 2:
            return 1.0
        
        # Calculate quality variance
        qualities = [s.quality for s in streams]
        quality_alignment = 1.0 - np.std(qualities)
        
        # Calculate noise level variance
        noise_levels = [s.noise_level for s in streams]
        noise_alignment = 1.0 - np.std(noise_levels)
        
        return np.clip((quality_alignment + noise_alignment) / 2.0, 0.0, 1.0)
    
    def _update_pattern_library(self, primary_pattern: PatternSignature,
                                supporting_patterns: List[PatternSignature]):
        """Update the pattern library with new patterns"""
        all_patterns = [primary_pattern] + supporting_patterns
        
        for pattern in all_patterns:
            if pattern.pattern_id in self.pattern_library:
                # Update existing pattern
                existing = self.pattern_library[pattern.pattern_id]
                existing.frequency += 1
                existing.last_seen = pattern.last_seen
                existing.confidence = 0.8 * existing.confidence + 0.2 * pattern.confidence
                existing.stability = 0.8 * existing.stability + 0.2 * pattern.stability
            else:
                # Add new pattern
                if len(self.pattern_library) < self.max_patterns:
                    self.pattern_library[pattern.pattern_id] = pattern
                else:
                    # Remove oldest pattern
                    oldest_id = min(self.pattern_library.keys(), 
                                    key=lambda k: self.pattern_library[k].last_seen)
                    del self.pattern_library[oldest_id]
                    self.pattern_library[pattern.pattern_id] = pattern
    
    def _convert_to_streams(self, game_data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert game data to stream format"""
        streams = {}
        
        # Map game data fields to stream names
        field_mapping = {
            'home_score': 'score_home',
            'away_score': 'score_away',
            'time_remaining': 'time',
            'momentum_shift': 'momentum',
            'home_strength': 'strength_home',
            'away_strength': 'strength_away',
            'pace': 'game_pace',
            'efficiency_differential': 'efficiency',
            'rebounding_differential': 'rebounding',
            'turnover_differential': 'turnovers'
        }
        
        for game_field, stream_name in field_mapping.items():
            if game_field in game_data:
                streams[stream_name] = game_data[game_field]
        
        return streams
    
    def _calculate_stream_quality(self, data: np.ndarray) -> float:
        """Calculate the quality of a data stream"""
        if len(data) == 0:
            return 0.0
        
        # Check for reasonable values
        if np.any(np.isnan(data)) or np.any(np.isinf(data)):
            return 0.1
        
        # Calculate stability (inverse of variance)
        if len(data) > 1:
            stability = 1.0 / (1.0 + np.var(data))
        else:
            stability = 0.5
        
        return np.clip(stability, 0.0, 1.0)
    
    def _calculate_noise_level(self, data: np.ndarray) -> float:
        """Calculate noise level in the data"""
        if len(data) < 2:
            return 0.0
        
        # Simple noise estimation using differences
        differences = np.diff(data)
        noise = np.std(differences) if len(differences) > 0 else 0.0
        
        return np.clip(noise, 0.0, 1.0)
    
    def _detect_trend_patterns(self, historical_data: List[np.ndarray], 
                               stream_id: str) -> List[PatternSignature]:
        """Detect trend patterns in historical data"""
        patterns = []
        
        if len(historical_data) < 3:
            return patterns
        
        # Calculate trend
        values = [np.mean(data) for data in historical_data]
        trend = np.polyfit(range(len(values)), values, 1)[0]
        
        if abs(trend) > 0.1: # Significant trend
            pattern_id = self._generate_pattern_id(f"trend_{stream_id}_{trend:.2f}")
            
            pattern = PatternSignature(
                pattern_id=pattern_id,
                complexity=abs(trend),
                frequency=1,
                last_seen=datetime.now(),
                confidence=min(abs(trend) * 2, 1.0),
                stability=0.7
            )
            patterns.append(pattern)
        
        return patterns
    
    def _detect_cyclical_patterns(self, historical_data: List[np.ndarray], 
                                  stream_id: str) -> List[PatternSignature]:
        """Detect cyclical patterns in historical data"""
        patterns = []
        
        if len(historical_data) < 6:
            return patterns
        
        # Simple cyclical detection using autocorrelation
        values = [np.mean(data) for data in historical_data]
        
        # Check for cycles of length 2-4
        for cycle_len in range(2, min(5, len(values)//2)):
            correlation = self._calculate_autocorrelation(values, cycle_len)
            
            if correlation > 0.6: # Strong cyclical pattern
                pattern_id = self._generate_pattern_id(f"cycle_{stream_id}_{cycle_len}")
                
                pattern = PatternSignature(
                    pattern_id=pattern_id,
                    complexity=correlation,
                    frequency=1,
                    last_seen=datetime.now(),
                    confidence=correlation,
                    stability=0.6
                )
                patterns.append(pattern)
        
        return patterns
    
    def _detect_anomaly_patterns(self, historical_data: List[np.ndarray], 
                                 stream_id: str) -> List[PatternSignature]:
        """Detect anomaly patterns in historical data"""
        patterns = []
        
        if len(historical_data) < 5:
            return patterns
        
        values = [np.mean(data) for data in historical_data]
        mean_val = np.mean(values)
        std_val = np.std(values)
        
        # Check for recent anomalies
        recent_values = values[-3:]
        for i, val in enumerate(recent_values):
            z_score = abs((val - mean_val) / std_val) if std_val > 0 else 0
            
            if z_score > 2.0: # Significant anomaly
                pattern_id = self._generate_pattern_id(f"anomaly_{stream_id}_{i}")
                
                pattern = PatternSignature(
                    pattern_id=pattern_id,
                    complexity=min(z_score / 3.0, 1.0),
                    frequency=1,
                    last_seen=datetime.now(),
                    confidence=min(z_score / 2.0, 1.0),
                    stability=0.4
                )
                patterns.append(pattern)
        
        return patterns
    
    def _calculate_stream_correlation(self, stream_id1: str, stream_id2: str) -> float:
        """Calculate correlation between two streams"""
        if (stream_id1 not in self.active_streams or 
            stream_id2 not in self.active_streams):
            return 0.0
        
        data1 = [np.mean(s.data) for s in self.active_streams[stream_id1]]
        data2 = [np.mean(s.data) for s in self.active_streams[stream_id2]]
        
        min_len = min(len(data1), len(data2))
        if min_len < 2:
            return 0.0
        
        data1 = data1[-min_len:]
        data2 = data2[-min_len:]
        
        correlation_matrix = np.corrcoef(data1, data2)
        return correlation_matrix[0, 1] if not np.isnan(correlation_matrix[0, 1]) else 0.0
    
    def _calculate_pattern_compatibility(self, pattern1: PatternSignature, 
                                         pattern2: PatternSignature) -> float:
        """Calculate compatibility between two patterns"""
        # Complexity similarity
        complexity_sim = 1.0 - abs(pattern1.complexity - pattern2.complexity)
        
        # Confidence similarity
        confidence_sim = 1.0 - abs(pattern1.confidence - pattern2.confidence)
        
        # Stability similarity
        stability_sim = 1.0 - abs(pattern1.stability - pattern2.stability)
        
        return (complexity_sim + confidence_sim + stability_sim) / 3.0
    
    def _calculate_autocorrelation(self, values: List[float], lag: int) -> float:
        """Calculate autocorrelation at given lag"""
        if len(values) < lag + 1:
            return 0.0
        
        n = len(values) - lag
        if n <= 0:
            return 0.0
        
        correlation = np.corrcoef(values[:-lag], values[lag:])[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    
    def _generate_pattern_id(self, pattern_desc: str) -> str:
        """Generate a unique pattern ID"""
        return hashlib.md5(pattern_desc.encode()).hexdigest()[:12]
    
    def _fallback_weaving(self) -> WeavingResult:
        """Fallback weaving when main process fails"""
        fallback_pattern = PatternSignature(
            pattern_id="fallback_neutral",
            complexity=0.5,
            frequency=1,
            last_seen=datetime.now(),
            confidence=0.2,
            stability=0.5
        )
        
        return WeavingResult(
            primary_pattern=fallback_pattern,
            supporting_patterns=[],
            fusion_strength=0.3,
            prediction=0.5,
            confidence=0.2,
            pattern_coherence=0.5,
            stream_alignment=0.5
        )

    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """Enable self-learning and self-correction for the spire."""
        if feedback:
            self.logger.info(f"[GorgonWeave] Received feedback: {feedback}")
            # Example: Adjust fusion matrix or detection threshold based on feedback
            if 'fusion_matrix_adjustment' in feedback:
                self.fusion_matrix += feedback['fusion_matrix_adjustment']
            if 'detection_threshold' in feedback:
                self.detection_threshold += feedback['detection_threshold']
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)
        # Self-diagnosis
        if hasattr(self, 'pattern_library') and len(self.pattern_library) > self.max_patterns:
            self.logger.warning("[GorgonWeave] Pattern library too large. Triggering self-correction.")
            self._self_correct()

    def _self_correct(self):
        """Internal logic to self-correct or reset parameters if performance is poor."""
        self.logger.info("[GorgonWeave] Performing self-correction/reset.")
        self.fusion_matrix = np.eye(self.max_streams) * 0.8 + np.random.normal(0, 0.1, (self.max_streams, self.max_streams))
        np.fill_diagonal(self.fusion_matrix, 1.0)
        self.detection_threshold = 0.65
        if not hasattr(self, 'self_correction_log'):
            self.self_correction_log = []
        self.self_correction_log.append({'timestamp': datetime.now().isoformat(), 'action': 'reset_fusion_matrix'})
    
    def get_status(self) -> Dict[str, Any]:
        """Get spire status"""
        return {
            'name': 'GorgonWeave_Expert',
            'status': 'active',
            'pattern_library_size': len(self.pattern_library),
            'active_streams_count': len(self.active_streams),
            'gaze_intensity': self.gaze_intensity,
            'petrification_strength': self.petrification_strength,
            'detection_threshold': self.detection_threshold,
            'temporal_window': self.temporal_window,
            'last_update': datetime.now().isoformat()
        }

# Export the class
__all__ = ['GorgonWeave_Expert']
