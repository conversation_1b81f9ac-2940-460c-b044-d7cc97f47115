{"integration_score": 66.66666666666666, "overall_status": "PARTIALLY_INTEGRATED", "analysis_results": {"kingdom_core": {"files_present": ["src/kingdom_architecture/medusa_kingdom_core.py", "kingdom/config/services_config.toml", "KINGDOM_ARCHITECTURE_PLAN.md"], "files_missing": [], "integration_status": "FULLY_INTEGRATED"}, "spires": {"components_present": {"cognitive_spires_factory": "src/cognitive_spires/__init__.py", "basketball_spires_manager": "src/cognitive_basketball_cortex/cognitive_spires_manager.py", "oracle_spires": "src/cognitive_spires/", "basketball_processors": "src/cognitive_basketball_cortex/"}, "components_missing": {}, "dependency_injection": "ACTIVE", "main_app_integration": "ACTIVE"}, "war_council": {"components_present": {"war_council_integration": "src/kingdom_architecture/war_council_integration.py", "war_council_simulator": "src/Battlegrounds/war_council_simulator.py", "war_council_config": "kingdom/config/services_config.toml"}, "components_missing": {}, "main_app_integration": "ACTIVE", "service_registry": "REGISTERED"}, "medusa_queen": {"components_present": {"medusa_core": "vault_oracle/core/medusa_core.py", "expert_medusa_core": "vault_oracle/runners/eternal_vigil_runner.py", "medusa_queen_adapter": "kingdom/adapters/medusa_queen_adapter.py", "autonomous_orchestrator": "src/autonomous/medusa_autonomous_orchestrator.py"}, "components_missing": {}, "main_app_integration": "ACTIVE", "supreme_authority": "ESTABLISHED"}, "service_flow": {"service_registry": "AVAILABLE", "dependency_injection": "AVAILABLE", "kingdom_services": "CONFIGURED", "communication_patterns": "ACTIVE"}, "main_application": {"kingdom_initialization": "ACTIVE", "spires_usage": "ACTIVE", "war_council_usage": "ACTIVE", "medusa_usage": "ACTIVE", "service_discovery": "ACTIVE"}}, "integration_gaps": [], "flow_issues": [], "recommendations": []}