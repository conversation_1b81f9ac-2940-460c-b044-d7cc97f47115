import logging
from fastapi import APIRouter, HTTPException
from datetime import datetime

# DEPRECATED: This file has been consolidated into unified_predictions.py
# This file is kept for reference but should not be used in production

"""
DEPRECATED - HYPER MEDUSA NEURAL VAULT - Legacy Prediction Router
================================================================

This router has been consolidated into unified_predictions.py for better
maintainability and to eliminate code duplication.

Please use backend.routers.unified_predictions instead.
"""


logger = logging.getLogger("hyper_medusa_neural_vault.deprecated_predictions")
logger.warning("⚠️ DEPRECATED: predictions.py router is deprecated. Use unified_predictions.py instead.")

# Minimal router for backward compatibility
router = APIRouter(
    prefix="/api/v1/predictions/legacy",
    tags=["Deprecated - Legacy Predictions"],
    deprecated=True
)

@router.get("/")
async def deprecated_notice():
    """Deprecation notice for legacy prediction router"""
    return {
        "status": "deprecated",
        "message": "This prediction router has been deprecated and consolidated into unified_predictions.py",
        "redirect_to": "/api/v1/predictions/unified",
        "timestamp": datetime.now()
    }
