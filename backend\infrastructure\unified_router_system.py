"""
🏀 HYPER MEDUSA NEURAL VAULT - Unified Router System
===================================================

Central router system that manages all API endpoints.
"""

from fastapi import APIRouter
from typing import List, Dict, Any
import logging
import os
import importlib

logger = logging.getLogger("UnifiedRouterSystem")

class UnifiedRouterSystem:
    """Manages all routers in a unified system"""
    
    def __init__(self):
        self.routers = {}
        self.router_registry = {}
        
    def discover_routers(self) -> List[APIRouter]:
        """Discover all available routers"""
        discovered_routers = []
        
        # Core routers
        core_routers = [
            ("predictions", "backend.routers.unified_predictions"),
            ("websocket", "backend.routers.websocket"),
            ("health", "backend.monitoring.production_health_monitor"),
        ]
        
        for name, module_path in core_routers:
            try:
                module = importlib.import_module(module_path)
                if hasattr(module, 'router'):
                    discovered_routers.append(module.router)
                    self.router_registry[name] = {
                        "module": module_path,
                        "status": "LOADED",
                        "router": module.router
                    }
                    logger.info(f"   ✅ Loaded router: {name}")
            except ImportError as e:
                logger.warning(f"   ⚠️ Could not load router {name}: {e}")
                self.router_registry[name] = {
                    "module": module_path,
                    "status": "FAILED",
                    "error": str(e)
                }
        
        return discovered_routers
    
    def get_router_status(self) -> Dict[str, Any]:
        """Get status of all routers"""
        return {
            "total_routers": len(self.router_registry),
            "loaded_routers": sum(1 for r in self.router_registry.values() if r["status"] == "LOADED"),
            "failed_routers": sum(1 for r in self.router_registry.values() if r["status"] == "FAILED"),
            "registry": self.router_registry
        }

# Global instance
unified_router_system = UnifiedRouterSystem()

def get_unified_routers() -> List[APIRouter]:
    """Get all unified routers"""
    return unified_router_system.discover_routers()

def get_router_system_status() -> Dict[str, Any]:
    """Get router system status"""
    return unified_router_system.get_router_status()
