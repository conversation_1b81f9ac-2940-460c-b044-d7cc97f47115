import sqlite3
import os

print("🔍 Finding Correct Table for Player Game Stats")
print("=" * 50)

db_file = "hyper_medusa_consolidated.db"
if os.path.exists(db_file):
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = [t[0] for t in cursor.fetchall()]
    print(f"📊 All tables: {tables}")
    
    # Look for tables with player game data
    for table in tables:
        if 'player' in table.lower() or 'game' in table.lower() or 'stat' in table.lower():
            print(f"\n🎯 Checking table: {table}")
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   Records: {count:,}")
            
            # Check if it has points/stats columns
            cursor.execute(f"PRAGMA table_info({table})")
            columns = [col[1] for col in cursor.fetchall()]
            stat_columns = [col for col in columns if any(stat in col.lower() for stat in ['point', 'assist', 'rebound', 'hero', 'league'])]
            if stat_columns:
                print(f"   Key columns: {stat_columns}")
                
                # Check for league data
                if 'league' in columns:
                    cursor.execute(f"SELECT DISTINCT league FROM {table} WHERE league IS NOT NULL LIMIT 5")
                    leagues = [row[0] for row in cursor.fetchall()]
                    print(f"   Leagues: {leagues}")
    
    conn.close()
