import asyncio
import logging
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from datetime import datetime, timedelta, timezone # Import timezone
from typing import Dict, List, Optional, Any, Tuple, Union
from dataclasses import dataclass, field
from pathlib import Path
import json
import sqlite3
from abc import ABC, abstractmethod
import pickle
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from src.utils.safe_logging import get_safe_logger


#!/usr/bin/env python3
"""
 HYPER MEDUSA NEURAL VAULT - Advanced AI Model Development and Prediction
==========================================================================

Phase 3: Advanced AI Model Development & Prediction Integration
Building sophisticated ML models that leverage all Phase 2 data and
implement autonomous learning capabilities, now integrated with a
real-time prediction engine.

Features:
- Multi-Modal Deep Learning Models
- Transformer-based Prediction Architecture
- Real-time Model Ensemble Optimization
- Cognitive Spires Integration for enhanced reasoning
- Dynamic Architecture Evolution (future expansion)
- Cross-League Transfer Learning (future expansion)
- Integrated Prediction Engine for deploying and using trained models
"""


# Configure safe logging for Windows
logger = get_safe_logger('advanced_ai_models')

@dataclass
class ModelArchitectureConfig:
    """Configuration for AI model architecture"""
    hidden_dimensions: List[int] = field(default_factory=lambda: [512, 256, 128, 64])
    attention_heads: int = 8
    transformer_layers: int = 6
    dropout_rate: float = 0.15
    sequence_length: int = 20
    feature_dimensions: int = 150
    league_embedding_dim: int = 32
    player_embedding_dim: int = 64

@dataclass
class TrainingConfig:
    """Configuration for model training"""
    epochs: int = 100
    early_stopping_patience: int = 15
    validation_split: float = 0.2
    l2_regularization: float = 0.001
    gradient_clipping: float = 1.0
    scheduler_step_size: int = 20
    scheduler_gamma: float = 0.8
    warmup_epochs: int = 5
    batch_size: int = 64
    learning_rate: float = 0.001 # Moved from ModelArchitectureConfig

class HyperMedusaDataset(Dataset):
    """Custom dataset for HYPER MEDUSA training data"""

    def __init__(self,
                 features: np.ndarray,
                 targets: np.ndarray,
                 league_ids: np.ndarray,
                 player_ids: np.ndarray,
                 sequence_length: int = 20):
        self.features = torch.FloatTensor(features)
        self.targets = torch.FloatTensor(targets)
        self.league_ids = torch.LongTensor(league_ids)
        self.player_ids = torch.LongTensor(player_ids)
        self.sequence_length = sequence_length

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        return {
            'features': self.features[idx],
            'targets': self.targets[idx],
            'league_id': self.league_ids[idx],
            'hero_id': self.player_ids[idx]
        }

class MultiModalAttentionBlock(nn.Module):
    """Multi-modal attention block for basketball intelligence"""

    def __init__(self,
                 feature_dim: int,
                 attention_heads: int = 8,
                 dropout_rate: float = 0.1):
        super().__init__()

        self.attention = nn.MultiheadAttention(
            embed_dim=feature_dim,
            num_heads=attention_heads,
            dropout=dropout_rate,
            batch_first=True
        )

        self.norm1 = nn.LayerNorm(feature_dim)
        self.norm2 = nn.LayerNorm(feature_dim)

        self.feed_forward = nn.Sequential(
            nn.Linear(feature_dim, feature_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout_rate),
            nn.Linear(feature_dim * 4, feature_dim),
            nn.Dropout(dropout_rate)
        )

    def forward(self, x):
        # Self-attention
        attended, _ = self.attention(x, x, x)
        x = self.norm1(x + attended)

        # Feed-forward
        ff_output = self.feed_forward(x)
        x = self.norm2(x + ff_output)

        return x

class BasketballTransformer(nn.Module):
    """Advanced Transformer model for basketball predictions"""

    def __init__(self, config: ModelArchitectureConfig):
        super().__init__()
        self.config = config

        # Embedding layers
        # Increased embedding size for more granularity for league_id
        self.league_embedding = nn.Embedding(3, config.league_embedding_dim) # NBA, WNBA, Both (0, 1, 2)
        # Increased max players for future expansion
        self.player_embedding = nn.Embedding(20000, config.player_embedding_dim)

        # Feature projection
        total_input_dim = (config.feature_dimensions +
                           config.league_embedding_dim +
                           config.player_embedding_dim)

        self.input_projection = nn.Linear(total_input_dim, config.hidden_dimensions[0])

        # Transformer blocks
        self.transformer_blocks = nn.ModuleList([
            MultiModalAttentionBlock(
                feature_dim=config.hidden_dimensions[0],
                attention_heads=config.attention_heads,
                dropout_rate=config.dropout_rate
            )
            for _ in range(config.transformer_layers)
        ])

        # Output layers
        self.output_layers = nn.ModuleList()
        prev_dim = config.hidden_dimensions[0]

        for dim in config.hidden_dimensions[1:]:
            self.output_layers.append(nn.Sequential(
                nn.Linear(prev_dim, dim),
                nn.GELU(),
                nn.Dropout(config.dropout_rate),
                nn.BatchNorm1d(dim) # BatchNorm1d for flat features
            ))
            prev_dim = dim

        # Final prediction heads
        self.prediction_head = nn.Linear(prev_dim, 1)
        self.confidence_head = nn.Linear(prev_dim, 1)

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize model weights"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, mean=0, std=0.1)

    def forward(self, features, league_ids, player_ids):
        batch_size = features.size(0)

        # Get embeddings
        league_emb = self.league_embedding(league_ids)
        player_emb = self.player_embedding(player_ids)

        # Concatenate all features
        combined_features = torch.cat([features, league_emb, player_emb], dim=-1)

        # Project to hidden dimension
        x = self.input_projection(combined_features)

        # Add sequence dimension for transformer (if not already present and required)
        # For a single time-step prediction, unsqueeze(1) makes it a sequence of length 1
        x = x.unsqueeze(1) # [batch_size, 1, hidden_dim]

        # Apply transformer blocks
        for transformer_block in self.transformer_blocks:
            x = transformer_block(x)

        # Remove sequence dimension
        x = x.squeeze(1) # [batch_size, hidden_dim]

        # Apply output layers
        for layer in self.output_layers:
            x = layer(x)

        # Generate predictions
        prediction = self.prediction_head(x)
        confidence = torch.sigmoid(self.confidence_head(x))

        return prediction, confidence

class EnsembleAdvancedModel(nn.Module):
    """Ensemble of advanced models with dynamic weighting"""

    def __init__(self,
                 config: ModelArchitectureConfig,
                 num_models: int = 5):
        super().__init__()
        self.num_models = num_models
        self.config = config

        # Create ensemble of models
        self.models = nn.ModuleList([
            BasketballTransformer(config) for _ in range(num_models)
        ])

        # Dynamic weighting network
        self.weight_network = nn.Sequential(
            nn.Linear(config.feature_dimensions, 64),
            nn.ReLU(),
            nn.Linear(64, num_models),
            nn.Softmax(dim=-1) # Softmax to ensure weights sum to 1
        )

    def forward(self, features, league_ids, player_ids):
        # Get predictions from all models
        predictions = []
        confidences = []

        for model in self.models:
            pred, conf = model(features, league_ids, player_ids)
            predictions.append(pred)
            confidences.append(conf)

        # Stack predictions
        predictions = torch.stack(predictions, dim=-1) # [batch_size, 1, num_models]
        confidences = torch.stack(confidences, dim=-1) # [batch_size, 1, num_models]

        # Get dynamic weights
        # Reshape features to match input for weight network
        weights = self.weight_network(features).unsqueeze(1) # [batch_size, 1, num_models]

        # Weighted ensemble
        ensemble_prediction = torch.sum(predictions * weights, dim=-1)
        ensemble_confidence = torch.sum(confidences * weights, dim=-1)

        return ensemble_prediction, ensemble_confidence, weights

class AdvancedAIModelDevelopment:
    """Advanced AI Model Development System"""

    def __init__(self,
                 model_config: ModelArchitectureConfig = None,
                 training_config: TrainingConfig = None):
        self.model_config = model_config or ModelArchitectureConfig()
        self.training_config = training_config or TrainingConfig()
        self.database_path = "hyper_medusa_neural_vault.db"
        self.models_path = Path("src/phase3/models")
        self.models_path.mkdir(parents=True, exist_ok=True)

        # Training components
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.scaler = StandardScaler() # Used for feature normalization
        self.models = {} # Stores trained models
        self.training_history = {} # Stores training metrics
        self.scalers = {} # Stores scalers for each model's features

        logger.info(f" Advanced AI Model Development initialized on {self.device}")

    async def create_advanced_model(self, model_name: str, model_type: str = "transformer") -> nn.Module:
        """Create an advanced AI model"""
        logger.info(f"🏗️ Creating advanced {model_type} model: {model_name}")

        if model_type == "transformer":
            model = BasketballTransformer(self.model_config)
        elif model_type == "ensemble":
            model = EnsembleAdvancedModel(self.model_config)
        else:
            raise ValueError(f"Unknown model type: {model_type}")

        model = model.to(self.device)
        self.models[model_name] = model

        logger.info(f" Model {model_name} created with {sum(p.numel() for p in model.parameters())} parameters")
        return model

    async def prepare_training_data(self) -> Tuple[DataLoader, DataLoader, StandardScaler]:
        """Prepare training data from Phase 2 systems and return the scaler"""
        logger.info(" MEDUSA VAULT: Preparing training data from Phase 2 systems...")

        # Load data from database
        features, targets, league_ids, player_ids = await self._load_training_data()

        # Normalize features and store the scaler
        features_normalized = self.scaler.fit_transform(features)
        self.scalers['main_scaler'] = self.scaler # Store the scaler for later use in prediction

        # Create dataset
        dataset = HyperMedusaDataset(
            features=features_normalized,
            targets=targets,
            league_ids=league_ids,
            player_ids=player_ids,
            sequence_length=self.model_config.sequence_length
        )

        # Split into train/validation
        train_size = int((1 - self.training_config.validation_split) * len(dataset))
        val_size = len(dataset) - train_size

        train_dataset, val_dataset = torch.utils.data.random_split(
            dataset, [train_size, val_size]
        )

        # Create data loaders
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.training_config.batch_size, # Corrected access
            shuffle=True,
            num_workers=2
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=self.training_config.batch_size, # Corrected access
            shuffle=False,
            num_workers=2
        )

        logger.info(f" Training data prepared: {len(train_dataset)} train, {len(val_dataset)} validation samples")
        return train_loader, val_loader, self.scaler

    async def _load_training_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Load training data from database with comprehensive fallback strategies"""
        try:
            with sqlite3.connect(self.database_path) as conn:
                query = """
                SELECT p.player_props, p.advanced_metrics, p.spatial_factors,
                p.base_prediction, p.confidence, p.league, p.hero_id,
                COALESCE(g.home_score, g.away_score, 0) as actual_outcome
                FROM phase2_integrated_predictions p
                LEFT JOIN nba_games g ON p.titan_clash_id = g.titan_clash_id
                WHERE p.advanced_metrics IS NOT NULL
                AND p.base_prediction IS NOT NULL
                LIMIT 10000
                """
                df = pd.read_sql_query(query, conn)

                if df.empty:
                    logger.warning("No data found in database, attempting fallback data loading.")
                    return await self._load_fallback_training_data()

                features_list = []
                targets_list = []
                league_ids_list = []
                player_ids_list = []

                for _, row in df.iterrows():
                    try:
                        player_props = json.loads(row['player_props']) if row['player_props'] else {}
                        advanced_metrics = json.loads(row['advanced_metrics']) if row['advanced_metrics'] else {}
                        spatial_factors = json.loads(row['spatial_factors']) if row['spatial_factors'] else {}

                        feature_vector = self._create_feature_vector(
                            player_props, advanced_metrics, spatial_factors
                        )

                        features_list.append(feature_vector)
                        targets_list.append(row['actual_outcome'])

                        league_mapping = {'NBA': 0, 'WNBA': 1}
                        league_id = league_mapping.get(row['league'], 2) # Default to 2 for 'Both' or unknown
                        league_ids_list.append(league_id)

                        hero_id = hash(str(row.get('hero_id', 'unknown'))) % 19999 # Increased player ID range
                        player_ids_list.append(hero_id)

                    except Exception as e:
                        logger.warning(f"Error processing row: {e}. Skipping.")
                        continue

                return (
                    np.array(features_list, dtype=np.float32),
                    np.array(targets_list, dtype=np.float32),
                    np.array(league_ids_list, dtype=np.int64),
                    np.array(player_ids_list, dtype=np.int64)
                )

        except Exception as e:
            logger.error(f"Error loading training data from database: {e}. Attempting fallback data loading.")
            return await self._load_fallback_training_data()

    async def _load_fallback_training_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Load training data from alternative sources when primary database fails"""
        try:
            # Try to load from unified_nba_wnba_data table as fallback
            with sqlite3.connect(self.database_path) as conn:
                fallback_query = """
                SELECT
                    points, rebounds, assists, steals, blocks, turnovers,
                    fg_percent, ft_percent, three_pt_made, minutes_played,
                    usage_rate, true_shooting_percent, player_efficiency_rating,
                    defensive_rating, offensive_rating, win_shares,
                    team_wins, team_losses, home_advantage, rest_days,
                    opponent_defensive_rating, season_progress, league_name,
                    player_name, team_abbreviation
                FROM unified_nba_wnba_data
                WHERE points IS NOT NULL
                AND rebounds IS NOT NULL
                AND minutes_played > 0
                AND league_name IN ('NBA', 'WNBA')
                ORDER BY RANDOM()
                LIMIT 5000
                """
                df = pd.read_sql_query(fallback_query, conn)

                if not df.empty:
                    logger.info(f"✅ Fallback: Loaded {len(df)} samples from unified_nba_wnba_data")
                    return self._process_unified_data(df)

        except Exception as e:
            logger.warning(f"Fallback data loading failed: {e}")

        # Final fallback: generate realistic training data based on basketball statistics
        logger.warning("All data sources failed, generating realistic basketball training data")
        return self._generate_realistic_training_data()

    def _process_unified_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Process unified NBA/WNBA data into training format"""
        features_list = []
        targets_list = []
        league_ids_list = []
        player_ids_list = []

        for _, row in df.iterrows():
            # Create comprehensive feature vector
            feature_vector = [
                float(row.get('points', 0)), float(row.get('rebounds', 0)),
                float(row.get('assists', 0)), float(row.get('steals', 0)),
                float(row.get('blocks', 0)), float(row.get('turnovers', 0)),
                float(row.get('fg_percent', 0.45)), float(row.get('ft_percent', 0.75)),
                float(row.get('three_pt_made', 0)), float(row.get('minutes_played', 20)),
                float(row.get('usage_rate', 0.2)), float(row.get('true_shooting_percent', 0.55)),
                float(row.get('player_efficiency_rating', 15)), float(row.get('defensive_rating', 110)),
                float(row.get('offensive_rating', 110)), float(row.get('win_shares', 0.1)),
                float(row.get('team_wins', 41)), float(row.get('team_losses', 41)),
                float(row.get('home_advantage', 0)), float(row.get('rest_days', 1)),
                float(row.get('opponent_defensive_rating', 110)), float(row.get('season_progress', 0.5))
            ]

            # Performance-based target classification
            points = float(row.get('points', 0))
            efficiency = float(row.get('player_efficiency_rating', 15))
            target = 1 if points >= 20 and efficiency >= 18 else 0

            features_list.append(feature_vector)
            targets_list.append(target)

            # League encoding
            league_mapping = {'NBA': 0, 'WNBA': 1}
            league_id = league_mapping.get(row.get('league_name', 'NBA'), 0)
            league_ids_list.append(league_id)

            # Player ID encoding
            player_name = str(row.get('player_name', 'unknown'))
            player_id = hash(player_name) % 19999
            player_ids_list.append(player_id)

        return (
            np.array(features_list, dtype=np.float32),
            np.array(targets_list, dtype=np.float32),
            np.array(league_ids_list, dtype=np.int64),
            np.array(player_ids_list, dtype=np.int64)
        )

    def _generate_realistic_training_data(self) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Generate realistic basketball training data based on statistical distributions"""
        logger.info("🏀 MEDUSA VAULT: Generating realistic basketball training data from statistical models")

        num_samples = 5000  # Increased sample size for better training
        feature_dim = self.model_config.feature_dimensions

        # Generate realistic basketball statistics based on NBA/WNBA distributions
        np.random.seed(42)

        # Core basketball statistics with realistic distributions
        points = np.clip(np.random.gamma(2, 8), 0, 60)  # Points: 0-60, avg ~16
        rebounds = np.clip(np.random.gamma(1.5, 3), 0, 25)  # Rebounds: 0-25, avg ~4.5
        assists = np.clip(np.random.gamma(1.2, 2.5), 0, 20)  # Assists: 0-20, avg ~3
        steals = np.clip(np.random.gamma(0.8, 1.2), 0, 8)  # Steals: 0-8, avg ~1
        blocks = np.clip(np.random.gamma(0.6, 1), 0, 8)  # Blocks: 0-8, avg ~0.6
        turnovers = np.clip(np.random.gamma(1, 2), 0, 10)  # Turnovers: 0-10, avg ~2

        # Shooting percentages with realistic correlations
        fg_percent = np.clip(np.random.beta(4, 4), 0.2, 0.7)  # FG%: 20-70%, avg ~50%
        ft_percent = np.clip(np.random.beta(6, 3), 0.5, 1.0)  # FT%: 50-100%, avg ~75%
        three_pt_made = np.clip(np.random.poisson(1.5), 0, 10)  # 3PM: 0-10, avg ~1.5

        # Minutes played affects other stats
        minutes_played = np.clip(np.random.gamma(3, 8), 5, 48)  # Minutes: 5-48, avg ~24

        # Advanced metrics with correlations
        usage_rate = np.clip(np.random.beta(2, 5), 0.1, 0.4)  # Usage: 10-40%, avg ~20%
        true_shooting = np.clip(fg_percent + np.random.normal(0, 0.05), 0.3, 0.8)  # TS%
        player_efficiency = np.clip(
            (points * 0.8 + rebounds * 0.6 + assists * 0.7) / minutes_played * 10 +
            np.random.normal(0, 3), 0, 40
        )  # PER: 0-40, realistic calculation

        # Team and situational factors
        defensive_rating = np.clip(np.random.normal(110, 8), 90, 130)  # DefRtg: 90-130
        offensive_rating = np.clip(np.random.normal(110, 10), 85, 135)  # OffRtg: 85-135
        win_shares = np.clip(np.random.gamma(1, 3), 0, 20)  # Win Shares: 0-20

        team_wins = np.random.randint(10, 70, num_samples)  # Team wins: 10-70
        team_losses = 82 - team_wins  # Assuming 82-game season
        home_advantage = np.random.binomial(1, 0.5, num_samples)  # Home/Away
        rest_days = np.random.poisson(1.5, num_samples)  # Rest days
        opponent_defensive_rating = np.clip(np.random.normal(110, 8), 90, 130)
        season_progress = np.random.uniform(0, 1, num_samples)  # Season progress: 0-100%

        # Combine all features into comprehensive feature vectors
        features_list = []
        for i in range(num_samples):
            feature_vector = [
                points[i], rebounds[i], assists[i], steals[i], blocks[i], turnovers[i],
                fg_percent[i], ft_percent[i], three_pt_made[i], minutes_played[i],
                usage_rate[i], true_shooting[i], player_efficiency[i],
                defensive_rating[i], offensive_rating[i], win_shares[i],
                team_wins[i], team_losses[i], home_advantage[i], rest_days[i],
                opponent_defensive_rating[i], season_progress[i]
            ]

            # Pad or truncate to match expected feature dimensions
            if len(feature_vector) < feature_dim:
                feature_vector.extend([0.0] * (feature_dim - len(feature_vector)))
            else:
                feature_vector = feature_vector[:feature_dim]

            features_list.append(feature_vector)

        features = np.array(features_list, dtype=np.float32)

        # Generate realistic performance targets based on comprehensive metrics
        targets = []
        for i in range(num_samples):
            # Multi-factor performance score
            performance_score = (
                points[i] * 0.3 +  # Points contribution
                player_efficiency[i] * 0.25 +  # Efficiency contribution
                (rebounds[i] + assists[i]) * 0.2 +  # Rebounds + assists
                (steals[i] + blocks[i]) * 0.15 +  # Defensive stats
                (minutes_played[i] / 48) * 0.1  # Playing time factor
            )

            # Classify into performance tiers
            if performance_score >= 25:
                target = 2  # Elite performance
            elif performance_score >= 15:
                target = 1  # Good performance
            else:
                target = 0  # Average performance

            targets.append(target)

        # Generate realistic league and player distributions
        # 70% NBA, 30% WNBA to reflect data availability
        league_ids = np.random.choice([0, 1], num_samples, p=[0.7, 0.3]).astype(np.int64)

        # Player IDs with realistic distribution
        player_ids = np.random.randint(1, 20000, num_samples, dtype=np.int64)

        logger.info(f"✅ Generated {num_samples} realistic basketball training samples")
        logger.info(f"📊 Performance distribution - Elite: {np.sum(np.array(targets) == 2)}, Good: {np.sum(np.array(targets) == 1)}, Average: {np.sum(np.array(targets) == 0)}")

        return features, np.array(targets, dtype=np.float32), league_ids, player_ids

    def _create_feature_vector(self,
                               player_props: Dict[str, Any],
                               advanced_metrics: Dict[str, Any],
                               spatial_factors: Dict[str, Any]) -> List[float]:
        """Create a consistent feature vector from various data components"""
        features = []

        # Player props features (first 50 dimensions)
        prop_features = [0.0] * 50
        if player_props:
            for i, (key, value) in enumerate(list(player_props.items())[:50]):
                if isinstance(value, (int, float)):
                    prop_features[i] = float(value)
        features.extend(prop_features)

        # Advanced metrics features (next 50 dimensions)
        metric_features = [0.0] * 50
        if advanced_metrics:
            for i, (key, value) in enumerate(list(advanced_metrics.items())[:50]):
                if isinstance(value, (int, float)):
                    metric_features[i] = float(value)
        features.extend(metric_features)

        # Spatial factors features (next 50 dimensions)
        spatial_features = [0.0] * 50
        if spatial_factors:
            for i, (key, value) in enumerate(list(spatial_factors.items())[:50]):
                if isinstance(value, (int, float)):
                    spatial_features[i] = float(value)
        features.extend(spatial_features)

        # Ensure we have exactly the right number of features, pad with zeros if less
        while len(features) < self.model_config.feature_dimensions:
            features.append(0.0)

        # Truncate if too many features, though the design should prevent this with proper slicing
        return features[:self.model_config.feature_dimensions]

    async def train_model(self,
                          model_name: str,
                          train_loader: DataLoader,
                          val_loader: DataLoader) -> Dict[str, Any]:
        """Train an advanced AI model"""
        logger.info(f" Starting training for model: {model_name}")

        model = self.models[model_name]
        model.train()

        # Setup optimizer and scheduler
        optimizer = optim.AdamW(
            model.parameters(),
            lr=self.training_config.learning_rate,
            weight_decay=self.training_config.l2_regularization
        )

        scheduler = optim.lr_scheduler.StepLR(
            optimizer,
            step_size=self.training_config.scheduler_step_size,
            gamma=self.training_config.scheduler_gamma
        )

        # Training loop
        best_val_loss = float('inf')
        patience_counter = 0
        training_history = {
            'train_losses': [],
            'val_losses': [],
            'train_accuracies': [],
            'val_accuracies': []
        }

        for epoch in range(self.training_config.epochs):
            # Training phase
            train_loss, train_acc = await self._train_epoch(
                model, train_loader, optimizer, epoch
            )

            # Validation phase
            val_loss, val_acc = await self._validate_epoch(
                model, val_loader, epoch
            )

            # Update scheduler
            scheduler.step()

            # Save metrics
            training_history['train_losses'].append(train_loss)
            training_history['val_losses'].append(val_loss)
            training_history['train_accuracies'].append(train_acc)
            training_history['val_accuracies'].append(val_acc)

            # Early stopping check
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                patience_counter = 0
                # Save best model and its associated scaler
                await self._save_model(model_name, model, self.scalers.get('main_scaler'), epoch, val_loss)
            else:
                patience_counter += 1

            if patience_counter >= self.training_config.early_stopping_patience:
                logger.info(f"Early stopping triggered at epoch {epoch}")
                break

            # Log progress
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: Train Loss {train_loss:.4f}, Val Loss {val_loss:.4f}, "
                            f"Train Acc {train_acc:.4f}, Val Acc {val_acc:.4f}")

        self.training_history[model_name] = training_history

        logger.info(f" Training completed for {model_name}")
        return training_history

    async def _train_epoch(self,
                            model: nn.Module,
                            train_loader: DataLoader,
                            optimizer: optim.Optimizer,
                            epoch: int) -> Tuple[float, float]:
        """Train for one epoch"""
        model.train()
        total_loss = 0
        predictions = []
        targets = []

        for batch_idx, batch in enumerate(train_loader):
            # Move data to device
            features = batch['features'].to(self.device)
            target = batch['targets'].to(self.device).unsqueeze(1)
            league_ids = batch['league_id'].to(self.device)
            player_ids = batch['hero_id'].to(self.device)

            # Forward pass
            optimizer.zero_grad()

            if isinstance(model, EnsembleAdvancedModel):
                pred, conf, weights = model(features, league_ids, player_ids)
            else:
                pred, conf = model(features, league_ids, player_ids)

            # Calculate loss
            loss = nn.MSELoss()(pred, target)

            # Add confidence regularization: encourage confident and accurate predictions
            # If confidence is high but prediction is far off, penalize more.
            # If confidence is low but prediction is accurate, encourage more confidence.
            # Simple approach: penalize large confidence if error is also large.
            error = torch.abs(pred - target)
            confidence_loss = torch.mean(conf * error) + torch.mean((1 - conf) * (1 - error)) # Example, can be more complex

            total_loss_batch = loss + 0.05 * confidence_loss # Adjust weight of confidence loss

            # Backward pass
            total_loss_batch.backward()

            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(
                model.parameters(),
                self.training_config.gradient_clipping
            )

            optimizer.step()

            total_loss += loss.item()
            predictions.extend(pred.detach().cpu().numpy())
            targets.extend(target.detach().cpu().numpy())

        avg_loss = total_loss / len(train_loader)
        accuracy = self._calculate_accuracy(predictions, targets)

        return avg_loss, accuracy

    async def _validate_epoch(self,
                               model: nn.Module,
                               val_loader: DataLoader,
                               epoch: int) -> Tuple[float, float]:
        """Validate for one epoch"""
        model.eval()
        total_loss = 0
        predictions = []
        targets = []

        with torch.no_grad():
            for batch in val_loader:
                # Move data to device
                features = batch['features'].to(self.device)
                target = batch['targets'].to(self.device).unsqueeze(1)
                league_ids = batch['league_id'].to(self.device)
                player_ids = batch['hero_id'].to(self.device)

                # Forward pass
                if isinstance(model, EnsembleAdvancedModel):
                    pred, conf, weights = model(features, league_ids, player_ids)
                else:
                    pred, conf = model(features, league_ids, player_ids)

                # Calculate loss
                loss = nn.MSELoss()(pred, target)

                total_loss += loss.item()
                predictions.extend(pred.cpu().numpy())
                targets.extend(target.cpu().numpy())

        avg_loss = total_loss / len(val_loader)
        accuracy = self._calculate_accuracy(predictions, targets)

        return avg_loss, accuracy

    def _calculate_accuracy(self, predictions: List[float], targets: List[float]) -> float:
        """Calculate accuracy for regression predictions, considering a margin of error"""
        predictions = np.array(predictions).flatten()
        targets = np.array(targets).flatten()

        # For regression, use relative error within 15% as "correct"
        # Avoid division by zero by adding a small epsilon
        relative_errors = np.abs(predictions - targets) / (np.abs(targets) + 1e-8)
        accuracy = np.mean(relative_errors < 0.15) # Within 15% margin

        return accuracy

    async def _save_model(self,
                           model_name: str,
                           model: nn.Module,
                           scaler: StandardScaler, # Pass scaler to save
                           epoch: int,
                           val_loss: float):
        """Save model checkpoint along with its associated StandardScaler"""
        checkpoint = {
            'model_name': model_name,
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'val_loss': val_loss,
            'model_config': self.model_config,
            'training_config': self.training_config,
            'scaler': pickle.dumps(scaler), # Serialize scaler using pickle
            'timestamp': datetime.now(timezone.utc).isoformat() # Updated to use datetime.now(timezone.utc)
        }

        checkpoint_path = self.models_path / f"{model_name}_best.pth"
        torch.save(checkpoint, checkpoint_path)

        logger.info(f" Model checkpoint saved: {checkpoint_path}")

    async def load_model(self, model_name: str) -> Optional[nn.Module]:
        """Load a trained model and its scaler"""
        checkpoint_path = self.models_path / f"{model_name}_best.pth"
        if not checkpoint_path.exists():
            logger.warning(f"Model checkpoint not found for {model_name} at {checkpoint_path}")
            return None

        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            loaded_model_config = checkpoint['model_config']
            loaded_model_type = "ensemble" if "ensemble" in model_name else "transformer" # Infer type
            model = await self.create_advanced_model(model_name, loaded_model_type) # Recreate model architecture
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval() # Set to evaluation mode

            self.scalers['main_scaler'] = pickle.loads(checkpoint['scaler']) # Deserialize scaler
            logger.info(f" Model {model_name} loaded successfully from {checkpoint_path}")
            return model
        except Exception as e:
            logger.error(f"Error loading model {model_name} from {checkpoint_path}: {e}")
            return None

    def get_model_status(self) -> Dict[str, Any]:
        """Get status of all models"""
        return {
            'available_models': list(self.models.keys()),
            'training_history_summaries': {
                name: {
                    'epochs_trained': len(history['train_losses']),
                    'last_val_loss': history['val_losses'][-1] if history['val_losses'] else 'N/A',
                    'last_val_accuracy': history['val_accuracies'][-1] if history['val_accuracies'] else 'N/A',
                }
                for name, history in self.training_history.items()
            },
            'device': str(self.device),
            'total_parameters': {
                name: sum(p.numel() for p in model.parameters())
                for name, model in self.models.items()
            },
            'scaler_available': 'main_scaler' in self.scalers
        }


# --- Prediction and Integration Components ---

class TransformerBasketballModel(BasketballTransformer):
    """Transformer-based basketball prediction model (alias for compatibility)"""

    def __init__(self, config: ModelArchitectureConfig):
        super().__init__(config)
        self.model_name = "TransformerBasketballModel"

    def predict(self, features: torch.Tensor, league_ids: torch.Tensor, player_ids: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Make predictions with the transformer model"""
        self.eval() # Set model to evaluation mode
        with torch.no_grad():
            prediction, confidence = self.forward(features, league_ids, player_ids)
            return prediction, confidence


class CognitiveSpire:
    """Cognitive Spire for advanced reasoning and prediction fusion"""

    def __init__(self, name: str, capabilities: List[str]):
        self.name = name
        self.capabilities = capabilities
        self.activation_level = 0.0
        self.prediction_history = [] # Stores processed predictions for logging/analysis

    def activate(self, confidence_level: float):
        """Activate the cognitive spire with given confidence, capping at 1.0"""
        self.activation_level = min(1.0, max(0.0, confidence_level)) # Ensure 0.0 <= level <= 1.0
        logger.info(f"[BRAIN] Cognitive Spire '{self.name}' activated at {self.activation_level:.2%}")

    def process_prediction(self, raw_prediction: Dict[str, Any]) -> Dict[str, Any]:
        """Process prediction through cognitive reasoning, adding enhancements"""
        # Example of cognitive enhancement: adjust prediction based on reasoning depth
        adjusted_prediction_value = raw_prediction['prediction_value'] * (1 + (self.activation_level * 0.05)) # Small adjustment

        enhanced_prediction = {
            **raw_prediction,
            'prediction_value': adjusted_prediction_value, # Updated prediction value
            'spire_enhancement': {
                'name': self.name,
                'activation_level': self.activation_level,
                'capabilities_applied': self.capabilities,
                'reasoning_depth_factor': len(self.capabilities) * self.activation_level,
                'timestamp': datetime.now(timezone.utc).isoformat() # Updated to use datetime.now(timezone.utc)
            }
        }

        self.prediction_history.append(enhanced_prediction)
        return enhanced_prediction

    def get_status(self) -> Dict[str, Any]:
        """Get cognitive spire status"""
        return {
            'name': self.name,
            'activation_level': self.activation_level,
            'capabilities': self.capabilities,
            'predictions_processed': len(self.prediction_history),
            'average_enhancement_factor': sum(p.get('spire_enhancement', {}).get('reasoning_depth_factor', 0)
                                              for p in self.prediction_history) / max(len(self.prediction_history), 1)
        }


class EnsemblePredictor:
    """Advanced ensemble prediction model with dynamic weighting and performance tracking"""

    def __init__(self, models: List[nn.Module]):
        self.models = models
        # Initial weights can be uniform, or set by a meta-learner
        self.weights = [1.0 / len(models)] * len(models) if models else []
        self.performance_history = [] # To track how weights are updated based on past performance

    def predict(self, features: torch.Tensor, league_ids: torch.Tensor, player_ids: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Make ensemble predictions by averaging or weighting individual model outputs"""
        if not self.models:
            raise ValueError("No models in the ensemble to make predictions.")

        all_predictions = []
        all_confidences = []

        for model in self.models:
            model.eval() # Ensure model is in evaluation mode
            with torch.no_grad():
                pred, conf = model(features, league_ids, player_ids)
                all_predictions.append(pred)
                all_confidences.append(conf)

        # Simple weighted average for now; could be replaced by a learned aggregation
        ensemble_pred = torch.sum(torch.stack(all_predictions, dim=-1) *
                                  torch.tensor(self.weights, device=features.device).unsqueeze(0).unsqueeze(0), dim=-1)
        ensemble_conf = torch.sum(torch.stack(all_confidences, dim=-1) *
                                  torch.tensor(self.weights, device=features.device).unsqueeze(0).unsqueeze(0), dim=-1)

        return ensemble_pred, ensemble_conf

    def update_weights(self, performances: List[float]):
        """
        Update ensemble weights based on individual model performances.
        Higher performance (e.g., accuracy) gets higher weight.
        """
        if not performances or len(performances) != len(self.models):
            logger.warning("Performance list does not match number of models or is empty. Weights not updated.")
            return

        # Simple update: normalize performances to get new weights
        total_perf = sum(performances)
        if total_perf > 0:
            self.weights = [p / total_perf for p in performances]
            self.performance_history.append(self.weights.copy())
            logger.info(f"Ensemble weights updated: {self.weights}")
        else:
            logger.warning("Total performance is zero, weights not updated.")

    def get_status(self) -> Dict[str, Any]:
        """Get ensemble status"""
        return {
            'num_models': len(self.models),
            'current_weights': self.weights,
            'performance_updates_count': len(self.performance_history),
            'average_weights_over_history': [np.mean([h[i] for h in self.performance_history])
                                             for i in range(len(self.weights))] if self.performance_history else self.weights
        }


class PredictionEngine:
    """
    Centralized engine for making and managing real-time predictions
    using trained AI models, Cognitive Spires, and Ensemble Predictors.
    """
    def __init__(self, model_development_system: AdvancedAIModelDevelopment):
        self.model_dev = model_development_system
        self.device = self.model_dev.device
        self.active_models = {} # Stores loaded nn.Module models
        self.scalers = self.model_dev.scalers # Reference to scalers from model_dev
        self.cognitive_spires = {} # Stores CognitiveSpire instances
        self.ensemble_predictor: Optional[EnsemblePredictor] = None # Ensemble predictor instance
        logger.info(" Prediction Engine initialized.")

    async def load_and_activate_model(self, model_name: str):
        """Loads a model and activates it for prediction."""
        logger.info(f" Loading and activating model: {model_name}")
        model = await self.model_dev.load_model(model_name)
        if model:
            self.active_models[model_name] = model
            logger.info(f" Model {model_name} successfully activated.")
        else:
            logger.error(f" Failed to load or activate model: {model_name}")

    def register_cognitive_spire(self, spire_name: str, capabilities: List[str]):
        """Registers a new cognitive spire with the engine."""
        if spire_name in self.cognitive_spires:
            logger.warning(f" Cognitive Spire '{spire_name}' already registered. Overwriting.")
        self.cognitive_spires[spire_name] = CognitiveSpire(spire_name, capabilities)
        logger.info(f" Cognitive Spire '{spire_name}' registered with capabilities: {capabilities}")

    def setup_ensemble_predictor(self, model_names: List[str]):
        """Sets up the ensemble predictor with a list of models."""
        ensemble_models = [self.active_models[name] for name in model_names if name in self.active_models]
        if not ensemble_models:
            logger.error("No active models found to set up ensemble predictor.")
            return

        self.ensemble_predictor = EnsemblePredictor(ensemble_models)
        logger.info(f" Ensemble Predictor set up with {len(ensemble_models)} models.")

    async def make_prediction(self,
                              model_name: Optional[str] = None,
                              input_data: Dict[str, Any] = None,
                              use_ensemble: bool = False,
                              apply_spires: bool = True) -> Dict[str, Any]:
        """
        Makes a prediction using the specified model, ensemble, and cognitive spires.
        Input data should contain 'player_props', 'advanced_metrics', 'spatial_factors',
        'league', and 'hero_id'.
        """
        if input_data is None:
            # Load sample input data from database for prediction
            logger.info("🏀 No input data provided, loading sample data from database.")
            input_data = await self._load_sample_prediction_input()

        # Preprocess input data
        try:
            features_raw = self.model_dev._create_feature_vector(
                input_data.get('player_props', {}),
                input_data.get('advanced_metrics', {}),
                input_data.get('spatial_factors', {})
            )
            # Ensure scaler is available
            if 'main_scaler' not in self.scalers or self.scalers['main_scaler'] is None:
                logger.error("Scaler not available. Cannot normalize features. Returning fallback prediction.")
                return await self._create_fallback_prediction_output("Scaler missing", input_data)

            features_scaled = self.scalers['main_scaler'].transform(np.array(features_raw).reshape(1, -1))
            features_tensor = torch.FloatTensor(features_scaled).to(self.device)

            league_mapping = {'NBA': 0, 'WNBA': 1}
            league_id = league_mapping.get(input_data.get('league', 'UNKNOWN'), 2)
            league_id_tensor = torch.LongTensor([league_id]).to(self.device)

            hero_id = hash(str(input_data.get('hero_id', 'unknown_player'))) % 19999
            player_id_tensor = torch.LongTensor([hero_id]).to(self.device)

        except Exception as e:
            logger.error(f"Error preprocessing input data: {e}. Returning fallback prediction.")
            return await self._create_fallback_prediction_output(f"Preprocessing error: {e}", input_data)

        prediction_value = None
        confidence_value = None

        if use_ensemble and self.ensemble_predictor:
            logger.info(" Making prediction using ensemble predictor.")
            pred_tensor, conf_tensor = self.ensemble_predictor.predict(
                features_tensor, league_id_tensor, player_id_tensor
            )
            prediction_value = pred_tensor.item()
            confidence_value = conf_tensor.item()
        elif model_name and model_name in self.active_models:
            logger.info(f" Making prediction using individual model: {model_name}")
            model = self.active_models[model_name]
            pred_tensor, conf_tensor = model.predict(
                features_tensor, league_id_tensor, player_id_tensor
            )
            prediction_value = pred_tensor.item()
            confidence_value = conf_tensor.item()
        else:
            logger.error("🚨 No valid model or ensemble specified or loaded. Cannot make prediction.")
            return await self._create_fallback_prediction_output("No model/ensemble for prediction", input_data)

        raw_prediction_output = {
            'id': f"pred_{datetime.now(timezone.UTC).timestamp()}", # Updated to use datetime.now(timezone.UTC)
            'prediction_value': prediction_value,
            'confidence': confidence_value,
            'model_used': model_name if not use_ensemble else "Ensemble",
            'timestamp': datetime.now(timezone.UTC).isoformat(), # Updated to use datetime.now(timezone.UTC)
            'input_data_summary': {
                'league': input_data.get('league'),
                'hero_id': input_data.get('hero_id'),
                'player_props_keys': list(input_data.get('player_props', {}).keys())[:5],
                'advanced_metrics_keys': list(input_data.get('advanced_metrics', {}).keys())[:5]
            }
        }

        # Apply Cognitive Spires
        if apply_spires:
            final_prediction_output = raw_prediction_output
            for spire_name, spire in self.cognitive_spires.items():
                spire.activate(confidence_value) # Activate spire based on prediction confidence
                final_prediction_output = spire.process_prediction(final_prediction_output)
            return final_prediction_output
        else:
            return raw_prediction_output

    async def _load_sample_prediction_input(self) -> Dict[str, Any]:
        """Load a real sample from the database for prediction processing."""
        try:
            with sqlite3.connect(self.database_path) as conn:
                # Try to get a recent sample from the database
                sample_query = """
                SELECT p.player_props, p.advanced_metrics, p.spatial_factors,
                       p.league, p.hero_id
                FROM phase2_integrated_predictions p
                WHERE p.player_props IS NOT NULL
                AND p.advanced_metrics IS NOT NULL
                ORDER BY RANDOM()
                LIMIT 1
                """
                df = pd.read_sql_query(sample_query, conn)

                if not df.empty:
                    row = df.iloc[0]
                    return {
                        "player_props": json.loads(row['player_props']) if row['player_props'] else {},
                        "advanced_metrics": json.loads(row['advanced_metrics']) if row['advanced_metrics'] else {},
                        "spatial_factors": json.loads(row['spatial_factors']) if row['spatial_factors'] else {},
                        "league": row.get('league', 'NBA'),
                        "hero_id": row.get('hero_id', 'sample_player')
                    }

        except Exception as e:
            logger.warning(f"Could not load sample from database: {e}")

        # Fallback: Load from unified data
        try:
            with sqlite3.connect(self.database_path) as conn:
                unified_query = """
                SELECT points, rebounds, assists, steals, blocks, turnovers,
                       fg_percent, ft_percent, three_pt_made, minutes_played,
                       usage_rate, true_shooting_percent, player_efficiency_rating,
                       defensive_rating, offensive_rating, league_name, player_name
                FROM unified_nba_wnba_data
                WHERE points IS NOT NULL AND player_name IS NOT NULL
                ORDER BY RANDOM()
                LIMIT 1
                """
                df = pd.read_sql_query(unified_query, conn)

                if not df.empty:
                    row = df.iloc[0]
                    return {
                        "player_props": {
                            "points": float(row.get('points', 0)),
                            "rebounds": float(row.get('rebounds', 0)),
                            "assists": float(row.get('assists', 0)),
                            "steals": float(row.get('steals', 0)),
                            "blocks": float(row.get('blocks', 0)),
                            "fg_percent": float(row.get('fg_percent', 0.45)),
                            "ft_percent": float(row.get('ft_percent', 0.75)),
                            "3pt_made": float(row.get('three_pt_made', 0))
                        },
                        "advanced_metrics": {
                            "usage_rate": float(row.get('usage_rate', 0.2)),
                            "true_shooting_percent": float(row.get('true_shooting_percent', 0.55)),
                            "player_efficiency_rating": float(row.get('player_efficiency_rating', 15)),
                            "defensive_rating": float(row.get('defensive_rating', 110)),
                            "offensive_rating": float(row.get('offensive_rating', 110))
                        },
                        "spatial_factors": {
                            "avg_dist_shot": 15.0,  # Default reasonable value
                            "avg_dist_defended": 5.0  # Default reasonable value
                        },
                        "league": row.get('league_name', 'NBA'),
                        "hero_id": row.get('player_name', 'sample_player')
                    }

        except Exception as e:
            logger.warning(f"Could not load from unified data: {e}")

        # Final fallback: Create realistic sample based on league averages
        logger.info("🏀 Creating realistic sample based on basketball statistics")
        return self._create_realistic_sample_input()

    def _create_realistic_sample_input(self) -> Dict[str, Any]:
        """Create a realistic sample input based on basketball statistical distributions"""
        # Use realistic NBA/WNBA statistical distributions
        league = np.random.choice(['NBA', 'WNBA'], p=[0.7, 0.3])

        if league == 'WNBA':
            # WNBA statistical ranges (generally lower scoring)
            player_props = {
                "points": float(np.clip(np.random.gamma(2, 6), 0, 35)),
                "rebounds": float(np.clip(np.random.gamma(1.5, 2.5), 0, 15)),
                "assists": float(np.clip(np.random.gamma(1.2, 2), 0, 12)),
                "steals": float(np.clip(np.random.gamma(0.8, 1), 0, 6)),
                "blocks": float(np.clip(np.random.gamma(0.5, 0.8), 0, 5)),
                "fg_percent": float(np.clip(np.random.beta(4, 4), 0.25, 0.65)),
                "ft_percent": float(np.clip(np.random.beta(6, 3), 0.6, 0.95)),
                "3pt_made": float(np.clip(np.random.poisson(1.2), 0, 8))
            }
            advanced_metrics = {
                "usage_rate": float(np.clip(np.random.beta(2, 5), 0.1, 0.35)),
                "true_shooting_percent": float(np.clip(np.random.beta(5, 4), 0.45, 0.75)),
                "player_efficiency_rating": float(np.clip(np.random.gamma(2, 7), 5, 35)),
                "defensive_rating": float(np.clip(np.random.normal(105, 6), 85, 125)),
                "offensive_rating": float(np.clip(np.random.normal(105, 8), 85, 130))
            }
        else:
            # NBA statistical ranges
            player_props = {
                "points": float(np.clip(np.random.gamma(2, 8), 0, 50)),
                "rebounds": float(np.clip(np.random.gamma(1.5, 3), 0, 20)),
                "assists": float(np.clip(np.random.gamma(1.2, 2.5), 0, 15)),
                "steals": float(np.clip(np.random.gamma(0.8, 1.2), 0, 8)),
                "blocks": float(np.clip(np.random.gamma(0.6, 1), 0, 6)),
                "fg_percent": float(np.clip(np.random.beta(4, 4), 0.3, 0.7)),
                "ft_percent": float(np.clip(np.random.beta(6, 3), 0.6, 0.95)),
                "3pt_made": float(np.clip(np.random.poisson(1.8), 0, 12))
            }
            advanced_metrics = {
                "usage_rate": float(np.clip(np.random.beta(2, 5), 0.1, 0.4)),
                "true_shooting_percent": float(np.clip(np.random.beta(5, 4), 0.45, 0.8)),
                "player_efficiency_rating": float(np.clip(np.random.gamma(2, 8), 5, 40)),
                "defensive_rating": float(np.clip(np.random.normal(110, 8), 90, 130)),
                "offensive_rating": float(np.clip(np.random.normal(110, 10), 85, 135))
            }

        spatial_factors = {
            "avg_dist_shot": float(np.clip(np.random.normal(15, 4), 5, 25)),
            "avg_dist_defended": float(np.clip(np.random.normal(5, 2), 1, 12))
        }

        return {
            "player_props": player_props,
            "advanced_metrics": advanced_metrics,
            "spatial_factors": spatial_factors,
            "league": league,
            "hero_id": f"realistic_player_{np.random.randint(1, 10000)}"
        }

    async def _create_fallback_prediction_output(self, error_message: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Creates intelligent basketball analytics-based prediction output for error cases."""
        try:
            # Extract league from input data or use default
            league = input_data.get('league', 'NBA') if input_data else 'NBA'

            # Try to extract player performance indicators from input data
            player_props = input_data.get('player_props', {}) if input_data else {}
            advanced_metrics = input_data.get('advanced_metrics', {}) if input_data else {}

            # Calculate prediction based on available input data
            if player_props and advanced_metrics:
                # Use actual player data to make intelligent prediction
                points = player_props.get('points', 15)
                efficiency = advanced_metrics.get('player_efficiency_rating', 15)
                usage = advanced_metrics.get('usage_rate', 0.2)

                # Performance-based prediction
                performance_factor = (points * 0.4 + efficiency * 0.3 + usage * 100 * 0.3)

                if league == 'WNBA':
                    base_total = 165.0
                    prediction_value = base_total + (performance_factor - 20) * 2
                    prediction_value = max(150.0, min(200.0, prediction_value))
                else:
                    base_total = 220.0
                    prediction_value = base_total + (performance_factor - 20) * 3
                    prediction_value = max(180.0, min(260.0, prediction_value))

                # Higher confidence when we have actual data
                confidence = 0.65
                methodology = f'{league}_player_performance_analysis'
            else:
                # Fallback to league averages
                if league == 'WNBA':
                    base_total = 165.0
                    prediction_value = base_total + np.random.normal(0, 8)
                    prediction_value = max(150.0, min(200.0, prediction_value))
                else:
                    base_total = 220.0
                    prediction_value = base_total + np.random.normal(0, 12)
                    prediction_value = max(180.0, min(260.0, prediction_value))

                # Lower confidence for pure averages
                confidence = 0.45
                methodology = f'{league}_historical_averages'

            # Adjust confidence based on error type
            if "preprocessing" in error_message.lower():
                confidence *= 0.9  # Slight reduction for data issues
            elif "model" in error_message.lower():
                confidence *= 0.8  # More reduction for model issues
            elif "scaler" in error_message.lower():
                confidence *= 0.85  # Medium reduction for scaling issues

            # Add basketball context to prediction
            basketball_context = {
                'league': league,
                'prediction_type': 'performance_based_total',
                'fallback_reason': 'intelligent_basketball_analytics',
                'methodology': methodology,
                'input_data_available': bool(player_props and advanced_metrics),
                'player_id': input_data.get('hero_id', 'unknown') if input_data else 'unknown'
            }

            return {
                'id': f"basketball_fallback_{datetime.now(timezone.UTC).timestamp()}",
                'prediction_value': float(prediction_value),
                'confidence': float(confidence),
                'model_used': "Basketball_Analytics_Fallback",
                'timestamp': datetime.now(timezone.UTC).isoformat(),
                'status': 'fallback_prediction',
                'error_details': error_message,
                'basketball_context': basketball_context,
                'prediction_bounds': {
                    'min_realistic': 150.0 if league == 'WNBA' else 180.0,
                    'max_realistic': 200.0 if league == 'WNBA' else 260.0,
                    'league_average': 165.0 if league == 'WNBA' else 220.0
                }
            }

        except Exception as fallback_error:
            # Ultimate fallback with minimal basketball intelligence
            logger.warning(f"Basketball analytics fallback failed: {fallback_error}")
            return {
                'id': f"minimal_fallback_{datetime.now(timezone.UTC).timestamp()}",
                'prediction_value': 200.0,  # Safe middle ground
                'confidence': 0.30,
                'model_used': "Minimal_Fallback",
                'timestamp': datetime.now(timezone.UTC).isoformat(),
                'status': 'minimal_fallback',
                'error_details': f"Original: {error_message}, Fallback: {fallback_error}"
            }


async def main():
    """Main advanced AI model development and prediction system"""
    logger.info(" MEDUSA VAULT: [BRAIN][LIGHTNING] Starting HYPER MEDUSA Advanced AI Model Development & Prediction [LIGHTNING][BRAIN]")

    # Create model development system
    model_dev = AdvancedAIModelDevelopment()

    # --- Model Creation and Training ---
    logger.info(" MEDUSA VAULT: --- MODEL DEVELOPMENT PHASE ---")
    transformer_model_name = "hyper_medusa_transformer"
    ensemble_model_name = "hyper_medusa_ensemble"

    # Create advanced transformer model
    transformer_model = await model_dev.create_advanced_model(
        transformer_model_name,
        "transformer"
    )

    # Create ensemble model
    ensemble_model = await model_dev.create_advanced_model(
        ensemble_model_name,
        "ensemble"
    )

    # Prepare training data and get the scaler
    train_loader, val_loader, scaler = await model_dev.prepare_training_data()
    # Ensure the scaler is associated with the model_dev system
    model_dev.scalers['main_scaler'] = scaler

    # Train the transformer model
    logger.info(" MEDUSA VAULT: [ROCKET] Training transformer model...")
    transformer_training_history = await model_dev.train_model(
        transformer_model_name,
        train_loader,
        val_loader
    )
    logger.info(f"Transformer model training history summary: {model_dev.training_history[transformer_model_name]}")

    # Train the ensemble model
    logger.info(" MEDUSA VAULT: [ROCKET] Training ensemble model...")
    ensemble_training_history = await model_dev.train_model(
        ensemble_model_name,
        train_loader,
        val_loader
    )
    logger.info(f"Ensemble model training history summary: {model_dev.training_history[ensemble_model_name]}")

    # --- Prediction Engine Initialization and Usage ---
    logger.info("\n MEDUSA VAULT: --- PREDICTION ENGINE PHASE ---")

    prediction_engine = PredictionEngine(model_dev)

    # Load and activate models in the prediction engine
    await prediction_engine.load_and_activate_model(transformer_model_name)
    await prediction_engine.load_and_activate_model(ensemble_model_name)

    # Register Cognitive Spires
    prediction_engine.register_cognitive_spire(
        "reasoning_spire",
        ["contextual_analysis", "bias_correction"]
    )
    prediction_engine.register_cognitive_spire(
        "intuition_spire",
        ["pattern_recognition", "anomaly_detection"]
    )

    # Set up the ensemble predictor
    prediction_engine.setup_ensemble_predictor([transformer_model_name, ensemble_model_name])

    # Make a prediction using the ensemble and spires
    logger.info("\n Making a prediction with Ensemble and Cognitive Spires:")
    sample_input = {
        "player_props": {"points": 28, "rebounds": 10, "assists": 7, "fg_percent": 0.55},
        "advanced_metrics": {"usage_rate": 0.28, "player_efficiency_rating": 26},
        "spatial_factors": {"avg_dist_shot": 12.5, "avg_dist_defended": 4.1},
        "league": "NBA",
        "hero_id": "player_lebron_james"
    }
    ensemble_prediction_output = await prediction_engine.make_prediction(
        input_data=sample_input,
        use_ensemble=True,
        apply_spires=True
    )
    logger.info(f"Ensemble Prediction Output (with Spires): {ensemble_prediction_output}")

    # Make a prediction using only the transformer model without spires
    logger.info("\n Making a prediction with Transformer model (no Spires):")
    transformer_prediction_output = await prediction_engine.make_prediction(
        model_name=transformer_model_name,
        input_data=sample_input,
        use_ensemble=False,
        apply_spires=False
    )
    logger.info(f"Transformer Prediction Output (no Spires): {transformer_prediction_output}")

    # Get overall status
    model_status = model_dev.get_model_status()
    logger.info(f"\n[CHART] Overall Model Development Status: {model_status}")

    logger.info(" MEDUSA VAULT: [CHECK] Advanced AI Model Development and Prediction Integration completed")
    return model_dev, prediction_engine


if __name__ == "__main__":
    asyncio.run(main())
