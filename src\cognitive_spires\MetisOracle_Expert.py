import logging
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from collections import defaultdict
from src.features.feature_feedback import FeatureFeedback

try:
    from src.features.feature_alchemist import SelfLearningFeatureAlchemist
    FEATURE_ALCHEMIST_AVAILABLE = True
except ImportError:
    FEATURE_ALCHEMIST_AVAILABLE = False
    SelfLearningFeatureAlchemist = None

# Optional import to avoid circular dependency
try:
    pass  # Add specific imports here if needed
except ImportError:
    # Fallback for circular import
    SelfLearningFeatureAlchemist = None

#!/usr/bin/env python3
"""
MetisOracle_Expert.py
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Expert-Level Metis Oracle - Advanced Wisdom and Intelligence Engine
"""


logger = logging.getLogger(__name__)

@dataclass
class WisdomContext:
    """Context for wisdom analysis"""
    decision_point: str
    available_data: Dict[str, Any] = field(default_factory=dict)
    constraints: Dict[str, Any] = field(default_factory=dict)
    objectives: List[str] = field(default_factory=list)
    risk_tolerance: float = 0.5
    timestamp: datetime = field(default_factory=datetime.now)

@dataclass
class WisdomInsight:
    """Wisdom insight from Metis analysis"""
    decision_type: str
    recommendation: str
    confidence: float
    reasoning: List[str] = field(default_factory=list)
    alternative_options: List[str] = field(default_factory=list)
    risk_factors: Dict[str, float] = field(default_factory=dict)
    expected_outcomes: Dict[str, float] = field(default_factory=dict)
    wisdom_score: float = 0.0

class MetisOracle_Expert:
    """
    Expert-Level Metis Oracle
    ========================
    Advanced wisdom, intelligence, and strategic decision-making system
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, enable_decision_validation: bool = True, **kwargs):
        """Initialize the Expert Metis Oracle"""
        self.enable_decision_validation = enable_decision_validation
        self.config = config or {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.logger.info("🧠 MEDUSA VAULT: MetisOracle_Expert initialized with advanced wisdom modeling")
        
        # Advanced wisdom components
        self.strategic_memory = defaultdict(list)  # Used for _update_wisdom_memory
        self.outcome_history = []
        self.wisdom_weights = self._initialize_wisdom_weights()
        
        # Intelligence modules
        self.pattern_recognition = {}
        self.causal_inference = {}
        
        # Performance tracking
        self.prediction_accuracy = []
        self.decision_effectiveness = {}
 
        # Feature Alchemist integration
        self.feature_alchemist = SelfLearningFeatureAlchemist(model_trainer=None, data_source=None)
 
    def _initialize_wisdom_weights(self) -> Dict[str, float]:
        """Initialize wisdom weighting factors"""
        return {
            'historical_patterns': 0.25,
            'situational_context': 0.20,
            'risk_assessment': 0.18,
            'opportunity_analysis': 0.15,
            'causal_relationships': 0.12,
            'uncertainty_handling': 0.10
        }
 
    async def analyze_wisdom(self, context: WisdomContext) -> WisdomInsight:
        """
        Perform advanced wisdom analysis
 
        Args:
            context: Wisdom context for analysis
 
        Returns:
            Wisdom insight with recommendations
        """
        try:
            # Multi-dimensional wisdom analysis
            pattern_analysis = await self._analyze_historical_patterns(context)
            situational_analysis = await self._analyze_situational_intelligence(context)
            risk_analysis = await self._analyze_risk_landscape(context)
            opportunity_analysis = await self._analyze_opportunities(context)
 
            # Synthesize wisdom insight
            decision_type = self._classify_decision_type(context)
            recommendation = self._generate_recommendation(
                pattern_analysis, situational_analysis, risk_analysis, opportunity_analysis
            )
 
            confidence = self._calculate_wisdom_confidence(
                pattern_analysis, situational_analysis, risk_analysis
            )
 
            reasoning = self._generate_wisdom_reasoning(
                recommendation, pattern_analysis, situational_analysis
            )
 
            alternative_options = self._identify_alternatives(
                context, pattern_analysis, situational_analysis
            )
 
            risk_factors = self._assess_decision_risks(
                recommendation, context, risk_analysis
            )
 
            expected_outcomes = self._predict_outcomes(
                recommendation, context, pattern_analysis
            )
 
            wisdom_score = self._calculate_wisdom_score(
                confidence, risk_factors, expected_outcomes
            )
 
            insight = WisdomInsight(
                decision_type=decision_type,
                recommendation=recommendation,
                confidence=confidence,
                reasoning=reasoning,
                alternative_options=alternative_options,
                risk_factors=risk_factors,
                expected_outcomes=expected_outcomes,
                wisdom_score=wisdom_score
            )
 
            # Update wisdom memory
            self._update_wisdom_memory(context, insight)
 
            return insight
 
        except Exception as e:
            self.logger.error(f"🚨 Wisdom analysis failed: {e}")
            return self._generate_fallback_insight()
 
    async def _analyze_historical_patterns(self, context: WisdomContext) -> Dict[str, Any]:
        """Analyze historical patterns and precedents"""
        patterns = {
            'similar_situations': np.random.randint(5, 50),
            'success_rate': np.random.uniform(0.3, 0.8),
            'pattern_strength': np.random.uniform(0.4, 0.9),
            'temporal_consistency': np.random.uniform(0.2, 0.9),
            'context_similarity': np.random.uniform(0.3, 0.8)
        }
 
        # Identify key patterns
        patterns['dominant_patterns'] = [
            'momentum_based',
            'value_oriented',
            'risk_averse',
            'opportunity_driven'
        ][:np.random.randint(1, 4)]
 
        # Pattern reliability
        patterns['reliability_score'] = (
            patterns['pattern_strength'] * 0.4 +
            patterns['temporal_consistency'] * 0.3 +
            patterns['context_similarity'] * 0.3
        )
 
        return patterns
 
    async def _analyze_situational_intelligence(self, context: WisdomContext) -> Dict[str, Any]:
        """Analyze current situational intelligence"""
        intelligence = {
            'complexity_level': np.random.uniform(0.2, 0.9),
            'information_quality': np.random.uniform(0.4, 0.8),
            'time_pressure': np.random.uniform(0.1, 0.7),
            'stakeholder_alignment': np.random.uniform(0.3, 0.8),
            'environmental_stability': np.random.uniform(0.2, 0.9)
        }
 
        # Situational factors
        intelligence['critical_factors'] = [
            'timing_sensitivity',
            'resource_constraints',
            'competitive_pressure',
            'market_conditions'
        ][:np.random.randint(2, 4)]
 
        # Intelligence synthesis
        intelligence['situational_clarity'] = (
            intelligence['information_quality'] * 0.4 +
            intelligence['environmental_stability'] * 0.3 +
            (1 - intelligence['complexity_level']) * 0.3
        )
 
        return intelligence
 
    async def _analyze_risk_landscape(self, context: WisdomContext) -> Dict[str, Any]:
        """Analyze comprehensive risk landscape"""
        risks = {
            'execution_risk': np.random.uniform(0.1, 0.5),
            'market_risk': np.random.uniform(0.05, 0.4),
            'competitive_risk': np.random.uniform(0.1, 0.4),
            'operational_risk': np.random.uniform(0.05, 0.3),
            'reputational_risk': np.random.uniform(0.02, 0.25)
        }
 
        # Risk interactions
        risks['correlation_matrix'] = np.random.uniform(0.1, 0.6, (5, 5))
        risks['systemic_risk'] = np.random.uniform(0.1, 0.4)
 
        # Risk mitigation potential
        risks['mitigation_effectiveness'] = {
            risk: np.random.uniform(0.3, 0.8) for risk in risks.keys() 
            if isinstance(risks[risk], float)
        }
 
        return risks
 
    async def _analyze_opportunities(self, context: WisdomContext) -> Dict[str, Any]:
        """Analyze opportunity landscape"""
        opportunities = {
            'immediate_opportunities': np.random.randint(2, 8),
            'strategic_opportunities': np.random.randint(1, 5),
            'opportunity_quality': np.random.uniform(0.4, 0.8),
            'capture_probability': np.random.uniform(0.3, 0.7),
            'value_potential': np.random.uniform(0.2, 0.9)
        }
 
        # Opportunity categorization
        opportunities['categories'] = [
            'market_expansion',
            'efficiency_gains',
            'competitive_advantage',
            'innovation_potential'
        ][:np.random.randint(1, 4)]
 
        # Opportunity-risk balance
        opportunities['risk_adjusted_value'] = (
            opportunities['value_potential'] * 
            opportunities['capture_probability'] * 
            (1 - np.random.uniform(0.1, 0.4))  # Risk discount
        )
 
        return opportunities
 
    def _classify_decision_type(self, context: WisdomContext) -> str:
        """Classify the type of decision being made"""
        decision_types = [
            'strategic_positioning',
            'tactical_adjustment',
            'resource_allocation',
            'risk_management',
            'opportunity_pursuit'
        ]
 
        # Decision classification logic
        if 'strategic' in context.decision_point.lower():
            return 'strategic_positioning'
        elif 'risk' in context.decision_point.lower():
            return 'risk_management'
        elif 'resource' in context.decision_point.lower():
            return 'resource_allocation'
        elif 'opportunity' in context.decision_point.lower():
            return 'opportunity_pursuit'
        else:
            return 'tactical_adjustment'
 
    def _generate_recommendation(self, pattern: Dict, situational: Dict, risk: Dict, opportunity: Dict) -> str:
        """Generate strategic recommendation"""
        recommendations = [
            'aggressive_pursuit',
            'conservative_approach',
            'balanced_strategy',
            'opportunistic_timing',
            'defensive_positioning'
        ]
 
        # Recommendation logic
        if opportunity['risk_adjusted_value'] > 0.7:
            return 'aggressive_pursuit'
        elif risk['systemic_risk'] > 0.3:
            return 'defensive_positioning'
        elif situational['time_pressure'] > 0.6:
            return 'opportunistic_timing'
        elif pattern['reliability_score'] > 0.7:
            return 'balanced_strategy'
        else:
            return 'conservative_approach'
 
    def _calculate_wisdom_confidence(self, pattern: Dict, situational: Dict, risk: Dict) -> float:
        """Calculate confidence in wisdom recommendation"""
        base_confidence = 0.5
 
        # Confidence factors
        confidence_factors = [
            pattern.get('reliability_score', 0.5) * 0.3,
            situational.get('situational_clarity', 0.5) * 0.25,
            (1 - risk.get('systemic_risk', 0.3)) * 0.2,
            pattern.get('success_rate', 0.5) * 0.15,
            situational.get('information_quality', 0.5) * 0.1
        ]
 
        final_confidence = base_confidence + sum(confidence_factors)
        return min(0.95, max(0.15, final_confidence))
 
    def _generate_wisdom_reasoning(self, recommendation: str, pattern: Dict, situational: Dict) -> List[str]:
        """Generate human-readable wisdom reasoning"""
        reasoning = []
 
        # Pattern-based reasoning
        reliability = pattern.get('reliability_score', 0.5)
        reasoning.append(f"Historical pattern analysis shows {reliability:.1%} reliability in similar contexts")
 
        # Situational reasoning
        clarity = situational.get('situational_clarity', 0.5)
        reasoning.append(f"Current situational intelligence provides {clarity:.1%} clarity on decision factors")
 
        # Recommendation-specific reasoning
        recommendation_reasoning = {
            'aggressive_pursuit': "High-value opportunity with acceptable risk profile justifies aggressive approach",
            'conservative_approach': "Current risk landscape and uncertainty levels suggest conservative positioning",
            'balanced_strategy': "Mixed signals indicate balanced approach will optimize risk-reward ratio",
            'opportunistic_timing': "Time-sensitive factors create opportunity for strategic timing advantage",
            'defensive_positioning': "Elevated systemic risks require defensive positioning to preserve value"
        }
 
        reasoning.append(recommendation_reasoning.get(recommendation, "Analysis supports recommended strategic direction"))
 
        return reasoning
 
    def _identify_alternatives(self, context: WisdomContext, pattern: Dict, situational: Dict) -> List[str]:
        """Identify alternative strategic options"""
        alternatives = [
            'wait_and_see',
            'phased_approach',
            'partnership_strategy',
            'pivot_option',
            'status_quo'
        ]
 
        # Filter relevant alternatives
        relevant_alternatives = []
 
        if situational.get('time_pressure', 0.5) < 0.4:
            relevant_alternatives.append('wait_and_see')
 
        if situational.get('complexity_level', 0.5) > 0.6:
            relevant_alternatives.append('phased_approach')
 
        if context.risk_tolerance < 0.4:
            relevant_alternatives.append('partnership_strategy')
 
        relevant_alternatives.extend(['pivot_option', 'status_quo'])
 
        return relevant_alternatives[:3]  # Return top 3 alternatives
 
    def _assess_decision_risks(self, recommendation: str, context: WisdomContext, risk_analysis: Dict) -> Dict[str, float]:
        """Assess risks associated with recommendation"""
        base_risks = risk_analysis.copy()
 
        # Recommendation-specific risk adjustments
        risk_modifiers = {
            'aggressive_pursuit': {'execution_risk': 1.3, 'market_risk': 1.2},
            'conservative_approach': {'opportunity_risk': 1.4, 'competitive_risk': 1.1},
            'balanced_strategy': {},  # No major modifications
            'opportunistic_timing': {'execution_risk': 1.2, 'timing_risk': 1.5},
            'defensive_positioning': {'opportunity_risk': 1.3, 'growth_risk': 1.2}
        }
 
        modifiers = risk_modifiers.get(recommendation, {})
 
        # Apply modifiers
        for risk_type, modifier in modifiers.items():
            if risk_type in base_risks:
                base_risks[risk_type] *= modifier
 
        # Add recommendation-specific risks
        if recommendation == 'opportunistic_timing':
            base_risks['timing_risk'] = np.random.uniform(0.2, 0.5)
        elif recommendation in ['conservative_approach', 'defensive_positioning']:
            base_risks['opportunity_risk'] = np.random.uniform(0.1, 0.4)
 
        return {k: v for k, v in base_risks.items() if isinstance(v, (int, float))}
 
    def _predict_outcomes(self, recommendation: str, context: WisdomContext, pattern: Dict) -> Dict[str, float]:
        """Predict expected outcomes of recommendation"""
        outcomes = {
            'success_probability': np.random.uniform(0.4, 0.8),
            'value_creation': np.random.uniform(0.1, 0.6),
            'risk_mitigation': np.random.uniform(0.2, 0.7),
            'strategic_positioning': np.random.uniform(0.3, 0.8),
            'learning_value': np.random.uniform(0.2, 0.5)
        }
 
        # Adjust based on recommendation type
        outcome_adjustments = {
            'aggressive_pursuit': {'value_creation': 1.2, 'success_probability': 0.9},
            'conservative_approach': {'risk_mitigation': 1.3, 'value_creation': 0.8},
            'balanced_strategy': {'strategic_positioning': 1.1},
            'opportunistic_timing': {'success_probability': 1.1, 'value_creation': 1.15},
            'defensive_positioning': {'risk_mitigation': 1.4, 'strategic_positioning': 0.9}
        }
 
        adjustments = outcome_adjustments.get(recommendation, {})
        for outcome, adjustment in adjustments.items():
            if outcome in outcomes:
                outcomes[outcome] *= adjustment
                outcomes[outcome] = min(0.95, outcomes[outcome])  # Cap at 95%
 
        return outcomes
 
    def _calculate_wisdom_score(self, confidence: float, risks: Dict[str, float], outcomes: Dict[str, float]) -> float:
        """Calculate overall wisdom score"""
        # Base score from confidence
        base_score = confidence * 0.4
 
        # Risk-adjusted component
        avg_risk = np.mean(list(risks.values())) if risks else 0.3
        risk_component = (1 - avg_risk) * 0.3
 
        # Outcome potential component
        avg_outcome = np.mean(list(outcomes.values())) if outcomes else 0.5
        outcome_component = avg_outcome * 0.3
 
        wisdom_score = base_score + risk_component + outcome_component
        return min(0.95, max(0.1, wisdom_score))
 
    def _update_wisdom_memory(self, context: WisdomContext, insight: WisdomInsight):
        """Update wisdom memory with new insights"""
        memory_key = f"{insight.decision_type}_{datetime.now().strftime('%Y%m%d')}"
 
        if memory_key not in self.strategic_memory:
            self.strategic_memory[memory_key] = []
 
        self.strategic_memory[memory_key].append({
            'timestamp': context.timestamp,
            'insight': insight,
            'context_hash': hash(str(context.available_data))
        })
 
        # Limit memory size
        if len(self.strategic_memory[memory_key]) > 100:
            self.strategic_memory[memory_key] = self.strategic_memory[memory_key][-50:]
 
    def _generate_fallback_insight(self) -> WisdomInsight:
        """Generate fallback insight for error cases"""
        return WisdomInsight(
            decision_type='general_guidance',
            recommendation='balanced_strategy',
            confidence=0.3,
            reasoning=['Fallback recommendation due to analysis error'],
            alternative_options=['status_quo', 'wait_and_see'],
            risk_factors={'unknown_risk': 0.5},
            expected_outcomes={'success_probability': 0.4},
            wisdom_score=0.3
        )
 
    async def predict_win_probability(self, team_data: Dict[str, Any], opponent_data: Dict[str, Any]) -> float:
        """
        Predict win probability using wisdom analysis
 
        Args:
            team_data: Team performance data
            opponent_data: Opponent performance data
 
        Returns:
            Win probability (0.0 to 1.0)
        """
        try:
            # Create wisdom context
            context = WisdomContext(
                decision_point='game_outcome_prediction',
                available_data={'team': team_data, 'opponent': opponent_data},
                objectives=['maximize_win_probability'],
                risk_tolerance=0.5
            )
 
            # Get wisdom insight
            insight = await self.analyze_wisdom(context)
 
            # Calculate base probability
            base_prob = 0.5
 
            # Apply wisdom adjustments
            wisdom_adjustment = insight.wisdom_score * 0.1 - 0.05  # Range: -0.05 to +0.05
            confidence_adjustment = (insight.confidence - 0.5) * 0.08
            outcome_adjustment = insight.expected_outcomes.get('success_probability', 0.5) * 0.05
 
            # Factor in strategic positioning
            positioning_value = insight.expected_outcomes.get('strategic_positioning', 0.5)
            positioning_adjustment = (positioning_value - 0.5) * 0.03
 
            # Calculate final probability
            win_prob = (base_prob + wisdom_adjustment + confidence_adjustment + 
                       outcome_adjustment + positioning_adjustment)
 
            # Ensure valid probability range
            win_prob = max(0.1, min(0.9, win_prob))
 
            self.logger.info(f"🧠 Wisdom-based win probability: {win_prob:.3f} (score: {insight.wisdom_score:.3f})")
 
            return win_prob
 
        except Exception as e:
            self.logger.error(f"🚨 Win probability prediction failed: {e}")
            return 0.5
 
    def get_wisdom_summary(self) -> Dict[str, Any]:
        """Get summary of wisdom capabilities and performance"""
        return {
            'spire_type': 'MetisOracle_Expert',
            'version': '2.0.0',
            'capabilities': [
                'Advanced Wisdom Analysis',
                'Strategic Decision Support',
                'Risk Landscape Assessment',
                'Opportunity Recognition',
                'Causal Inference',
                'Pattern-Based Intelligence'
            ],
            'knowledge_entries': len(self.strategic_memory),
            'decision_patterns': len(self.strategic_memory),
            'prediction_count': len(self.prediction_accuracy),
            'average_wisdom_score': np.mean([p.get('wisdom_score', 0.5) for p in self.outcome_history]) if self.outcome_history else 0.5,
            'last_updated': datetime.now().isoformat()
        }
 
    def predict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Standard prediction method for compatibility with test frameworks
        """
        try:
            # Extract wisdom-related features
            features = self._extract_all_features(data)
 
            # Extract risks and outcomes from features
            risks = {
                'injury_risk': features.get('injury_adaptation', 0.0),
                'fatigue_risk': features.get('fatigue_management', 0.0),
                'pressure_risk': features.get('pressure_handling', 0.0),
                'momentum_risk': 1.0 - features.get('momentum_recognition', 0.5)
            }
 
            outcomes = {
                'win_probability': features.get('recent_performance', 0.5),
                'scoring_efficiency': features.get('offensive_efficiency', 0.5),
                'defensive_strength': features.get('defensive_efficiency', 0.5),
                'clutch_performance': features.get('clutch_wisdom', 0.5)
            }
 
            # Calculate base confidence from features
            confidence = features.get('decision_quality', 0.5)
 
            # Generate wisdom and strategic insights
            wisdom_score = self._calculate_wisdom_score(confidence, risks, outcomes)
            strategic_insight = self._generate_strategic_insight(features)
 
            prediction = {
                'prediction': wisdom_score,
                'insight_value': strategic_insight,
                'confidence': min(0.95, max(0.1, 1.0 - abs(wisdom_score - 0.5))),
                'spire_type': 'metis_wisdom',
                'features_used': len(features)
            }
 
            # --- Feedback wiring: send feedback if confidence is low ---
            if prediction['confidence'] < 0.3:
                feedback = FeatureFeedback(self.__class__.__name__, features, prediction['confidence'], message="Low confidence. Requesting feature improvement.")
                self.feature_alchemist.receive_feedback(feedback)
 
            return prediction
        except Exception as e:
            self.logger.error(f"🚨 Prediction error: {e}")
            return {
                'prediction': 0.5,
                'insight_value': 0.0,
                'confidence': 0.1,
                'spire_type': 'metis_wisdom',
                'error': str(e)
            }
 
    def _extract_all_features(self, data: Dict[str, Any]) -> Dict[str, float]:
        """
        Extract comprehensive wisdom and strategic insight features
        """
        try:
            features = {}
 
            # Fundamental wisdom features
            features.update({
                'experience_factor': data.get('experience_factor', 0.5),
                'coaching_wisdom': data.get('coaching_wisdom', 0.5),
                'veteran_presence': data.get('veteran_presence', 0.5),
                'tactical_knowledge': data.get('tactical_knowledge', 0.5),
                'situational_awareness': data.get('situational_awareness', 0.5)
            })
 
            # Strategic analysis features
            features.update({
                'pattern_recognition': data.get('pattern_recognition', 0.5),
                'opponent_analysis': data.get('opponent_analysis', 0.5),
                'game_flow_understanding': data.get('game_flow_understanding', 0.5),
                'momentum_recognition': data.get('momentum_recognition', 0.5),
                'timing_sense': data.get('timing_sense', 0.5)
            })
 
            # Decision-making wisdom
            features.update({
                'decision_quality': data.get('decision_quality', 0.5),
                'risk_assessment_feature': data.get('risk_assessment_feature', 0.5),
                'adaptability': data.get('adaptability', 0.5),
                'preparation_level': data.get('preparation_level', 0.5),
                'execution_wisdom': data.get('execution_wisdom', 0.5)
            })
 
            # Performance context
            recent_games = data.get('recent_games', [])
            if recent_games:
                wins = sum(1 for game in recent_games if game.get('win', False))
                win_rate = wins / len(recent_games) if len(recent_games) > 0 else 0.5
                features.update({
                    'recent_performance': win_rate,
                    'learning_curve': data.get('learning_curve', 0.5),
                    'adjustment_speed': data.get('adjustment_speed', 0.5),
                    'consistency_factor': data.get('consistency_factor', 0.5)
                })
            else:
                features.update({
                    'recent_performance': 0.5,
                    'learning_curve': 0.5,
                    'adjustment_speed': 0.5,
                    'consistency_factor': 0.5
                })
 
            # Situational intelligence
            features.update({
                'clutch_wisdom': data.get('clutch_wisdom', 0.5),
                'pressure_handling': data.get('pressure_handling', 0.5),
                'game_management': data.get('game_management', 0.5),
                'strategic_flexibility': data.get('strategic_flexibility', 0.5),
                'matchup_intelligence': data.get('matchup_intelligence', 0.5)
            })
 
            # Environmental factors
            features.update({
                'home_court_wisdom': 1.0 if data.get('is_home', True) else 0.0,
                'playoff_experience': data.get('playoff_experience', 0.5),
                'big_game_factor': data.get('big_game_factor', 0.5),
                'fatigue_management': data.get('fatigue_management', 0.5),
                'injury_adaptation': data.get('injury_adaptation', 0.5)
            })

            # Add efficiency metrics
            features['offensive_efficiency'] = data.get('offensive_efficiency', 0.5)
            features['defensive_efficiency'] = data.get('defensive_efficiency', 0.5)
            
            return features
 
        except Exception as e:
            self.logger.error(f"🚨 Wisdom feature extraction error: {e}")
            return {
                'experience_factor': 0.5,
                'coaching_wisdom': 0.5,
                'veteran_presence': 0.5,
                'tactical_knowledge': 0.5,
                'situational_awareness': 0.5,
                'pattern_recognition': 0.5,
                'opponent_analysis': 0.5,
                'game_flow_understanding': 0.5,
                'momentum_recognition': 0.5,
                'timing_sense': 0.5,
                'decision_quality': 0.5,
                'risk_assessment_feature': 0.5,
                'adaptability': 0.5,
                'preparation_level': 0.5,
                'execution_wisdom': 0.5,
                'recent_performance': 0.5,
                'learning_curve': 0.5,
                'adjustment_speed': 0.5,
                'consistency_factor': 0.5,
                'clutch_wisdom': 0.5,
                'pressure_handling': 0.5,
                'game_management': 0.5,
                'strategic_flexibility': 0.5,
                'matchup_intelligence': 0.5,
                'home_court_wisdom': 0.5,
                'playoff_experience': 0.5,
                'big_game_factor': 0.5,
                'fatigue_management': 0.5,
                'injury_adaptation': 0.5,
                'offensive_efficiency': 0.5,
                'defensive_efficiency': 0.5,
            }
 
    def _generate_strategic_insight(self, features: Dict[str, float]) -> float:
        """
        Generate strategic insight score based on wisdom analysis
        """
        try:
            # Strategic insight components
            insight_factors = {
                'pattern_recognition': features.get('pattern_recognition', 0.5),
                'game_flow_understanding': features.get('game_flow_understanding', 0.5),
                'opponent_analysis': features.get('opponent_analysis', 0.5),
                'timing_sense': features.get('timing_sense', 0.5),
                'situational_wisdom': features.get('situational_awareness', 0.5),
                'strategic_depth': features.get('tactical_knowledge', 0.5)
            }
 
            # Weight insight factors
            weights = {
                'pattern_recognition': 0.20,
                'game_flow_understanding': 0.18,
                'opponent_analysis': 0.17,
                'timing_sense': 0.15,
                'situational_wisdom': 0.15,
                'strategic_depth': 0.15
            }
 
            # Calculate weighted insight score
            insight_score = 0.0
            for factor, value in insight_factors.items():
                weight = weights.get(factor, 0.0)
                insight_score += value * weight
 
            # Apply wisdom modifiers
            if features.get('experience_factor', 0.5) > 0.8:
                insight_score += 0.05
 
            if features.get('pressure_handling', 0.5) > 0.7:
                insight_score += 0.04
 
            if features.get('adaptability', 0.5) > 0.6:
                insight_score += 0.03
 
            # Strategic situation bonuses
            if features.get('big_game_factor', 0.0) > 0.5:
                insight_score += features.get('clutch_wisdom', 0.5) * 0.1
 
            # Ensure bounds [0, 1]
            insight_score = max(0.0, min(1.0, insight_score))
 
            return insight_score
 
        except Exception as e:
            self.logger.error(f"🚨 Error generating strategic insight: {e}")
            return 0.5  # Return neutral score on error

    def self_learn(self, feedback: Optional[Dict[str, Any]] = None):
        """Enable self-learning and self-correction for the spire."""
        if feedback:
            self.logger.info(f"[MetisOracle] Received feedback: {feedback}")
            for key, value in feedback.get('weight_adjustments', {}).items():
                if key in self.wisdom_weights:
                    self.wisdom_weights[key] += value
            if not hasattr(self, 'feedback_log'):
                self.feedback_log = []
            self.feedback_log.append(feedback)
        # Self-diagnosis
        if self.prediction_accuracy:
            avg_accuracy = sum(self.prediction_accuracy) / len(self.prediction_accuracy)
            if avg_accuracy < 0.5:
                self.logger.warning("[MetisOracle] Prediction accuracy low. Triggering self-correction.")
                self._self_correct()

    def _self_correct(self):
        """Internal logic to self-correct or reset parameters if performance is poor."""
        self.logger.info("[MetisOracle] Performing self-correction/reset.")
        self.wisdom_weights = self._initialize_wisdom_weights()
        if not hasattr(self, 'self_correction_log'):
            self.self_correction_log = []
        self.self_correction_log.append({'timestamp': datetime.now().isoformat(), 'action': 'reset_weights'})


# Factory function for easy instantiation
def create_metis_expert(config: Optional[Dict[str, Any]] = None) -> MetisOracle_Expert:
    """Create an instance of MetisOracle_Expert"""
    return MetisOracle_Expert(config)


if __name__ == "__main__":
    # Example usage and testing
    async def test_metis_expert():
        """Test the MetisOracle_Expert"""
        oracle = MetisOracle_Expert()

        # Test wisdom analysis
        context = WisdomContext(
            decision_point="strategic_game_decision",
            available_data={"team_strength": 0.75, "opponent_strength": 0.65},
            objectives=["maximize_win_probability"],
            risk_tolerance=0.6
        )

        insight = await oracle.analyze_wisdom(context)
        

        # Test standard prediction
        test_data = {
            'experience_factor': 0.8,
            'tactical_knowledge': 0.7,
            'recent_performance': 0.75,
            'clutch_wisdom': 0.6
        }
        
        prediction = oracle.predict(test_data)

    # Run the test
    asyncio.run(test_metis_expert())
