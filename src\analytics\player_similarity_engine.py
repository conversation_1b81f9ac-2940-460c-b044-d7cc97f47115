import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple, Union
import logging
from dataclasses import dataclass, field
import json
from datetime import datetime
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.decomposition import PCA
import umap
from src.utils.sqlite_utils import create_connection


"""
 HYPER MEDUSA NEURAL VAULT - Player Similarity Engine 
===========================================================
Advanced player comparison system with cross-era and cross-league analysis capabilities.

Features:
- Neural player similarity calculation with profile embedding
- Cross-era normalization for fair historical comparisons
- NBA-WNBA translation layer for cross-league analysis
- Archetype detection and classification
- Career trajectory projection based on similar player paths
- Advanced statistical fingerprinting for player identification
- Similarity visualization and reporting
"""

# UMAP is optional - will be used if available
try:
    UMAP_AVAILABLE = True
except ImportError:
    UMAP_AVAILABLE = False

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants for player similarity
# Key statistics to consider for similarity calculations
KEY_STATS = [
    # Core stats
    'pts', 'reb', 'ast', 'stl', 'blk', 'tov', 'pf',
    # Shooting splits
    'fg_pct', 'fg3_pct', 'ft_pct',
    # Advanced metrics
    'ts_pct', 'efg_pct', 'usg_pct', 'ast_pct', 'reb_pct',
    'stl_pct', 'blk_pct', 'tov_pct',
    # Physical attributes
    'height', 'weight',
    # Playing style
    'pct_fga_2pt', 'pct_fga_3pt', 'pct_pts_2pt', 'pct_pts_3pt', 'pct_pts_ft',
    'pct_pts_fb', 'pct_pts_paint', 'pace'
]

# Player position groups for more accurate comparisons
POSITION_GROUPS = {
    'guards': ['PG', 'SG', 'G'],
    'wings': ['SF', 'SG/SF', 'SF/PF'],
    'bigs': ['PF', 'C', 'PF/C']
}

# Era definitions for normalization
ERAS = {
    'pre_three_point': (1950, 1979),
    'early_three_point': (1980, 1994),
    'isolation_era': (1995, 2004),
    'seven_seconds_or_less': (2005, 2014),
    'pace_and_space': (2015, 2020),
    'modern': (2021, 2025)
}

# League adjustment factors for WNBA-NBA comparison
# These are approximate factors based on league averages and differences
LEAGUE_ADJUSTMENT = {
    'pts': 1.10, # NBA scoring is ~10% higher
    'reb': 1.08,
    'ast': 1.05,
    'blk': 1.12,
    'stl': 0.95, # WNBA has slightly higher steal rates
    'tov': 0.92,
    'fg_pct': 1.02,
    'fg3_pct': 1.03,
    'usg_pct': 1.0, # Usage should be normalized by team
    'pace': 1.04, # NBA pace is typically faster
}

@dataclass
class PlayerProfile:
    """Complete player profile with statistical fingerprint"""
    hero_id: str
    player_name: str
    league: str
    position: str
    seasons: List[int]
    raw_stats: Dict[str, float]
    normalized_stats: Dict[str, float]
    era: str = ""
    archetype: str = ""
    physical_profile: Dict[str, Any] = field(default_factory=dict)
    style_profile: Dict[str, float] = field(default_factory=dict)
    embedding: np.ndarray = None
    
    def __post_init__(self):
        """Set era based on player's prime seasons"""
        if not self.era and self.seasons:
            mid_career = sorted(self.seasons)[len(self.seasons)//2]
            # Convert to integer if it's a string
            if isinstance(mid_career, str):
                mid_career = int(mid_career)
            for era_name, (start_year, end_year) in ERAS.items():
                if start_year <= mid_career <= end_year:
                    self.era = era_name
                    break
        
        if not self.era:
            self.era = "modern" # Default to modern era


class PlayerSimilarityEngine:
    """Advanced player similarity analysis engine"""
    
    def __init__(self, db_path: str = "medusa_vault.db"):
        self.db_path = db_path
        self.scaler = StandardScaler()
        self.player_profiles = {} # Cache of player profiles
        self.era_stats = {} # Cache of era average stats
        self.dimensionality_reducer = None # For visualization
    
    def _fetch_player_stats(self, hero_id: str, league: str = "NBA") -> Dict[str, Any]:
        """Fetch player career statistics from database"""
        conn = create_connection(self.db_path)
        cursor = conn.cursor()
        
        # Fetch player info
        cursor.execute("""
        SELECT player_name, position, height, weight, draft_year
        FROM player_info 
        WHERE hero_id = ? AND league = ?
        """, (hero_id, league))
        
        player_info = cursor.fetchone()
        if not player_info:
            conn.close()
            raise ValueError(f"Player with ID {hero_id} not found in {league}")
        
        player_name, position, height, weight, draft_year = player_info
        
        # Fetch seasons played
        cursor.execute("""
        SELECT DISTINCT season 
        FROM player_season_stats 
        WHERE hero_id = ? AND league = ?
        ORDER BY season
        """, (hero_id, league))
        
        seasons = [int(row[0]) for row in cursor.fetchall()]
        
        # Fetch career averages
        cursor.execute("""
        SELECT 
        AVG(pts) as pts, 
        AVG(reb) as reb, 
        AVG(ast) as ast,
        AVG(stl) as stl, 
        AVG(blk) as blk, 
        AVG(tov) as tov,
        AVG(pf) as pf,
        AVG(fg_pct) as fg_pct, 
        AVG(fg3_pct) as fg3_pct, 
        AVG(ft_pct) as ft_pct,
        AVG(ts_pct) as ts_pct,
        AVG(efg_pct) as efg_pct,
        AVG(usg_pct) as usg_pct,
        AVG(ast_pct) as ast_pct,
        AVG(reb_pct) as reb_pct,
        AVG(stl_pct) as stl_pct,
        AVG(blk_pct) as blk_pct,
        AVG(tov_pct) as tov_pct
        FROM player_season_stats
        WHERE hero_id = ? AND league = ? AND min > 500
        """, (hero_id, league))
        
        career_stats = dict(zip([col[0] for col in cursor.description], cursor.fetchone()))
        
        # Fetch playing style stats
        cursor.execute("""
        SELECT 
        AVG(pct_fga_2pt) as pct_fga_2pt, 
        AVG(pct_fga_3pt) as pct_fga_3pt,
        AVG(pct_pts_2pt) as pct_pts_2pt, 
        AVG(pct_pts_3pt) as pct_pts_3pt,
        AVG(pct_pts_ft) as pct_pts_ft,
        AVG(pct_pts_fb) as pct_pts_fb,
        AVG(pct_pts_paint) as pct_pts_paint,
        AVG(pace) as pace
        FROM player_style_stats
        WHERE hero_id = ? AND league = ? AND min > 500
        """, (hero_id, league))
        
        style_stats_row = cursor.fetchone()
        if style_stats_row:
            style_stats = dict(zip([col[0] for col in cursor.description], style_stats_row))
        else:
            # Provide default values if style stats aren't available
            style_stats = {
                'pct_fga_2pt': 0.6, 'pct_fga_3pt': 0.4,
                'pct_pts_2pt': 0.6, 'pct_pts_3pt': 0.3, 'pct_pts_ft': 0.1,
                'pct_pts_fb': 0.1, 'pct_pts_paint': 0.3, 'pace': 100
            }
        
        conn.close()
        
        # Combine all stats
        all_stats = {**career_stats, **style_stats}
        
        # Create physical profile
        physical_profile = {
            'height': height,
            'weight': weight,
            'draft_year': draft_year
        }
        
        return {
            'player_name': player_name,
            'position': position,
            'seasons': seasons,
            'stats': all_stats,
            'physical_profile': physical_profile,
            'style_profile': style_stats
        }
    
    def _get_era_stats(self, era: str, league: str = "NBA") -> Dict[str, float]:
        """Get average statistics for a specific era"""
        # Use cached era stats if available
        cache_key = f"{era}_{league}"
        if cache_key in self.era_stats:
            return self.era_stats[cache_key]
        
        # Otherwise fetch from database
        era_years = ERAS.get(era, ERAS['modern'])
        start_year, end_year = era_years
        
        conn = create_connection(self.db_path)
        cursor = conn.cursor()
        
        # Get league averages for the era
        cursor.execute("""
        SELECT 
        AVG(pts_per_game) as pts, 
        AVG(reb_per_game) as reb, 
        AVG(ast_per_game) as ast,
        AVG(stl_per_game) as stl, 
        AVG(blk_per_game) as blk, 
        AVG(tov_per_game) as tov,
        AVG(pf_per_game) as pf,
        AVG(fg_pct) as fg_pct, 
        AVG(fg3_pct) as fg3_pct, 
        AVG(ft_pct) as ft_pct,
        AVG(league_pace) as pace
        FROM league_season_stats
        WHERE league = ? AND season BETWEEN ? AND ?
        """, (league, start_year, end_year))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            era_stats = dict(zip([col[0] for col in cursor.description], result))
        else:
            # Default stats if no data found
            era_stats = {
                'pts': 100.0, 'reb': 40.0, 'ast': 20.0, 'stl': 7.0, 'blk': 4.0, 
                'tov': 14.0, 'pf': 20.0, 'fg_pct': 0.46, 'fg3_pct': 0.35, 
                'ft_pct': 0.78, 'pace': 100.0
            }
        
        # Cache the result
        self.era_stats[cache_key] = era_stats
        return era_stats
    
    def _normalize_stats(self, player_stats: Dict[str, float], player_era: str, league: str) -> Dict[str, float]:
        """Normalize player statistics to modern era for fair comparison"""
        modern_stats = self._get_era_stats('modern', league)
        era_stats = self._get_era_stats(player_era, league)
        
        normalized_stats = {}
        
        # Normalize counting stats by era averages
        for stat in ['pts', 'reb', 'ast', 'stl', 'blk', 'tov', 'pf']:
            player_val = player_stats.get(stat)
            modern_val = modern_stats.get(stat) if modern_stats else None
            era_val = era_stats.get(stat) if era_stats else None
            
            if (player_val is not None and modern_val is not None and 
                era_val is not None and era_val > 0):
                era_factor = modern_val / era_val
                normalized_stats[stat] = player_val * era_factor
            else:
                normalized_stats[stat] = player_val if player_val is not None else 0.0
        
        # Normalize shooting percentages (less adjustment needed)
        for stat in ['fg_pct', 'fg3_pct', 'ft_pct', 'ts_pct', 'efg_pct']:
            player_val = player_stats.get(stat)
            modern_val = modern_stats.get(stat) if modern_stats else None
            era_val = era_stats.get(stat) if era_stats else None
            
            if (player_val is not None and modern_val is not None and 
                era_val is not None and era_val > 0):
                # Use a softer adjustment for percentages
                era_factor = modern_val / era_val
                adjustment = (era_factor - 1) * 0.7 + 1 # 70% of the full adjustment
                normalized_stats[stat] = player_val * adjustment
            else:
                normalized_stats[stat] = player_val if player_val is not None else 0.0
        
        # Copy other stats directly
        for stat in player_stats:
            if stat not in normalized_stats:
                normalized_stats[stat] = player_stats[stat]
        
        return normalized_stats
    
    def _translate_league_stats(self, stats: Dict[str, float], from_league: str, to_league: str) -> Dict[str, float]:
        """Translate statistics between leagues (NBA/WNBA)"""
        if from_league == to_league:
            return stats # No translation needed
        
        translated_stats = {}
        
        # Apply league adjustment factors
        for stat, value in stats.items():
            factor = LEAGUE_ADJUSTMENT.get(stat, 1.0)
            
            if from_league == "WNBA" and to_league == "NBA":
                translated_stats[stat] = value * factor
            elif from_league == "NBA" and to_league == "WNBA":
                translated_stats[stat] = value / factor
            else:
                translated_stats[stat] = value
        
        return translated_stats
    
    def get_player_profile(self, hero_id: str, league: str = "NBA", force_refresh: bool = False) -> PlayerProfile:
        """Get or create a player profile"""
        cache_key = f"{hero_id}_{league}"
        
        # Return cached profile if available unless forced to refresh
        if cache_key in self.player_profiles and not force_refresh:
            return self.player_profiles[cache_key]
        
        # Fetch player data
        player_data = self._fetch_player_stats(hero_id, league)
        
        # Normalize stats to modern era
        seasons = player_data['seasons']
        era = self._determine_player_era(seasons)
        normalized_stats = self._normalize_stats(player_data['stats'], era, league)
        
        # Create player profile
        profile = PlayerProfile(
            hero_id=hero_id,
            player_name=player_data['player_name'],
            league=league,
            position=player_data['position'],
            seasons=seasons,
            raw_stats=player_data['stats'],
            normalized_stats=normalized_stats,
            era=era,
            physical_profile=player_data['physical_profile'],
            style_profile=player_data['style_profile']
        ) 
        # Cache the profile
        self.player_profiles[cache_key] = profile
        return profile
    
    def _determine_player_era(self, seasons: List[int]) -> str:
        """Determine a player's primary era based on their seasons played"""
        if not seasons:
            return "modern" # Default to modern era
        # Use the middle of their career to determine era
        # Ensure all seasons are integers
        int_seasons = [int(s) if isinstance(s, str) else s for s in seasons]
        mid_career = sorted(int_seasons)[len(int_seasons)//2]
        
        for era_name, (start_year, end_year) in ERAS.items():
            if start_year <= mid_career <= end_year:
                return era_name
        
        return "modern" # Default to modern era
    
    def generate_player_embedding(self, player_profile: PlayerProfile) -> np.ndarray:
        """Generate a numerical embedding vector for a player"""
        # Extract the key stats for the embedding
        stats_vector = []
        
        for stat in KEY_STATS:
            if stat in player_profile.normalized_stats:
                value = player_profile.normalized_stats[stat]
                # Handle None and NaN values
                if value is None or (isinstance(value, float) and np.isnan(value)):
                    stats_vector.append(0.0)
                else:
                    stats_vector.append(float(value))
            else:
                stats_vector.append(0.0) # Default value if stat is missing
        
        # Convert to numpy array and check for any remaining NaN values
        embedding = np.array(stats_vector)
        
        # Replace any NaN values with 0
        embedding = np.nan_to_num(embedding, nan=0.0)
        
        # Store in the player profile
        player_profile.embedding = embedding
        
        return embedding
    
    def find_similar_players(self, hero_id: str, league: str = "NBA", 
                             target_league: str = None, top_n: int = 10, 
                             include_same_player: bool = False,
                             position_filter: bool = True) -> List[Dict[str, Any]]:
        """Find players most similar to the given player"""
        # Default target league to the same as the player
        if target_league is None:
            target_league = league
        
        # Get the player profile
        source_profile = self.get_player_profile(hero_id, league)
        source_position_group = self._get_position_group(source_profile.position)
        
        # Generate embedding if not already done
        if source_profile.embedding is None:
            self.generate_player_embedding(source_profile)
        
        # Get all players from the target league
        conn = create_connection(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute("""
        SELECT DISTINCT hero_id, player_name, position
        FROM player_info
        WHERE league = ?
        """, (target_league,))
        
        target_players = cursor.fetchall()
        conn.close()
        
        # Find similarities
        similarities = []
        
        for target_id, target_name, target_position in target_players:
            # Skip the same player if not requested
            if target_id == hero_id and league == target_league and not include_same_player:
                continue
            
            # Apply position filter if requested
            if position_filter:
                target_position_group = self._get_position_group(target_position)
                if target_position_group != source_position_group:
                    continue
            
            try:
                # Get target player profile
                target_profile = self.get_player_profile(target_id, target_league)
                
                # Generate embedding if not already done
                if target_profile.embedding is None:
                    self.generate_player_embedding(target_profile)
                
                # Calculate similarity
                similarity = self._calculate_similarity(source_profile, target_profile)
                
                # Store similarity result
                similarities.append({
                    'hero_id': target_id,
                    'player_name': target_name,
                    'league': target_league,
                    'position': target_position,
                    'similarity_score': similarity,
                    'era': target_profile.era
                })
            
            except Exception as e:
                logger.warning(f"Error calculating similarity for player {target_id}: {e}")
                continue
        
        # Sort by similarity score and return top_n
        similarities.sort(key=lambda x: x['similarity_score'], reverse=True)
        return similarities[:top_n]
    
    def _calculate_similarity(self, profile1: PlayerProfile, profile2: PlayerProfile) -> float:
        """Calculate similarity score between two player profiles"""
        # Ensure both profiles have embeddings
        if profile1.embedding is None or profile2.embedding is None:
            raise ValueError("Both player profiles must have embeddings")
        
        # If comparing across leagues, translate the embeddings
        if profile1.league != profile2.league:
            # We'll use player1's league as the reference
            if profile2.league != profile1.league:
                # Create a copy to avoid modifying the original
                profile2_embedding = profile2.embedding.copy()
                
                # Apply league adjustment factors to key stats
                for i, stat in enumerate(KEY_STATS):
                    factor = LEAGUE_ADJUSTMENT.get(stat, 1.0)
                    if profile2.league == "WNBA" and profile1.league == "NBA":
                        profile2_embedding[i] *= factor
                    else:
                        profile2_embedding[i] /= factor
            else:
                profile2_embedding = profile2.embedding
        else:
            profile2_embedding = profile2.embedding
        
        # Calculate cosine similarity
        similarity = cosine_similarity(
            profile1.embedding.reshape(1, -1),
            profile2_embedding.reshape(1, -1)
        )[0][0]
        
        # Scale to 0-100 range
        similarity = (similarity + 1) / 2 * 100
        
        return similarity
    
    def _get_position_group(self, position: str) -> str:
        """Map a position to a position group"""
        position = position.upper()
        
        for group, positions in POSITION_GROUPS.items():
            if position in positions or any(pos in position for pos in positions):
                return group
        
        # Default to wings for unknown positions
        return 'wings'
    
    def visualize_player_similarities(self, player_ids: List[str], league: str = "NBA") -> plt.Figure:
        """Create a 2D visualization of player similarities"""
        if not player_ids:
            raise ValueError("Must provide at least one player ID")
        
        # Get profiles for all players
        profiles = [self.get_player_profile(pid, league) for pid in player_ids]
        
        # Generate embeddings for any profiles that need them
        for profile in profiles:
            if profile.embedding is None:
                self.generate_player_embedding(profile)
        
        # Collect all embeddings and names
        embeddings = np.array([p.embedding for p in profiles])
        names = [p.player_name for p in profiles]
        
        # Use PCA to reduce dimensions to 2D
        if embeddings.shape[0] > 2:
            pca = PCA(n_components=2)
            embedding_2d = pca.fit_transform(embeddings)
        else:
            # If 2 or fewer players, just use the first 2 dimensions
            embedding_2d = embeddings[:, :2]
        
        # Create a figure
        fig, ax = plt.subplots(figsize=(12, 10))
        
        # Plot the points
        ax.scatter(embedding_2d[:, 0], embedding_2d[:, 1], s=100, alpha=0.7)
        
        # Add labels for each point
        for i, (x, y) in enumerate(embedding_2d):
            ax.annotate(names[i], (x, y), fontsize=12, 
                        xytext=(5, 5), textcoords='offset points')
        
        ax.set_title(f"Player Similarity Visualization ({league})", fontsize=16)
        ax.grid(True, linestyle='--', alpha=0.7)
        
        return fig
    
    def get_player_archetype(self, hero_id: str, league: str = "NBA") -> str:
        """Determine a player's archetype based on their statistical profile"""
        profile = self.get_player_profile(hero_id, league)
        
        # Generate embedding if not already done
        if profile.embedding is None:
            self.generate_player_embedding(profile)
        # Extract key metrics for archetype determination
        pts = profile.normalized_stats.get('pts', 0) or 0
        reb = profile.normalized_stats.get('reb', 0) or 0
        ast = profile.normalized_stats.get('ast', 0) or 0
        fg3_pct = profile.normalized_stats.get('fg3_pct', 0) or 0
        pct_pts_3pt = profile.style_profile.get('pct_pts_3pt', 0) or 0
        pct_pts_paint = profile.style_profile.get('pct_pts_paint', 0) or 0
        
        # Determine archetype based on statistical profile
        position_group = self._get_position_group(profile.position)
        
        if position_group == 'guards':
            if ast >= 7:
                if pts >= 20:
                    return "Point Creator"
                else:
                    return "Floor General"
            elif pct_pts_3pt >= 0.4 and fg3_pct >= 0.37:
                return "Sharpshooter"
            elif pts >= 20:
                return "Scoring Guard"
            else:
                return "Combo Guard"
        
        elif position_group == 'wings':
            if pts >= 22:
                if ast >= 5:
                    return "Point Forward"
                else:
                    return "Wing Scorer"
            elif pct_pts_3pt >= 0.45:
                return "3-and-D Wing"
            elif reb >= 7:
                return "Two-Way Forward"
            else:
                return "Role Player Wing"
        
        elif position_group == 'bigs':
            if pts >= 20 and pct_pts_paint >= 0.5:
                return "Scoring Big"
            elif ast >= 4:
                return "Playmaking Big"
            elif reb >= 10:
                return "Glass Cleaner"
            elif pct_pts_3pt >= 0.3:
                return "Stretch Big"
            else:
                return "Traditional Big"
        
        return "Versatile Player" # Default archetype
    
    def save_similarity_analysis(self, source_player_id: str, league: str,
                                 similar_players: List[Dict[str, Any]]):
        """Save similarity analysis results to database"""
        conn = create_connection(self.db_path)
        cursor = conn.cursor()
        
        # Create table if it doesn't exist
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS player_similarity_analysis (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        source_player_id TEXT NOT NULL,
        source_league TEXT NOT NULL,
        analysis_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        similar_players TEXT NOT NULL
        )
        ''')
        
        # Insert analysis data
        cursor.execute('''
        INSERT INTO player_similarity_analysis 
        (source_player_id, source_league, similar_players)
        VALUES (?, ?, ?) ''', (source_player_id, league, json.dumps(similar_players)))
        
        conn.commit()
        conn.close()
