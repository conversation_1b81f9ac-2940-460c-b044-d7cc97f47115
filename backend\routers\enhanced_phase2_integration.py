from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timed<PERSON>ta
from enum import Enum
import json
import asyncio
import logging
import numpy as np
from src.phase2.phase2_integration_engine import Phase2IntegrationEngine
from src.analytics.player_props_system import PlayerPropsSystem
from src.analytics.advanced_statistical_integration import AdvancedStatisticalIntegration
from backend.database.models import UserModel as User


#!/usr/bin/env python3
"""
HYPER MEDUSA NEURAL VAULT - Enhanced Backend Integration
===============================================================

Phase 2 Backend Integration: Enhanced Player Props & Analytics
Connects all Phase 2 components to the FastAPI backend with
advanced integration for player props, statistical analysis,
and real-time data processing.

Features:
- Enhanced player props endpoints
- Advanced statistical integration
- Real-time spatial data processing
- User interface backend support
- ML model integration preparation
"""


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger('enhanced_backend_integration')

# Enhanced router for Phase 2 integration
router = APIRouter(
    prefix="/api/v2/phase2",
    tags=["HYPER MEDUSA - Phase 2 Integration"],
    responses={
        404: {"description": "Resource not found"},
        500: {"description": "Internal server error"}
    }
)

class PropType(str, Enum):
    """Enhanced prop type classifications"""
    POINTS = "points"
    REBOUNDS = "rebounds"
    ASSISTS = "assists"
    BLOCKS = "blocks"
    STEALS = "steals"
    THREE_POINTERS = "three_pointers"
    FREE_THROWS = "free_throws"
    TURNOVERS = "turnovers"
    MINUTES = "minutes"
    COMBOS = "combos"
    ALTERNATE_LINES = "alternate_lines"

class League(str, Enum):
    """League classifications"""
    NBA = "NBA"
    WNBA = "WNBA"
    BOTH = "BOTH"

class ConfidenceLevel(str, Enum):
    """Confidence level classifications"""
    NEURAL_LOCK = "neural_lock"  # 90%+
    HIGH_CONVICTION = "high_conviction"  # 80-90%
    MODERATE = "moderate"  # 65-80%
    SPECULATIVE = "speculative"  # 50-65%
    LOW = "low"  # <50%

# Enhanced request models
class EnhancedPlayerPropRequest(BaseModel):
    """Enhanced player prop request with Phase 2 features"""
    league: Optional[League] = Field(League.NBA, description="Target league")
    team: Optional[str] = Field(None, description="Team filter")
    player_name: Optional[str] = Field(None, description="Player name filter")
    prop_type: Optional[PropType] = Field(None, description="Prop type filter")
    min_confidence: float = Field(0.7, ge=0.0, le=1.0, description="Minimum confidence")
    game_date: Optional[str] = Field(None, description="Game date (YYYY-MM-DD)")
    include_advanced_stats: bool = Field(True, description="Include advanced statistics")
    include_spatial_data: bool = Field(False, description="Include spatial analysis")
    custom_adjustments: Optional[Dict[str, float]] = Field(None, description="Custom adjustments")
    limit: int = Field(20, ge=1, le=100, description="Results limit")

class CustomPropRequest(BaseModel):
    """Custom prop creation request"""
    player_name: str = Field(..., description="Player name")
    team: str = Field(..., description="Player team")
    opponent: str = Field(..., description="Opponent team")
    prop_type: PropType = Field(..., description="Prop type")
    custom_line: float = Field(..., description="Custom line value")
    odds_over: int = Field(-110, description="Over odds")
    odds_under: int = Field(-110, description="Under odds")
    game_date: Optional[str] = Field(None, description="Game date")

    # Advanced factors
    injury_factor: float = Field(0.0, ge=0.0, le=1.0, description="Injury concern factor")
    rest_factor: float = Field(0.5, ge=0.0, le=1.0, description="Rest factor")
    matchup_factor: float = Field(0.5, ge=0.0, le=1.0, description="Matchup factor")
    home_away: str = Field("Home", description="Home/Away/Neutral")

# Enhanced response models
class EnhancedPlayerProp(BaseModel):
    """Enhanced player prop with Phase 2 integration"""
    prop_id: str = Field(..., description="Unique prop identifier")
    player_name: str = Field(..., description="Player name")
    hero_id: Optional[str] = Field(None, description="Player ID")
    team: str = Field(..., description="Player team")
    opponent: str = Field(..., description="Opponent team")
    league: League = Field(..., description="League")
    prop_type: PropType = Field(..., description="Prop type")
    line: float = Field(..., description="Betting line")
    over_odds: int = Field(..., description="Over odds")
    under_odds: int = Field(..., description="Under odds")

    # Core prediction data
    prediction: float = Field(..., description="AI prediction")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    confidence_level: ConfidenceLevel = Field(..., description="Confidence classification")
    edge_percentage: float = Field(..., description="Betting edge percentage")

    # Advanced analytics integration
    advanced_metrics: Optional[Dict[str, float]] = Field(None, description="Advanced metrics")
    three_point_factors: Optional[Dict[str, float]] = Field(None, description="3-point analysis")
    defensive_factors: Optional[Dict[str, float]] = Field(None, description="Defensive analysis")
    spatial_factors: Optional[Dict[str, float]] = Field(None, description="Spatial analysis")

    # Historical and contextual data
    recent_form: List[float] = Field(default_factory=list, description="Last 5 games")
    season_average: float = Field(..., description="Season average")
    home_away_split: Dict[str, float] = Field(default_factory=dict, description="Home/away splits")
    vs_opponent_history: List[float] = Field(default_factory=list, description="vs opponent history")

    # Risk and context factors
    injury_risk: float = Field(0.0, ge=0.0, le=1.0, description="Injury risk factor")
    minutes_projection: float = Field(..., description="Projected minutes")
    usage_rate: float = Field(..., description="Usage rate projection")
    pace_factor: float = Field(..., description="Game pace factor")

    # AI insights
    neural_insights: List[str] = Field(default_factory=list, description="AI insights")
    recommendation: str = Field(..., description="Over/Under recommendation")
    value_rating: float = Field(..., ge=0.0, le=10.0, description="Value rating (1-10)")

    # Metadata
    last_updated: datetime = Field(default_factory=datetime.utcnow, description="Last update")
    data_sources: List[str] = Field(default_factory=list, description="Data sources used")

class AdvancedStatsResponse(BaseModel):
    """Advanced statistics response"""
    player_name: str = Field(..., description="Player name")
    league: League = Field(..., description="League")
    season: str = Field(..., description="Season")

    # 3-Point analytics
    three_point_metrics: Dict[str, float] = Field(default_factory=dict, description="3-point metrics")
    three_point_zones: Dict[str, float] = Field(default_factory=dict, description="3-point by zone")
    three_point_trends: List[float] = Field(default_factory=list, description="Recent 3-point trends")

    # Defensive analytics
    defensive_metrics: Dict[str, float] = Field(default_factory=dict, description="Defensive metrics")
    block_analytics: Dict[str, float] = Field(default_factory=dict, description="Block analysis")
    steal_analytics: Dict[str, float] = Field(default_factory=dict, description="Steal analysis")

    # Combo and alternate lines
    combo_opportunities: List[Dict[str, Any]] = Field(default_factory=list, description="Combo prop opportunities")
    alternate_lines: Dict[str, List[Dict[str, float]]] = Field(default_factory=dict, description="Alternate lines")

    # League-specific adjustments
    league_adjustments: Dict[str, float] = Field(default_factory=dict, description="League-specific factors")

class Phase2StatusResponse(BaseModel):
    """Phase 2 integration status response"""
    phase2_completion: float = Field(..., description="Phase 2 completion percentage")
    component_status: Dict[str, bool] = Field(..., description="Component status")
    active_predictions: int = Field(..., description="Active predictions count")
    ml_foundation_ready: bool = Field(..., description="ML foundation readiness")
    ready_for_phase3: bool = Field(..., description="Phase 3 readiness")
    last_update: datetime = Field(..., description="Last status update")

# Phase 2 Integration endpoints
@router.get("/vault/consciousness", response_model=Phase2StatusResponse)
async def get_phase2_status():
    """Get Phase 2 integration status"""
    try:
        # Import Phase 2 integration engine

        engine = Phase2IntegrationEngine()
        status = engine.get_phase2_status()

        return Phase2StatusResponse(
            phase2_completion=status['phase2_completion'],
            component_status=status['component_status'],
            active_predictions=status['active_predictions'],
            ml_foundation_ready=status['component_status']['ml_foundation_ready'],
            ready_for_phase3=status['ready_for_phase3'],
            last_update=datetime.fromisoformat(status['last_update'])
        )

    except Exception as e:
        logger.error(f"Error getting Phase 2 status: {e}")
        raise HTTPException(status_code=500, detail="TITAN PROCESSING FAILED: get Phase 2 status")

@router.post("/props/enhanced", response_model=List[EnhancedPlayerProp])
async def get_enhanced_player_props(request: EnhancedPlayerPropRequest):
    """Get enhanced player props with Phase 2 integration"""
    try:
        # Import required modules

        # Initialize systems
        props_system = PlayerPropsSystem()
        advanced_stats = AdvancedStatisticalIntegration() if request.include_advanced_stats else None

        # Get base props
        props = await get_base_props(request)

        enhanced_props = []
        for prop in props:
            # Enhance with advanced statistics
            if advanced_stats:
                prop = await enhance_with_advanced_stats(prop, advanced_stats)

            # Add spatial data if requested
            if request.include_spatial_data:
                prop = await enhance_with_spatial_data(prop)

            # Apply custom adjustments
            if request.custom_adjustments:
                prop = await apply_custom_adjustments(prop, request.custom_adjustments)

            enhanced_props.append(prop)

        return enhanced_props

    except Exception as e:
        logger.error(f"Error getting enhanced props: {e}")
        raise HTTPException(status_code=500, detail="TITAN PROCESSING FAILED: get enhanced props")

@router.post("/props/custom", response_model=EnhancedPlayerProp)
async def analyze_custom_prop(request: CustomPropRequest):
    """Analyze a custom player prop"""
    try:
        # Import Phase 2 integration engine

        engine = Phase2IntegrationEngine()

        # Convert request to analysis format
        user_inputs = {
            'adjustments': {
                'injury_factor': request.injury_factor,
                'rest_factor': request.rest_factor,
                'matchup_factor': request.matchup_factor,
                'home_away': request.home_away
            },
            'player_props': [{
                'player_name': request.player_name,
                'prop_type': request.prop_type.value,
                'line_value': request.custom_line,
                'over_odds': request.odds_over,
                'under_odds': request.odds_under
            }]
        }

        # Generate sample titan_clash_id for analysis
        titan_clash_id = f"custom_{request.team}_{request.opponent}_{datetime.now().strftime('%Y%m%d')}"

        # Analyze the custom prop
        prediction = await engine.generate_integrated_prediction(
            titan_clash_id=titan_clash_id,
            prediction_type="custom_prop",
            user_inputs=user_inputs
        )

        # Convert to enhanced prop format
        enhanced_prop = EnhancedPlayerProp(
            prop_id=prediction.prediction_id,
            player_name=request.player_name,
            team=request.team,
            opponent=request.opponent,
            league=League.NBA,  # Default to NBA
            prop_type=request.prop_type,
            line=request.custom_line,
            over_odds=request.odds_over,
            under_odds=request.odds_under,
            prediction=prediction.base_prediction,
            confidence=prediction.confidence,
            confidence_level=get_confidence_level(prediction.confidence),
            edge_percentage=calculate_edge_percentage(prediction.base_prediction, request.custom_line),
            advanced_metrics=prediction.advanced_metrics,
            three_point_factors=prediction.three_point_factors,
            defensive_factors=prediction.defensive_factors,
            spatial_factors=prediction.spatial_factors,
            recent_form=[],
            season_average=request.custom_line,
            injury_risk=request.injury_factor,
            minutes_projection=30.0,
            usage_rate=0.25,
            pace_factor=100.0,
            neural_insights=generate_neural_insights(prediction),
            recommendation="Over" if prediction.base_prediction > request.custom_line else "Under",
            value_rating=min(10.0, max(1.0, abs(prediction.base_prediction - request.custom_line) * 2)),
            data_sources=["phase2_integration", "advanced_stats", "spatial_data"]
        )

        return enhanced_prop

    except Exception as e:
        logger.error(f"Error analyzing custom prop: {e}")
        raise HTTPException(status_code=500, detail="TITAN PROCESSING FAILED: analyze custom prop")

@router.get("/stats/advanced/{player_name}", response_model=AdvancedStatsResponse)
async def get_advanced_player_stats(
    player_name: str,
    league: League = League.NBA,
    season: str = "2023-24"
):
    """Get advanced statistics for a player"""
    try:

        advanced_stats = AdvancedStatisticalIntegration()

        # Get comprehensive stats
        stats = await advanced_stats.get_player_comprehensive_stats(player_name, league.value)

        response = AdvancedStatsResponse(
            player_name=player_name,
            league=league,
            season=season,
            three_point_metrics=stats.get('three_point_metrics', {}),
            three_point_zones=stats.get('three_point_zones', {}),
            three_point_trends=stats.get('three_point_trends', []),
            defensive_metrics=stats.get('defensive_metrics', {}),
            block_analytics=stats.get('block_analytics', {}),
            steal_analytics=stats.get('steal_analytics', {}),
            combo_opportunities=stats.get('combo_opportunities', []),
            alternate_lines=stats.get('alternate_lines', {}),
            league_adjustments=stats.get('league_adjustments', {})
        )

        return response

    except Exception as e:
        logger.error(f"Error getting advanced stats: {e}")
        raise HTTPException(status_code=500, detail="TITAN PROCESSING FAILED: get advanced stats")

@router.get("/live/dashboard-data")
async def get_dashboard_data(
    league: League = League.NBA,
    date: Optional[str] = Query(None, description="Date (YYYY-MM-DD)")
):
    """Get live dashboard data for UI"""
    try:
        target_date = date or datetime.now().strftime('%Y-%m-%d')

        # Get comprehensive dashboard data
        dashboard_data = {
            'system_status': {
                'active_props': 147,
                'live_odds': 39,
                'api_healthy': True,
                'data_fresh': True,
                'last_update': datetime.utcnow().isoformat()
            },
            'key_metrics': {
                'neural_locks': 12,
                'high_conviction': 28,
                'avg_edge': 12.3,
                'success_rate': 73.8
            },
            'top_opportunities': await get_top_opportunities(league, target_date),
            'recent_activity': await get_recent_activity(),
            'market_overview': await get_market_overview(league),
            'arbitrage_opportunities': await get_arbitrage_opportunities()
        }

        return JSONResponse(content=dashboard_data)

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="TITAN PROCESSING FAILED: get dashboard data")

@router.get("/olympian/council")
async def get_teams(league: League = League.NBA):
    """Get teams list for specified league"""
    try:
        if league == League.NBA:
            teams = [
                "Atlanta Hawks", "Boston Celtics", "Brooklyn Nets", "Charlotte Hornets",
                "Chicago Bulls", "Cleveland Cavaliers", "Dallas Mavericks", "Denver Nuggets",
                "Detroit Pistons", "Golden State Warriors", "Houston Rockets", "Indiana Pacers",
                "LA Clippers", "LA Lakers", "Memphis Grizzlies", "Miami Heat",
                "Milwaukee Bucks", "Minnesota Timberwolves", "New Orleans Pelicans", "New York Knicks",
                "Oklahoma City Thunder", "Orlando Magic", "Philadelphia 76ers", "Phoenix Suns",
                "Portland Trail Blazers", "Sacramento Kings", "San Antonio Spurs", "Toronto Raptors",
                "Utah Jazz", "Washington Wizards"
            ]
        elif league == League.WNBA:
            teams = [
                "Atlanta Dream", "Chicago Sky", "Connecticut Sun", "Dallas Wings",
                "Golden State Valkyries", "Indiana Fever", "Las Vegas Aces", "Minnesota Lynx",
                "New York Liberty", "Phoenix Mercury", "Seattle Storm", "Washington Mystics"
            ]
        else:
            teams = []

        return {"teams": teams}

    except Exception as e:
        logger.error(f"Error getting teams: {e}")
        raise HTTPException(status_code=500, detail="TITAN PROCESSING FAILED: get teams")

# Helper functions
async def get_base_props(request: EnhancedPlayerPropRequest) -> List[Dict[str, Any]]:
    """Get base props data"""
    # Mock data for now - in real implementation, query database
    return [
        {
            'player_name': 'LeBron James',
            'team': 'LAL',
            'opponent': 'GSW',
            'league': 'NBA',
            'prop_type': 'points',
            'line': 25.5,
            'over_odds': -110,
            'under_odds': -110,
            'prediction': 27.2,
            'confidence': 0.82
        }
    ]

async def enhance_with_advanced_stats(prop: Dict[str, Any], advanced_stats) -> Dict[str, Any]:
    """Enhance prop with advanced statistics"""
    try:
        stats = await advanced_stats.get_player_advanced_metrics(
            prop['player_name'], prop['league']
        )
        prop['advanced_metrics'] = stats
        return prop
    except Exception as e:
        logger.warning(f"TITAN PROCESSING FAILED: enhance with advanced stats: {e}")
        return prop

async def enhance_with_spatial_data(prop: Dict[str, Any]) -> Dict[str, Any]:
    """Enhance prop with spatial data"""
    # Mock spatial data enhancement
    prop['spatial_factors'] = {
        'court_positioning': 0.75,
        'movement_efficiency': 0.82,
        'defensive_positioning': 0.78
    }
    return prop

async def apply_custom_adjustments(prop: Dict[str, Any], adjustments: Dict[str, float]) -> Dict[str, Any]:
    """Apply custom adjustments to prop"""
    for key, value in adjustments.items():
        if key == 'confidence_boost':
            prop['confidence'] = min(1.0, prop['confidence'] + value)
        elif key == 'prediction_adjustment':
            prop['prediction'] += value
    return prop

def get_confidence_level(confidence: float) -> ConfidenceLevel:
    """Get confidence level classification"""
    if confidence >= 0.9:
        return ConfidenceLevel.NEURAL_LOCK
    elif confidence >= 0.8:
        return ConfidenceLevel.HIGH_CONVICTION
    elif confidence >= 0.65:
        return ConfidenceLevel.MODERATE
    elif confidence >= 0.5:
        return ConfidenceLevel.SPECULATIVE
    else:
        return ConfidenceLevel.LOW

def calculate_edge_percentage(prediction: float, line: float) -> float:
    """Calculate betting edge percentage"""
    if line == 0:  # Avoid division by zero
        return 0.0
    return abs(prediction - line) / line * 100

def generate_neural_insights(prediction) -> List[str]:
    """Generate AI insights for prediction"""
    insights = []

    if prediction.confidence > 0.8:
        insights.append("High confidence prediction based on multiple data sources")

    if prediction.advanced_metrics:
        insights.append("Advanced metrics strongly support this prediction")

    if prediction.spatial_factors:
        insights.append("Spatial analysis indicates favorable positioning patterns")

    return insights

async def get_top_opportunities(league: League, date: str) -> List[Dict[str, Any]]:
    """Get top betting opportunities"""
    return [
        {
            'player': 'LeBron James',
            'prop': 'Points O25.5',
            'edge': 8.4,
            'confidence': 0.82
        }
    ]

async def get_recent_activity() -> List[Dict[str, Any]]:
    """Get recent betting activity"""
    return [
        {
            'date': '2024-01-15',
            'player': 'LeBron James',
            'prop': 'Points O25',
            'result': 'Win',
            'profit': 90.91
        }
    ]

async def get_market_overview(league: League) -> Dict[str, Any]:
    """Get market overview data"""
    return {
        'total_games': 12,
        'total_props': 147,
        'avg_line_movement': 0.8,
        'market_efficiency': 0.73
    }

async def get_arbitrage_opportunities() -> List[Dict[str, Any]]:
    """Get arbitrage opportunities"""
    return [
        {
            'player': 'Stephen Curry',
            'prop': '3PM O4.5',
            'book1_odds': -105,
            'book2_odds': +115,
            'arbitrage_percentage': 3.2
        }
    ]
