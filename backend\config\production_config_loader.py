import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from functools import lru_cache
import re


"""
🚀 HYPER MEDUSA NEURAL VAULT - Production Configuration Loader
============================================================

Unified production configuration management system that:
1. Loads configuration from unified YAML file
2. Validates environment variables
3. Provides type-safe configuration access
4. Implements configuration caching and hot-reloading
"""


logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str
    provider: str = "postgresql"
    pool_size: int = 50
    max_overflow: int = 100
    pool_timeout: int = 60
    pool_recycle: int = 7200
    echo: bool = False
    pool_pre_ping: bool = True
    connect_args: Dict[str, Any] = field(default_factory=dict)

@dataclass
class RedisConfig:
    """Redis configuration"""
    url: str
    password: Optional[str] = None
    max_connections: int = 100
    socket_timeout: int = 10
    socket_connect_timeout: int = 10
    retry_on_timeout: bool = True
    health_check_interval: int = 30

@dataclass
class SecurityConfig:
    """Security configuration"""
    secret_key: str
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    jwt_expiration: int = 3600
    encryption_key: Optional[str] = None
    password_min_length: int = 12
    max_login_attempts: int = 3
    lockout_duration: int = 1800
    cors_enabled: bool = True
    csrf_protection: bool = True
    rate_limit: str = "100/minute"
    cors_origins: list = field(default_factory=list)

@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    enabled: bool = True
    prometheus_enabled: bool = True
    prometheus_port: int = 9090
    grafana_enabled: bool = True
    grafana_port: int = 3000
    grafana_url: str = "http://localhost:3000"
    grafana_token: Optional[str] = None
    health_check_interval: int = 30
    metrics_retention_days: int = 30
    alerting_enabled: bool = True
    sentry_dsn: Optional[str] = None
    alert_webhook_url: Optional[str] = None
    alert_channels: list = field(default_factory=list)

@dataclass
class MLConfig:
    """Machine Learning configuration"""
    model_path: str = "/app/models/production"
    batch_size: int = 64
    learning_rate: float = 0.0001
    epochs: int = 200
    validation_split: float = 0.15
    early_stopping_patience: int = 20
    device: str = "cuda"
    mixed_precision: bool = True
    gradient_clipping: float = 1.0
    checkpoint_interval: int = 25
    model_cache_ttl_hours: int = 48
    drift_detection_enabled: bool = True

@dataclass
class ProductionConfig:
    """Main production configuration"""
    app_name: str = "HYPER_MEDUSA_NEURAL_VAULT"
    version: str = "3.0.0-PRODUCTION"
    environment: str = "production"
    debug: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    workers: int = 8
    
    # Sub-configurations
    database: DatabaseConfig = field(default_factory=lambda: DatabaseConfig(""))
    redis: RedisConfig = field(default_factory=lambda: RedisConfig(""))
    security: SecurityConfig = field(default_factory=lambda: SecurityConfig("", ""))
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    ml: MLConfig = field(default_factory=MLConfig)
    
    # Feature flags
    feature_flags: Dict[str, bool] = field(default_factory=dict)
    
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment.lower() == "production"
    
    def validate(self) -> bool:
        """Validate configuration"""
        errors = []
        
        # Validate required fields
        if not self.database.url:
            errors.append("DATABASE_URL is required")
        
        if not self.redis.url:
            errors.append("REDIS_URL is required")
        
        if not self.security.secret_key:
            errors.append("SECRET_KEY is required")
        
        if not self.security.jwt_secret:
            errors.append("JWT_SECRET is required")
        
        # Validate security key lengths
        if len(self.security.secret_key) < 32:
            errors.append("SECRET_KEY must be at least 32 characters")
        
        if len(self.security.jwt_secret) < 32:
            errors.append("JWT_SECRET must be at least 32 characters")
        
        if errors:
            logger.error(f"❌ Configuration validation failed: {errors}")
            return False
        
        logger.info("✅ Configuration validation passed")
        return True

class ProductionConfigLoader:
    """Production configuration loader with environment variable substitution"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or self._get_default_config_path()
        self._config_cache: Optional[ProductionConfig] = None
        self._env_var_pattern = re.compile(r'\$\{([^}]+)\}')
    
    def _get_default_config_path(self) -> str:
        """Get default configuration file path"""
        # Try multiple possible locations
        possible_paths = [
            "config/production_unified.yaml",
            "backend/config/production_unified.yaml",
            "../config/production_unified.yaml",
            "./production_unified.yaml"
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        
        # If none found, use the first one as default
        return possible_paths[0]
    
    def _substitute_env_vars(self, value: Any) -> Any:
        """Recursively substitute environment variables in configuration values"""
        if isinstance(value, str):
            # Find all ${VAR_NAME} patterns and replace with environment variables
            def replace_env_var(match):
                env_var = match.group(1)
                default_value = None
                
                # Handle ${VAR_NAME:default_value} syntax
                if ':' in env_var:
                    env_var, default_value = env_var.split(':', 1)
                
                env_value = os.getenv(env_var, default_value)
                if env_value is None:
                    logger.warning(f"⚠️ Environment variable {env_var} not set")
                    return match.group(0)  # Return original if not found
                
                return env_value
            
            return self._env_var_pattern.sub(replace_env_var, value)
        
        elif isinstance(value, dict):
            return {k: self._substitute_env_vars(v) for k, v in value.items()}
        
        elif isinstance(value, list):
            return [self._substitute_env_vars(item) for item in value]
        
        else:
            return value
    
    def load_config(self, force_reload: bool = False) -> ProductionConfig:
        """Load production configuration with caching"""
        if self._config_cache is not None and not force_reload:
            return self._config_cache
        
        try:
            logger.info(f"📄 Loading production configuration from {self.config_path}")
            
            # Load YAML configuration
            with open(self.config_path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)
            
            # Substitute environment variables
            config_data = self._substitute_env_vars(raw_config)
            
            # Create configuration objects
            config = self._create_config_from_dict(config_data)
            
            # Validate configuration
            if not config.validate():
                raise ValueError("Configuration validation failed")
            
            # Cache the configuration
            self._config_cache = config
            
            logger.info("✅ Production configuration loaded successfully")
            return config
            
        except Exception as e:
            logger.error(f"❌ Failed to load production configuration: {e}")
            raise
    
    def _create_config_from_dict(self, config_data: Dict[str, Any]) -> ProductionConfig:
        """Create ProductionConfig from dictionary"""
        app_config = config_data.get('app', {})

        # Create sub-configurations with field filtering
        database_config = self._create_database_config(config_data.get('database', {}))
        redis_config = self._create_redis_config(config_data.get('redis', {}))
        security_config = self._create_security_config(config_data.get('security', {}))
        monitoring_config = self._create_monitoring_config(config_data.get('monitoring', {}))
        ml_config = self._create_ml_config(config_data.get('ml', {}))
        
        # Create main configuration
        config = ProductionConfig(
            app_name=app_config.get('name', 'HYPER_MEDUSA_NEURAL_VAULT'),
            version=app_config.get('version', '3.0.0-PRODUCTION'),
            environment=app_config.get('environment', 'production'),
            debug=app_config.get('debug', False),
            host=app_config.get('host', '0.0.0.0'),
            port=app_config.get('port', 8000),
            workers=app_config.get('workers', 8),
            database=database_config,
            redis=redis_config,
            security=security_config,
            monitoring=monitoring_config,
            ml=ml_config,
            feature_flags=config_data.get('feature_flags', {})
        )
        
        return config

    def _create_database_config(self, data: Dict[str, Any]) -> DatabaseConfig:
        """Create DatabaseConfig with field filtering"""
        valid_fields = {
            'url', 'provider', 'pool_size', 'max_overflow', 'pool_timeout',
            'pool_recycle', 'echo', 'pool_pre_ping', 'connect_args'
        }
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return DatabaseConfig(**filtered_data)

    def _create_redis_config(self, data: Dict[str, Any]) -> RedisConfig:
        """Create RedisConfig with field filtering"""
        valid_fields = {
            'url', 'password', 'max_connections', 'socket_timeout',
            'socket_connect_timeout', 'retry_on_timeout', 'health_check_interval'
        }
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return RedisConfig(**filtered_data)

    def _create_security_config(self, data: Dict[str, Any]) -> SecurityConfig:
        """Create SecurityConfig with field filtering"""
        valid_fields = {
            'secret_key', 'jwt_secret', 'jwt_algorithm', 'jwt_expiration',
            'encryption_key', 'password_min_length', 'max_login_attempts',
            'lockout_duration', 'cors_enabled', 'csrf_protection', 'rate_limit',
            'cors_origins'
        }
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return SecurityConfig(**filtered_data)

    def _create_monitoring_config(self, data: Dict[str, Any]) -> MonitoringConfig:
        """Create MonitoringConfig with field filtering"""
        valid_fields = {
            'enabled', 'prometheus_enabled', 'prometheus_port', 'grafana_enabled',
            'grafana_port', 'grafana_url', 'grafana_token', 'health_check_interval',
            'metrics_retention_days', 'alerting_enabled', 'sentry_dsn',
            'alert_webhook_url', 'alert_channels'
        }
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return MonitoringConfig(**filtered_data)

    def _create_ml_config(self, data: Dict[str, Any]) -> MLConfig:
        """Create MLConfig with field filtering"""
        valid_fields = {
            'model_path', 'batch_size', 'learning_rate', 'epochs',
            'validation_split', 'early_stopping_patience', 'device',
            'mixed_precision', 'gradient_clipping', 'checkpoint_interval',
            'model_cache_ttl_hours', 'drift_detection_enabled'
        }
        filtered_data = {k: v for k, v in data.items() if k in valid_fields}
        return MLConfig(**filtered_data)

    def get_config_value(self, key_path: str, default: Any = None) -> Any:
        """Get a specific configuration value using dot notation"""
        config = self.load_config()
        
        # Split the key path and traverse the configuration
        keys = key_path.split('.')
        value = config
        
        try:
            for key in keys:
                if hasattr(value, key):
                    value = getattr(value, key)
                elif isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
            
            return value
        except Exception:
            return default
    
    def reload_config(self) -> ProductionConfig:
        """Force reload configuration from file"""
        return self.load_config(force_reload=True)

# Global configuration loader instance
_config_loader: Optional[ProductionConfigLoader] = None

def get_production_config() -> ProductionConfig:
    """Get the global production configuration instance"""
    global _config_loader
    
    if _config_loader is None:
        _config_loader = ProductionConfigLoader()
    
    return _UnifiedConfigSystem().get_config()

def get_config_value(key_path: str, default: Any = None) -> Any:
    """Get a specific configuration value using dot notation"""
    global _config_loader
    
    if _config_loader is None:
        _config_loader = ProductionConfigLoader()
    
    return _config_loader.get_config_value(key_path, default)

def reload_production_config() -> ProductionConfig:
    """Force reload the production configuration"""
    global _config_loader
    
    if _config_loader is None:
        _config_loader = ProductionConfigLoader()
    
    return _config_loader.reload_config()

# Environment validation
def validate_production_environment() -> bool:
    """Validate that all required environment variables are set for production"""
    required_env_vars = [
        'DATABASE_URL',
        'REDIS_URL',
        'SECRET_KEY',
        'JWT_SECRET',
        'ENCRYPTION_KEY'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"❌ Missing required environment variables: {missing_vars}")
        return False
    
    logger.info("✅ All required environment variables are set")
    return True

if __name__ == "__main__":
    # Test configuration loading
    try:
        config = get_production_config()

        # Validate environment
        if validate_production_environment():
            print("✅ Production environment validation successful")
            print(f"🏀 MEDUSA VAULT: Configuration loaded for {config.environment}")
        else:
            print("❌ Production environment validation failed")

    except Exception as e:
        print(f"❌ Configuration loading failed: {e}")
