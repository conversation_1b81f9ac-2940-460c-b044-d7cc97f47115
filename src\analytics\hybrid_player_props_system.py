import numpy as np
import pandas as pd
import sqlite3
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
import random
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
import joblib
import os


#!/usr/bin/env python3
"""
Hybrid Player Props System - Option 3
Combines ML models with advanced analytics for maximum accuracy (78-85% target)
"""


# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HybridPlayerPropsSystem:
    """Hybrid system combining ML models with advanced analytics"""
    
    def __init__(self, db_path: str = "medusa_master.db"):
        self.db_path = db_path
        self.ml_models = {}
        self.scalers = {}
        self.models_trained = False
        
        # Advanced analytics components
        self.matchup_analyzer = MatchupAnalyzer(db_path)
        self.trend_detector = TrendDetector(db_path)
        self.situational_analyzer = SituationalAnalyzer(db_path)
        
    def prepare_ml_training_data(self) -> Dict[str, pd.DataFrame]:
        """Prepare comprehensive training data for ML models"""
        logger.info("🔄 Preparing ML training data from comprehensive dataset...")
        
        conn = sqlite3.connect(self.db_path)
        
        # Get comprehensive player performance data
        query = """
            SELECT 
                player_name,
                season,
                stat_category,
                stat_value,
                data_category,
                game_date,
                team_abbreviation,
                league_name,
                raw_data
            FROM unified_nba_wnba_data 
            WHERE league_name = 'WNBA' 
            AND stat_value IS NOT NULL 
            AND CAST(stat_value AS FLOAT) >= 0
            AND player_name IS NOT NULL
            ORDER BY player_name, game_date
        """
        
        raw_data = pd.read_sql_query(query, conn)
        conn.close()
        
        # Process data for different prop types
        prop_datasets = {}
        
        for prop_type in ['points', 'rebounds', 'assists', '3pt_made']:
            prop_data = self._create_prop_dataset(raw_data, prop_type)
            if not prop_data.empty:
                prop_datasets[prop_type] = prop_data
                logger.info(f"✅ Created {prop_type} dataset: {len(prop_data)} samples")
        
        return prop_datasets
    
    def _create_prop_dataset(self, raw_data: pd.DataFrame, prop_type: str) -> pd.DataFrame:
        """Create ML dataset for specific prop type"""
        
        # Filter relevant data for prop type
        if prop_type == 'points':
            relevant_data = raw_data[raw_data['stat_category'].str.contains('point|pts|scoring', case=False, na=False)]
        elif prop_type == 'rebounds':
            relevant_data = raw_data[raw_data['stat_category'].str.contains('rebound|reb', case=False, na=False)]
        elif prop_type == 'assists':
            relevant_data = raw_data[raw_data['stat_category'].str.contains('assist|ast', case=False, na=False)]
        elif prop_type == '3pt_made':
            relevant_data = raw_data[raw_data['stat_category'].str.contains('3p|three', case=False, na=False)]
        else:
            return pd.DataFrame()
        
        if relevant_data.empty:
            return pd.DataFrame()
        
        # Create features for ML
        features = []
        targets = []
        
        for player in relevant_data['player_name'].unique():
            player_data = relevant_data[relevant_data['player_name'] == player].sort_values('game_date')
            
            if len(player_data) < 5:  # Need minimum data
                continue
            
            # Create rolling features
            player_data['stat_value_float'] = pd.to_numeric(player_data['stat_value'], errors='coerce')
            player_data = player_data.dropna(subset=['stat_value_float'])
            
            if len(player_data) < 5:
                continue
            
            # Rolling statistics
            player_data['rolling_avg_5'] = player_data['stat_value_float'].rolling(5, min_periods=1).mean()
            player_data['rolling_avg_10'] = player_data['stat_value_float'].rolling(10, min_periods=1).mean()
            player_data['rolling_std_5'] = player_data['stat_value_float'].rolling(5, min_periods=1).std().fillna(0)
            
            # Trend features
            player_data['recent_trend'] = player_data['stat_value_float'].rolling(3, min_periods=1).mean() / player_data['rolling_avg_10']
            player_data['recent_trend'] = player_data['recent_trend'].fillna(1.0)
            
            # Create feature vectors
            for i in range(5, len(player_data)):  # Need history for features
                feature_vector = [
                    player_data.iloc[i-1]['rolling_avg_5'],  # Recent 5-game average
                    player_data.iloc[i-1]['rolling_avg_10'], # Recent 10-game average
                    player_data.iloc[i-1]['rolling_std_5'],  # Recent consistency
                    player_data.iloc[i-1]['recent_trend'],   # Trend factor
                    len(player_data),  # Career games (experience)
                    i / len(player_data),  # Season progress
                ]
                
                target_value = player_data.iloc[i]['stat_value_float']
                
                features.append(feature_vector)
                targets.append(target_value)
        
        if not features:
            return pd.DataFrame()
        
        # Create DataFrame
        feature_names = ['recent_avg_5', 'recent_avg_10', 'consistency', 'trend', 'experience', 'season_progress']
        dataset = pd.DataFrame(features, columns=feature_names)
        dataset['target'] = targets
        
        return dataset
    
    def train_ml_models(self) -> bool:
        """Train ML models for each prop type"""
        logger.info("🧠 Training ML models for hybrid system...")
        
        # Prepare training data
        prop_datasets = self.prepare_ml_training_data()
        
        if not prop_datasets:
            logger.error("❌ No training data available")
            return False
        
        # Train models for each prop type
        for prop_type, dataset in prop_datasets.items():
            logger.info(f"🔄 Training {prop_type} model...")
            
            # Prepare features and targets
            X = dataset.drop('target', axis=1)
            y = dataset['target']
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train ensemble model
            rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
            gb_model = GradientBoostingRegressor(n_estimators=100, random_state=42)
            
            rf_model.fit(X_train_scaled, y_train)
            gb_model.fit(X_train_scaled, y_train)
            
            # Evaluate models
            rf_score = rf_model.score(X_test_scaled, y_test)
            gb_score = gb_model.score(X_test_scaled, y_test)
            
            # Choose best model
            if rf_score > gb_score:
                best_model = rf_model
                best_score = rf_score
                model_type = "RandomForest"
            else:
                best_model = gb_model
                best_score = gb_score
                model_type = "GradientBoosting"
            
            # Store model and scaler
            self.ml_models[prop_type] = best_model
            self.scalers[prop_type] = scaler
            
            logger.info(f"✅ {prop_type} model trained: {model_type}, R² = {best_score:.3f}")
        
        self.models_trained = True
        logger.info(f"🎯 ML training complete: {len(self.ml_models)} models ready")
        return True
    
    def ml_prediction(self, player_name: str, prop_type: str, recent_stats: List[float]) -> Dict[str, Any]:
        """Get ML-based prediction"""
        if not self.models_trained or prop_type not in self.ml_models:
            return {'prediction': None, 'confidence': 0.0, 'method': 'ml_unavailable'}
        
        # Create feature vector
        if len(recent_stats) < 5:
            recent_stats.extend([np.mean(recent_stats)] * (5 - len(recent_stats)))
        
        features = [
            np.mean(recent_stats[-5:]),  # Recent 5-game average
            np.mean(recent_stats[-10:]) if len(recent_stats) >= 10 else np.mean(recent_stats),  # 10-game average
            np.std(recent_stats[-5:]) if len(recent_stats) >= 5 else 0,  # Consistency
            np.mean(recent_stats[-3:]) / np.mean(recent_stats) if len(recent_stats) > 3 else 1.0,  # Trend
            len(recent_stats),  # Experience
            0.5  # Mid-season assumption
        ]
        
        # Scale features
        scaler = self.scalers[prop_type]
        features_scaled = scaler.transform([features])
        
        # Get prediction
        model = self.ml_models[prop_type]
        predicted_value = model.predict(features_scaled)[0]
        
        # Calculate confidence based on recent consistency
        consistency = 1 - min(0.5, np.std(recent_stats[-5:]) / np.mean(recent_stats[-5:])) if len(recent_stats) >= 5 else 0.5
        
        return {
            'predicted_value': predicted_value,
            'confidence': consistency,
            'method': 'ml_model'
        }
    
    def hybrid_prediction(self, player_name: str, prop_type: str, line: float, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make hybrid prediction combining ML and advanced analytics"""
        
        # Get recent player stats (simulated for now)
        recent_stats = self._get_recent_player_stats(player_name, prop_type)
        
        # 1. ML Prediction
        ml_result = self.ml_prediction(player_name, prop_type, recent_stats)
        
        # 2. Advanced Analytics
        analytics_adjustments = self._get_analytics_adjustments(player_name, prop_type, context or {})
        
        # 3. Combine predictions
        if ml_result['prediction'] is not None:
            base_prediction = ml_result['predicted_value']
            ml_confidence = ml_result['confidence']
        else:
            # Fallback to statistical average
            base_prediction = np.mean(recent_stats) if recent_stats else line
            ml_confidence = 0.3
        
        # Apply analytics adjustments
        adjusted_prediction = base_prediction * analytics_adjustments['multiplier']
        
        # Determine over/under
        prediction = "OVER" if adjusted_prediction >= line else "UNDER"
        
        # Calculate final confidence
        analytics_confidence = analytics_adjustments['confidence']
        final_confidence = (ml_confidence * 0.6 + analytics_confidence * 0.4)
        
        # Boost confidence if both methods agree strongly
        prediction_strength = abs(adjusted_prediction - line) / line
        if prediction_strength > 0.2:  # Strong prediction
            final_confidence = min(0.95, final_confidence * 1.2)
        
        return {
            'player': player_name,
            'prop_type': prop_type,
            'line': line,
            'prediction': prediction,
            'confidence': final_confidence,
            'predicted_value': adjusted_prediction,
            'ml_prediction': base_prediction,
            'analytics_multiplier': analytics_adjustments['multiplier'],
            'method': 'hybrid',
            'components': {
                'ml_confidence': ml_confidence,
                'analytics_confidence': analytics_confidence,
                'recent_avg': np.mean(recent_stats) if recent_stats else 0
            }
        }
    
    def _get_recent_player_stats(self, player_name: str, prop_type: str, num_games: int = 10) -> List[float]:
        """Get recent player stats (simplified for demo)"""
        # In real implementation, this would query actual game logs
        # For now, simulate realistic stats based on prop type
        
        if prop_type == 'points':
            base_avg = random.uniform(12, 22)
        elif prop_type == 'rebounds':
            base_avg = random.uniform(4, 10)
        elif prop_type == 'assists':
            base_avg = random.uniform(2, 7)
        elif prop_type == '3pt_made':
            base_avg = random.uniform(1, 4)
        else:
            base_avg = 10
        
        # Generate realistic game-by-game stats
        stats = []
        for _ in range(num_games):
            game_stat = max(0, random.normalvariate(base_avg, base_avg * 0.3))
            stats.append(game_stat)
        
        return stats
    
    def _get_analytics_adjustments(self, player_name: str, prop_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Get advanced analytics adjustments"""
        
        # Matchup analysis
        matchup_factor = self.matchup_analyzer.get_matchup_factor(player_name, context.get('opponent', ''))
        
        # Trend analysis
        trend_factor = self.trend_detector.get_trend_factor(player_name, prop_type)
        
        # Situational analysis
        situational_factor = self.situational_analyzer.get_situational_factor(context)
        
        # Combine factors
        combined_multiplier = matchup_factor * trend_factor * situational_factor
        
        # Calculate confidence based on data quality
        confidence = min(0.9, 0.5 + abs(combined_multiplier - 1.0) * 0.5)
        
        return {
            'multiplier': combined_multiplier,
            'confidence': confidence,
            'components': {
                'matchup': matchup_factor,
                'trend': trend_factor,
                'situational': situational_factor
            }
        }

# Supporting classes for advanced analytics
class MatchupAnalyzer:
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_matchup_factor(self, player_name: str, opponent: str) -> float:
        # Simplified matchup analysis
        return random.uniform(0.85, 1.15)

class TrendDetector:
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_trend_factor(self, player_name: str, prop_type: str) -> float:
        # Simplified trend analysis
        return random.uniform(0.90, 1.10)

class SituationalAnalyzer:
    def __init__(self, db_path: str):
        self.db_path = db_path
    
    def get_situational_factor(self, context: Dict[str, Any]) -> float:
        # Simplified situational analysis
        factors = []
        
        # Home/away
        if context.get('venue') == 'home':
            factors.append(1.05)
        elif context.get('venue') == 'away':
            factors.append(0.95)
        else:
            factors.append(1.0)
        
        # Rest days
        rest_days = context.get('rest_days', 1)
        if rest_days >= 3:
            factors.append(1.03)
        elif rest_days == 0:
            factors.append(0.97)
        else:
            factors.append(1.0)
        
        return np.mean(factors) if factors else 1.0

def main():
    """Test hybrid player props system"""
    
    # Initialize system
    hybrid_system = HybridPlayerPropsSystem()
    
    # Train ML models
    if hybrid_system.train_ml_models():
        
        # Test hybrid predictions
        test_cases = [
            {'player': 'A\'ja Wilson', 'prop_type': 'points', 'line': 22.5},
            {'player': 'Breanna Stewart', 'prop_type': 'rebounds', 'line': 8.5},
            {'player': 'Courtney Vandersloot', 'prop_type': 'assists', 'line': 6.5},
            {'player': 'Sabrina Ionescu', 'prop_type': '3pt_made', 'line': 2.5}
        ]
        
        for test in test_cases:
            context = {'venue': 'home', 'rest_days': 1, 'opponent': 'Connecticut Sun'}
            result = hybrid_system.hybrid_prediction(
                test['player'], test['prop_type'], test['line'], context
            )
            print(f"Result: {result['prediction']} " +
                  f"(conf: {result['confidence']:.1%}, pred: {result['predicted_value']:.1f})")

    else:
        print("No test data available")

if __name__ == "__main__":
    main()
