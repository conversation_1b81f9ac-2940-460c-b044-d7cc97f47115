import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple, Union, Set
from datetime import datetime, timedelta, timezone # Added timezone for consistent UTC usage
from dataclasses import dataclass, field # field imported for dataclass defaults
from enum import Enum
import uuid
import json
import hashlib
from collections import defaultdict, deque
import numpy as np
from pydantic import BaseModel, Field, validator # validator is for Pydantic V1, for V2 use field_validator or model_validator
import gzip
import base64

# Configure logger first before any imports that might use it
logger = logging.getLogger(__name__)



#!/usr/bin/env python3

"""
Expert Quantum Entanglement Management System

Advanced quantum entanglement orchestration for the Oracle prediction system
with state synchronization, temporal coherence, and basketball-aware
quantum correlations.

Features:
- Quantum state synchronization across distributed nodes
- Temporal coherence preservation
- Basketball-specific entanglement patterns
- Advanced security and encryption
- Real-time monitoring and diagnostics
- Expert-level performance optimization

"""


# Pydantic imports for data validation and serialization

# Import real cosmic exceptions and oracle focus
try:
    from vault_oracle.core.cosmic_exceptions import (
        QuantumEntanglementError,
        TemporalCoherenceFailure,
        DataSynchronizationError
    )
    # Import oracle_focus separately to avoid conflicts
    try:
        from vault_oracle.core.oracle_focus import oracle_focus
    except ImportError:
        # Fallback oracle_focus if not available
        def oracle_focus(func):
            """Fallback decorator for oracle focus (does nothing)"""
            return func

    COSMIC_IMPORTS_AVAILABLE = True
    logger.info("✅ Successfully imported cosmic exceptions and oracle focus")
except ImportError as e:
    logger.warning(f"⚠️ Cosmic imports not available: {e}")
    COSMIC_IMPORTS_AVAILABLE = False

    # Fallback implementations only if imports fail
    def oracle_focus(func):
        """Fallback decorator for oracle focus (does nothing)"""
        return func

    class QuantumEntanglementError(Exception):
        """Fallback QuantumEntanglementError for standalone execution"""
        pass
    class TemporalCoherenceFailure(Exception):
        """Fallback TemporalCoherenceFailure for standalone execution"""
        pass
    class DataSynchronizationError(Exception):
        """Fallback DataSynchronizationError for standalone execution"""
        pass

class EntanglementState(Enum):
    """Quantum entanglement state classifications"""
    PRISTINE = "PRISTINE" # Perfect entanglement
    COHERENT = "COHERENT" # High coherence
    STABLE = "STABLE" # Good stability
    FLUCTUATING = "FLUCTUATING" # Some instability
    DEGRADED = "DEGRADED" # Poor entanglement
    COLLAPSED = "COLLAPSED" # Entanglement broken

class QuantumSecurityLevel(Enum):
    """Security levels for quantum states"""
    PUBLIC = "PUBLIC"
    INTERNAL = "INTERNAL"
    CONFIDENTIAL = "CONFIDENTIAL"
    SECRET = "SECRET"  # Security: Replace with environment variable
    TOP_SECRET = "TOP_SECRET"  # Security: Replace with environment variable

class QuantumNode(BaseModel):
    """Individual quantum node configuration (Pydantic Model)"""
    node_id: str = Field(..., description="Unique identifier for the quantum node")
    node_type: str = Field("standard", description="Type of the quantum node (e.g., standard, basketball_team)")
    security_level: QuantumSecurityLevel = Field(QuantumSecurityLevel.INTERNAL, description="Security clearance level")
    last_sync: Optional[datetime] = Field(default_factory=lambda: datetime.now(timezone.utc), description="Last synchronization timestamp")
    coherence_score: float = Field(1.0, ge=0.0, le=1.0, description="Current quantum coherence score (0.0 to 1.0)")
    entanglement_strength: float = Field(0.5, ge=0.0, le=1.0, description="Average entanglement strength with other nodes")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Arbitrary metadata for the node")
    is_active: bool = Field(True, description="Whether the node is currently active")

    model_config = { # For Pydantic V2
        "arbitrary_types_allowed": True
    }

class EntanglementPair(BaseModel):
    """Quantum entanglement pair definition (Pydantic Model)"""
    pair_id: str = Field(..., description="Unique identifier for the entanglement pair")
    node1_id: str = Field(..., description="ID of the first entangled node")
    node2_id: str = Field(..., description="ID of the second entangled node")
    entanglement_strength: float = Field(0.5, ge=0.0, le=1.0, description="Strength of the entanglement (0.0 to 1.0)")
    coherence_level: float = Field(1.0, ge=0.0, le=1.0, description="Coherence level of the entanglement (0.0 to 1.0)")
    chronicle_timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="Timestamp when entanglement was established")
    last_measurement: Optional[datetime] = Field(None, description="Last time entanglement strength was measured")
    basketball_context: Optional[str] = Field(None, description="Specific basketball context (e.g., game ID, player matchup)")
    temporal_stability: float = Field(1.0, ge=0.0, le=1.0, description="Temporal stability of the entanglement")

    model_config = { # For Pydantic V2
        "arbitrary_types_allowed": True
    }

class QuantumMetrics(BaseModel):
    """Comprehensive quantum system metrics (Pydantic Model)"""
    total_nodes: int = Field(0, description="Total number of registered quantum nodes")
    active_entanglements: int = Field(0, description="Number of currently active entanglement pairs")
    average_coherence: float = Field(0.0, ge=0.0, le=1.0, description="Average coherence across all active nodes")
    system_stability: float = Field(0.0, ge=0.0, le=1.0, description="Overall system stability score")
    sync_success_rate: float = Field(0.0, ge=0.0, le=1.0, description="Success rate of state synchronization operations")
    quantum_throughput: float = Field(0.0, description="Total quantum operations processed per second")
    temporal_drift: float = Field(0.0, ge=0.0, le=1.0, description="Overall temporal drift in the system")
    basketball_correlations: int = Field(0, description="Number of basketball-specific correlations established")

    model_config = { # For Pydantic V2
        "arbitrary_types_allowed": True
    }

@dataclass
class TemporalAnomaly:
    """Represents a detected temporal anomaly"""
    anomaly_type: str
    severity: float
    expected_duration: float
    actual_duration: float
    impact_assessment: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

class ExpertQuantumEntanglementManager:
    """
    Expert-level quantum entanglement management system for the Oracle.
    Manages quantum state synchronization, temporal coherence, and
    basketball-specific quantum correlations across distributed nodes.
    """
    def __init__(self, config: Optional[Dict[str, Any]] = None, core_instance=None):
        """Initialize the expert quantum entanglement manager"""
        self.config = config or {}
        self.core_instance = core_instance

        # Core quantum state management
        self.quantum_nodes: Dict[str, QuantumNode] = {}
        self.entanglement_pairs: Dict[str, EntanglementPair] = {}
        self.entangled_states: Dict[str, Any] = {} # Raw data storage
        self.quantum_registry: Dict[str, Dict[str, Any]] = {} # For broader quantum info

        # Temporal management
        self.temporal_anchors: Dict[str, datetime] = {}
        self.coherence_history: deque = deque(maxlen=1000)
        self.sync_timeline: Dict[str, List[datetime]] = defaultdict(list)

        # Basketball-specific entanglements
        self.basketball_entanglements: Dict[str, List[str]] = defaultdict(list)
        self.team_quantum_states: Dict[str, Dict[str, Any]] = {}
        self.player_quantum_profiles: Dict[str, Dict[str, Any]] = {}

        # Performance optimization
        self.state_cache: Dict[str, Tuple[Any, datetime]] = {}
        self.cache_ttl = timedelta(minutes=5)
        self.compression_enabled = self.config.get('compression_enabled', True)

        # Security and monitoring
        self.security_keys: Dict[str, str] = {}
        self.access_log: deque = deque(maxlen=10000)
        self.performance_metrics = QuantumMetrics()

        # Async support
        self._lock = asyncio.Lock() # Use asyncio.Lock for async concurrency

        logger.info(" MEDUSA VAULT: Expert Quantum Entanglement Manager initialized")

    @oracle_focus
    async def register_quantum_node(
        self,
        node_id: str,
        node_type: str = "standard",
        security_level: QuantumSecurityLevel = QuantumSecurityLevel.INTERNAL,
        metadata: Optional[Dict[str, Any]] = None
    ) -> QuantumNode:
        """Register a new quantum node in the system"""
        async with self._lock: # Use async with for asyncio.Lock
            if node_id in self.quantum_nodes:
                logger.warning(f" Node {node_id} already registered, updating...")

            node = QuantumNode(
                node_id=node_id,
                node_type=node_type,
                security_level=security_level,
                last_sync=datetime.now(timezone.utc), # Ensure timezone-aware
                metadata=metadata or {}
            )
            self.quantum_nodes[node_id] = node
            self.security_keys[node_id] = self._generate_security_key(node_id)

            logger.info(f"🔗 Quantum node registered: {node_id} ({node_type})")
            self._update_metrics()
            return node

    @oracle_focus
    async def entangle_nodes(
        self,
        node1_id: str,
        node2_id: str,
        strength: float = 0.5,
        basketball_context: Optional[str] = None
    ) -> str:
        """Create quantum entanglement between two nodes"""
        async with self._lock: # Use async with for asyncio.Lock
            # Validate nodes exist
            if node1_id not in self.quantum_nodes or node2_id not in self.quantum_nodes:
                raise QuantumEntanglementError(f"Cannot entangle non-existent nodes: {node1_id}, {node2_id}")

            # Create entanglement pair
            pair_id = f"{min(node1_id, node2_id)}_{max(node1_id, node2_id)}_{uuid.uuid4().hex[:8]}"
            entanglement = EntanglementPair(
                pair_id=pair_id,
                node1_id=node1_id,
                node2_id=node2_id,
                entanglement_strength=max(0.0, min(1.0, strength)), # Clamp strength
                coherence_level=1.0,
                chronicle_timestamp=datetime.now(timezone.utc), # Ensure timezone-aware
                basketball_context=basketball_context
            )
            self.entanglement_pairs[pair_id] = entanglement

            # Update node entanglement strengths
            self.quantum_nodes[node1_id].entanglement_strength = strength
            self.quantum_nodes[node2_id].entanglement_strength = strength

            # Track basketball-specific entanglements
            if basketball_context:
                self.basketball_entanglements[basketball_context].append(pair_id)

            logger.info(f"⚛️ Quantum entanglement created: {pair_id} (strength: {strength:.3f})")
            self._update_metrics()
            return pair_id

    @oracle_focus
    async def entangle_state(self, node_id: str, state: Dict[str, Any],
                             temporal_anchor: Optional[datetime] = None):
        """Synchronize quantum state for a node with temporal anchoring"""
        async with self._lock: # Use async with for asyncio.Lock
            try:
                # Validate node
                if node_id not in self.quantum_nodes:
                    raise QuantumEntanglementError(f"Node {node_id} not registered")

                # Temporal anchoring
                anchor_time = temporal_anchor or datetime.now(timezone.utc) # Ensure timezone-aware
                self.temporal_anchors[node_id] = anchor_time

                # State encryption and compression
                processed_state = self._process_state(state, node_id)

                # Store state with timestamp
                self.entangled_states[node_id] = {
                    'data': processed_state,
                    'timestamp': anchor_time,
                    'checksum': self._calculate_checksum(processed_state),
                    'node_type': self.quantum_nodes[node_id].node_type
                }

                # Update node sync time
                self.quantum_nodes[node_id].last_sync = anchor_time
                self.sync_timeline[node_id].append(anchor_time)

                # Propagate to entangled nodes
                await self._propagate_entanglement(node_id, processed_state) # Await async propagation

                # Update cache
                self.state_cache[node_id] = (processed_state, anchor_time)

                self._log_access(f"entangle_state:{node_id}")

            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: entangle state for {node_id}: {e}")
                raise QuantumEntanglementError(f"State entanglement failed: {e}")

    @oracle_focus
    async def get_entangled_state(
        self,
        node_id: str,
        include_metadata: bool = False,
        temporal_window: Optional[timedelta] = None
    ) -> Dict[str, Any]:
        """Retrieve entangled state with advanced filtering options"""
        async with self._lock: # Use async with for asyncio.Lock
            try:
                # Check cache first
                if node_id in self.state_cache:
                    cached_state, cached_time = self.state_cache[node_id]
                    if datetime.now(timezone.utc) - cached_time < self.cache_ttl: # Ensure timezone-aware
                        return self._format_state_response(cached_state, include_metadata, cached_time)

                # Get from main storage
                if node_id not in self.entangled_states:
                    logger.warning(f" No entangled state found for node {node_id}")
                    return {}

                state_data = self.entangled_states[node_id]
                state_time = state_data.get('timestamp', datetime.now(timezone.utc)) # Ensure timezone-aware

                # Apply temporal filtering
                if temporal_window:
                    if datetime.now(timezone.utc) - state_time > temporal_window: # Ensure timezone-aware
                        logger.warning(f"⏰ State for {node_id} outside temporal window")
                        return {}

                # Verify checksum
                current_checksum = self._calculate_checksum(state_data['data'])
                if current_checksum != state_data.get('checksum'):
                    logger.error(f"🚨 Checksum mismatch for node {node_id} - possible corruption")
                    raise DataSynchronizationError(f"State corruption detected for {node_id}")

                self._log_access(f"get_entangled_state:{node_id}")
                return self._format_state_response(state_data['data'], include_metadata, state_time)

            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: retrieve state for {node_id}: {e}")
                return {}

    @oracle_focus
    async def sync_entangled_states(self, states: Dict[str, Dict[str, Any]],
                                    force_sync: bool = False):
        """Bulk synchronize quantum states with conflict resolution"""
        async with self._lock: # Use async with for asyncio.Lock
            sync_results = {
                'successful': [],
                'failed': [],
                'conflicts': [],
                'total_processed': len(states)
            }
            try:
                for node_id, state in states.items():
                    try:
                        # Check for conflicts
                        if not force_sync and self._detect_sync_conflict(node_id, state):
                            sync_results['conflicts'].append(node_id)
                            logger.warning(f" Sync conflict detected for {node_id}")
                            continue

                        # Perform entanglement
                        await self.entangle_state(node_id, state) # Await the async method
                        sync_results['successful'].append(node_id)

                    except Exception as e:
                        sync_results['failed'].append({'node_id': node_id, 'error': str(e)})
                        logger.error(f" TITAN PROCESSING FAILED: sync state for {node_id}: {e}")

                # Update performance metrics
                self.performance_metrics.sync_success_rate = (
                    len(sync_results['successful']) / len(states)
                    if states else 0.0
                )
                logger.info(f" Bulk sync completed: {len(sync_results['successful'])}/{len(states)} successful")
                return sync_results
            except Exception as e:
                logger.error(f" Bulk sync failed: {e}")
                raise DataSynchronizationError(f"Bulk synchronization failed: {e}")

    @oracle_focus
    async def measure_entanglement_strength(self, node1_id: str, node2_id: str) -> float:
        """Measure quantum entanglement strength between nodes"""
        async with self._lock: # Use async with for asyncio.Lock
            pair_key_prefix = f"{min(node1_id, node2_id)}_{max(node1_id, node2_id)}"
            # Find the entanglement pair
            for pair_id, entanglement in self.entanglement_pairs.items():
                if pair_id.startswith(pair_key_prefix):
                    # Update measurement time
                    entanglement.last_measurement = datetime.now(timezone.utc) # Ensure timezone-aware

                    # Calculate current strength (may decay over time)
                    time_decay = self._calculate_temporal_decay(entanglement.chronicle_timestamp)
                    current_strength = entanglement.entanglement_strength * (1.0 - time_decay) # Ensure float multiplication

                    return max(0.0, current_strength) # Ensure strength is not negative
            return 0.0

    @oracle_focus
    def get_quantum_metrics(self) -> QuantumMetrics:
        """Get comprehensive quantum system metrics"""
        # No async lock needed in sync method
        self._update_metrics()
        return self.performance_metrics

    @oracle_focus
    async def basketball_entangle_teams(self, home_team_id: str, away_team_id: str,
                                        game_context: str) -> str:
        """Create basketball-specific quantum entanglement between teams"""
        basketball_context = f"game_{game_context}_{home_team_id}_vs_{away_team_id}"

        # Register teams as quantum nodes if not already registered
        await self.register_quantum_node( # Await the async method
            f"team_{home_team_id}",
            node_type="basketball_team",
            metadata={"mythic_roster_id": home_team_id, "role": "home"}
        )
        await self.register_quantum_node( # Await the async method
            f"team_{away_team_id}",
            node_type="basketball_team",
            metadata={"mythic_roster_id": away_team_id, "role": "away"}
        )

        # Create entanglement with basketball context
        pair_id = await self.entangle_nodes( # Await the async method
            f"team_{home_team_id}",
            f"team_{away_team_id}",
            strength=0.8, # High strength for game matchups
            basketball_context=basketball_context
        )

        logger.info(f" Basketball teams entangled: {home_team_id} vs {away_team_id}")
        return pair_id

    @oracle_focus
    def list_entangled_nodes(self,
                             node_type: Optional[str] = None,
                             active_only: bool = True) -> List[str]:
        """List quantum nodes with optional filtering"""
        with self._lock: # Use async with for asyncio.Lock
            nodes = []
            for node_id, node in self.quantum_nodes.items():
                if active_only and not node.is_active:
                    continue
                if node_type and node.node_type != node_type:
                    continue
                nodes.append(node_id)
            return sorted(nodes)

    @oracle_focus
    async def collapse_quantum_state(self, node_id: str,
                                     preserve_entanglements: bool = False):
        """Collapse quantum state for a node (quantum measurement)"""
        async with self._lock: # Use async with for asyncio.Lock
            try:
                if node_id in self.entangled_states:
                    del self.entangled_states[node_id]
                if node_id in self.state_cache:
                    del self.state_cache[node_id]
                if node_id in self.temporal_anchors:
                    del self.temporal_anchors[node_id]

                # Optionally break entanglements
                if not preserve_entanglements:
                    pairs_to_remove = []
                    for pair_id, entanglement in self.entanglement_pairs.items():
                        if entanglement.node1_id == node_id or entanglement.node2_id == node_id:
                            pairs_to_remove.append(pair_id)
                    for pair_id in pairs_to_remove:
                        del self.entanglement_pairs[pair_id]

                logger.info(f" Quantum state collapsed for node {node_id}")
                self._update_metrics()
            except Exception as e:
                logger.error(f" TITAN PROCESSING FAILED: collapse quantum state for {node_id}: {e}")
                raise QuantumEntanglementError(f"State collapse failed: {e}")

    # ====================
    # 🔧 PRIVATE HELPER METHODS
    # ====================

    def _process_state(self, state: Dict[str, Any], node_id: str) -> Dict[str, Any]:
        """Process state with encryption and compression"""
        processed = state.copy()
        # Add metadata
        processed['_quantum_metadata'] = {
            'node_id': node_id,
            'processed_at': datetime.now(timezone.utc).isoformat(), # Ensure timezone-aware
            'security_level': self.quantum_nodes[node_id].security_level.value
        }
        # Apply real compression if enabled
        if self.compression_enabled and len(json.dumps(processed)) > 1024:
            try:
                # Real compression implementation using gzip
                original_data = json.dumps(processed)
                compressed_bytes = gzip.compress(original_data.encode('utf-8'))

                # Encode compressed data as base64 for JSON serialization
                compressed_b64 = base64.b64encode(compressed_bytes).decode('utf-8')

                # Calculate compression ratio
                compression_ratio = len(compressed_bytes) / len(original_data.encode('utf-8'))

                # Only use compression if it actually reduces size significantly
                if compression_ratio < 0.8:  # At least 20% reduction
                    processed = {
                        '_compressed': True,
                        '_compression_ratio': compression_ratio,
                        '_original_size': len(original_data),
                        '_compressed_size': len(compressed_bytes),
                        '_data': compressed_b64,
                        '_quantum_metadata': processed.get('_quantum_metadata', {})
                    }
                    logger.debug(f"🗜️ Compressed quantum state: {compression_ratio:.2%} of original size")
                else:
                    # Compression not beneficial, keep original
                    processed['_compression_attempted'] = True
                    processed['_compression_ratio'] = compression_ratio
                    logger.debug(f"⚠️ Compression not beneficial: {compression_ratio:.2%} ratio")

            except Exception as e:
                logger.warning(f"⚠️ Compression failed: {e}, using uncompressed data")
                processed['_compression_failed'] = str(e)

        return processed

    def _decompress_state(self, compressed_state: Dict[str, Any]) -> Dict[str, Any]:
        """Decompress quantum state data"""
        if not compressed_state.get('_compressed', False):
            return compressed_state

        try:
            # Extract compressed data
            compressed_b64 = compressed_state.get('_data', '')
            if not compressed_b64:
                logger.warning("⚠️ No compressed data found in state")
                return compressed_state

            # Decode and decompress
            compressed_bytes = base64.b64decode(compressed_b64.encode('utf-8'))
            decompressed_data = gzip.decompress(compressed_bytes).decode('utf-8')

            # Parse back to dictionary
            original_state = json.loads(decompressed_data)

            logger.debug(f"🔓 Decompressed quantum state: {len(compressed_bytes)} -> {len(decompressed_data)} bytes")
            return original_state

        except Exception as e:
            logger.error(f"❌ Decompression failed: {e}")
            # Return the compressed state as-is if decompression fails
            return compressed_state

    async def _propagate_entanglement(self, source_node_id: str, state: Dict[str, Any]):
        """Propagate state changes to entangled nodes"""
        entangled_nodes = self._get_entangled_nodes(source_node_id)
        tasks = []
        for node_id in entangled_nodes:
            # Create a task for each propagation to allow concurrent updates
            tasks.append(asyncio.create_task(self._single_node_propagation(source_node_id, node_id, state)))
        await asyncio.gather(*tasks, return_exceptions=True) # Gather results, handle exceptions internally

    async def _single_node_propagation(self, source_node_id: str, target_node_id: str, state: Dict[str, Any]):
        """Propagate state to a single entangled node."""
        try:
            # Calculate propagation strength
            strength = await self.measure_entanglement_strength(source_node_id, target_node_id) # Await async method
            if strength > 0.1: # Only propagate if significant entanglement
                # Create partial state based on entanglement strength
                partial_state = self._create_partial_state(state, strength)

                # Update entangled node
                async with self._lock: # Acquire lock for state modification
                    if target_node_id in self.entangled_states:
                        self.entangled_states[target_node_id]['data'].update(partial_state)
                        self.entangled_states[target_node_id]['checksum'] = self._calculate_checksum(self.entangled_states[target_node_id]['data'])
                        self.entangled_states[target_node_id]['timestamp'] = datetime.now(timezone.utc) # Update timestamp

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: propagate to {target_node_id}: {e}")

    def _get_entangled_nodes(self, node_id: str) -> List[str]:
        """Get all nodes entangled with the given node"""
        entangled = []
        for entanglement in self.entanglement_pairs.values():
            if entanglement.node1_id == node_id:
                entangled.append(entanglement.node2_id)
            elif entanglement.node2_id == node_id:
                entangled.append(entanglement.node1_id)