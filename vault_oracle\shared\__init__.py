#!/usr/bin/env python3
# DIGITAL FINGERPRINT: UUID=ef0a1b2c-3d4e-5f6a-7b8c-9d0e1f2a3b4c | DATE=2025-06-26
"""
(C) 2025 Hyper Medusa Neural Vault, All Rights Reserved.
PROPRIETARY & CONFIDENTIAL — UNAUTHORIZED USE, COPYING, OR DISTRIBUTION IS STRICTLY PROHIBITED.

This file contains proprietary algorithms, business logic, and intellectual property of Hyper Medusa Neural Vault.
Use, reproduction, or disclosure by any means without explicit written permission is a violation of applicable law.
"""

try:
    from .wisdom_scarab import WisdomScarab
    WISDOM_SCARAB_AVAILABLE = True
except ImportError:
    WISDOM_SCARAB_AVAILABLE = False
    WisdomScarab = None

try:
    from .elemental_utils import (
        calculate_all_elemental_decays,
        calculate_elemental_shift,
        determine_dominant_element,
        get_default_elemental_profile,
        ELEMENTAL_HALF_LIFE,
        ELEMENTAL_MAP
    )
    ELEMENTAL_UTILS_AVAILABLE = True
except ImportError:
    ELEMENTAL_UTILS_AVAILABLE = False
    calculate_all_elemental_decays = None
    calculate_elemental_shift = None
    determine_dominant_element = None
    get_default_elemental_profile = None
    ELEMENTAL_HALF_LIFE = {}
    ELEMENTAL_MAP = {}

"""
HYPER MEDUSA NEURAL VAULT - Shared Utilities Package Business Value Documentation
================================================================================

shared/__init__.py
------------------
Centralizes and exposes shared utility functions and classes for the Medusa Vault platform.

Business Value:
- Reduces code duplication and enforces best practices across the codebase.
- Simplifies maintenance and onboarding for new developers.
- Supports rapid development and integration of new features.

For further details, see module-level docstrings and architecture documentation.
"""

"""
Vault Oracle Shared Utilities
Authoritative implementations to eliminate duplicate code across the system

 NOTE: WisdomScarab in this module is DEPRECATED
Use vault_oracle.wells.WisdomScarab for production schema management
"""

# DEPRECATED: Use vault_oracle.wells.WisdomScarab instead

# DEPRECATED: TitanClashInterface in this module is DEPRECATED
# Use vault_oracle.core.QuantumEntangler.TitanClashInterface for expert team clash analysis
# Import it directly: from vault_oracle.core.QuantumEntangler import TitanClashInterface

__all__ = [
 'calculate_all_elemental_decays',
 'calculate_elemental_shift',
 'determine_dominant_element', 
 'get_default_elemental_profile',
 'ELEMENTAL_HALF_LIFE',
 'ELEMENTAL_MAP',
 'WisdomScarab' # DEPRECATED - Use vault_oracle.wells.WisdomScarab
]
