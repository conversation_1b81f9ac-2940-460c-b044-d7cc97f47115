import logging
from .cortex_core import CognitiveBasketballCortex

"""
Cognitive Basketball Cortex - Central AI Engine
=============================================

The brain of the NBA Book Official system, orchestrating all advanced
analytical and predictive capabilities through specialized cognitive spires.
"""

logger = logging.getLogger(__name__)

# Import all basketball processors from the unified basketball_processors module
try:
    from .basketball_processors import (
        # Quantum Processors
        QuantumMetricEngine,
        SituationalNeuralProcessor,
        AthleteCognitiveProfiler,
        AdaptiveThreatMatrix,
        EntangledMemoryVault,
        TemporalFluxStabilizer,
        # Neural Processors
        NeuralPatternRecognizer,
        PerformanceOracleEngine,
        GameStateAnalyzer,
        PredictiveInsightEngine,
        StrategicSimulator,
        RealTimeProcessor
    )

    ALL_PROCESSORS_AVAILABLE = True
    logger.info("✅ All Basketball Cognitive Processors imported successfully")

except ImportError as e:
    ALL_PROCESSORS_AVAILABLE = False
    logger.error(f"❌ Failed to import basketball processors: {e}")

    # Set all processors to None if import fails
    QuantumMetricEngine = None
    SituationalNeuralProcessor = None
    AthleteCognitiveProfiler = None
    AdaptiveThreatMatrix = None
    EntangledMemoryVault = None
    TemporalFluxStabilizer = None
    NeuralPatternRecognizer = None
    PerformanceOracleEngine = None
    GameStateAnalyzer = None
    PredictiveInsightEngine = None
    StrategicSimulator = None
    RealTimeProcessor = None

# Export all available components
__all__ = [
    'CognitiveBasketballCortex',
    # Quantum Processors
    'QuantumMetricEngine',
    'SituationalNeuralProcessor',
    'AthleteCognitiveProfiler',
    'AdaptiveThreatMatrix',
    'EntangledMemoryVault',
    'TemporalFluxStabilizer',
    # Neural Processors
    'NeuralPatternRecognizer',
    'PerformanceOracleEngine',
    'GameStateAnalyzer',
    'PredictiveInsightEngine',
    'StrategicSimulator',
    'RealTimeProcessor',
    # Status flags
    'ALL_PROCESSORS_AVAILABLE'
]

__version__ = "3.1.0"
__cortex_version__ = "MEDUSA_CORTEX_3.1_UNIFIED"
