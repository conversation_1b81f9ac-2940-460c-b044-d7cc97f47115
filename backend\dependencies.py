from fastapi import Request, HTTPException
from typing import Optional, Any
import logging
from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
from vault_oracle.core.oracle_engine import OracleEngine
from src.cognitive_spires import CognitiveSpiresFactory_Expert
from backend.services.enhanced_prediction_service import EnhancedPredictionService
from backend.infrastructure.database import ExpertDatabaseManager

"""
🏀 HYPER MEDUSA NEURAL VAULT - Dependency Injection System
=========================================================

Centralized dependency injection for FastAPI application.
Provides shared instances of heavy services to avoid initialization during imports.
"""


logger = logging.getLogger("🏀 HYPER_MEDUSA_DEPENDENCIES")

# Global service instances (initialized once during startup)
_messaging_orchestrator: Optional[Any] = None
_oracle_engine: Optional[Any] = None
_cognitive_spires: Optional[Any] = None
_enhanced_prediction_service: Optional[Any] = None
_database_manager: Optional[Any] = None
_kingdom_core: Optional[Any] = None
_war_council: Optional[Any] = None
_medusa_queen: Optional[Any] = None

def get_messaging_orchestrator(request: Request):
    """Get the shared messaging orchestrator instance (created on-demand)"""
    # Check if app state has the service and it's not None
    if hasattr(request.app.state, 'messaging_orchestrator') and request.app.state.messaging_orchestrator is not None:
        return request.app.state.messaging_orchestrator

    # Create on-demand
    try:
        orchestrator = ExpertMessagingOrchestrator()

        # Store in app state for reuse
        if hasattr(request.app.state, 'messaging_orchestrator'):
            request.app.state.messaging_orchestrator = orchestrator

        logger.info("✅ Created messaging orchestrator on-demand")
        return orchestrator
    except Exception as e:
        logger.error(f"❌ Failed to create messaging orchestrator: {e}")
        raise HTTPException(status_code=503, detail="Messaging service unavailable")

def get_oracle_engine(request: Request):
    """Get the shared oracle engine instance (created on-demand)"""
    # Check if app state has the service and it's not None
    if hasattr(request.app.state, 'oracle_engine') and request.app.state.oracle_engine is not None:
        return request.app.state.oracle_engine

    # Create on-demand
    try:
        engine = OracleEngine()

        # Store in app state for reuse
        if hasattr(request.app.state, 'oracle_engine'):
            request.app.state.oracle_engine = engine

        logger.info("✅ Created oracle engine on-demand")
        return engine
    except Exception as e:
        logger.error(f"❌ Failed to create oracle engine: {e}")
        raise HTTPException(status_code=503, detail="Oracle service unavailable")

def get_cognitive_spires(request: Request):
    """Get the shared cognitive spires instance"""
    if hasattr(request.app.state, 'cognitive_spires'):
        return request.app.state.cognitive_spires
    
    # Fallback to global instance
    global _cognitive_spires
    if _cognitive_spires is None:
        logger.warning("⚠️ Cognitive spires not initialized, creating fallback")
        try:
            _cognitive_spires = CognitiveSpiresFactory_Expert()
        except ImportError as e:
            logger.error(f"❌ Failed to create cognitive spires: {e}")
            raise HTTPException(status_code=503, detail="Cognitive spires unavailable")
    
    return _cognitive_spires

def get_enhanced_prediction_service(request: Request):
    """Get the shared enhanced prediction service instance"""
    if hasattr(request.app.state, 'enhanced_prediction_service'):
        return request.app.state.enhanced_prediction_service
    
    # Fallback to global instance
    global _enhanced_prediction_service
    if _enhanced_prediction_service is None:
        logger.warning("⚠️ Enhanced prediction service not initialized, creating fallback")
        try:
            _enhanced_prediction_service = EnhancedPredictionService()
        except ImportError as e:
            logger.error(f"❌ Failed to create enhanced prediction service: {e}")
            raise HTTPException(status_code=503, detail="Prediction service unavailable")
    
    return _enhanced_prediction_service

def get_database_manager(request: Request):
    """Get the shared database manager instance"""
    if hasattr(request.app.state, 'database_manager'):
        return request.app.state.database_manager
    
    # Fallback to global instance
    global _database_manager
    if _database_manager is None:
        logger.warning("⚠️ Database manager not initialized, creating fallback")
        try:
            _database_manager = ExpertDatabaseManager()
        except ImportError as e:
            logger.error(f"❌ Failed to create database manager: {e}")
            raise HTTPException(status_code=503, detail="Database service unavailable")
    
    return _database_manager

def get_orchestrator_context(request: Request):
    """Get the orchestrator context (compatibility function)"""
    # This is for backward compatibility with existing routers
    class OrchestatorContext:
        def __init__(self, request: Request):
            self.messaging_orchestrator = get_messaging_orchestrator(request)
            self.oracle_engine = get_oracle_engine(request)
            self.cognitive_spires = get_cognitive_spires(request)
            self.enhanced_prediction_service = get_enhanced_prediction_service(request)
            self.database_manager = get_database_manager(request)
    
    return OrchestatorContext(request)

# Service initialization functions (called during startup)
async def initialize_services(app):
    """Initialize lightweight service placeholders during application startup"""
    logger.info("🚀 Initializing lightweight service placeholders...")

    try:
        # Set up service placeholders (no heavy initialization)
        logger.info("📡 Setting up messaging orchestrator placeholder...")
        app.state.messaging_orchestrator = None  # Will be created on-demand
        logger.info("✅ Messaging orchestrator placeholder set")

        logger.info("🔮 Setting up oracle engine placeholder...")
        app.state.oracle_engine = None  # Will be created on-demand
        logger.info("✅ Oracle engine placeholder set")

        logger.info("🧠 Setting up cognitive spires placeholder...")
        app.state.cognitive_spires = None  # Will be created on-demand
        logger.info("✅ Cognitive spires placeholder set")

        logger.info("🎯 Setting up enhanced prediction service placeholder...")
        app.state.enhanced_prediction_service = None  # Will be created on-demand
        logger.info("✅ Enhanced prediction service placeholder set")

        logger.info("🗄️ Setting up database manager placeholder...")
        app.state.database_manager = None  # Will be created on-demand
        logger.info("✅ Database manager placeholder set")
        
        logger.info("🎉 All shared services initialized successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Service initialization failed: {e}")
        return False

async def cleanup_services(app):
    """Cleanup services during application shutdown"""
    logger.info("🧹 Cleaning up shared services...")
    
    # Cleanup services if they have cleanup methods
    services = [
        'messaging_orchestrator',
        'oracle_engine', 
        'cognitive_spires',
        'enhanced_prediction_service',
        'database_manager'
    ]
    
    for service_name in services:
        if hasattr(app.state, service_name):
            service = getattr(app.state, service_name)
            if hasattr(service, 'cleanup'):
                try:
                    await service.cleanup()
                    logger.info(f"✅ {service_name} cleaned up")
                except Exception as e:
                    logger.error(f"❌ Failed to cleanup {service_name}: {e}")
    
    logger.info("🧹 Service cleanup complete")


def get_kingdom_core(request: Request):
    """Get the shared kingdom core instance"""
    if hasattr(request.app.state, 'kingdom_core'):
        return request.app.state.kingdom_core
    
    # Fallback to global instance
    global _kingdom_core
    if _kingdom_core is None:
        logger.warning("⚠️ Kingdom core not initialized, creating fallback")
        try:
            from src.kingdom_architecture.medusa_kingdom_core import MedusaKingdomCore
            _kingdom_core = MedusaKingdomCore()
        except ImportError as e:
            logger.error(f"❌ Failed to create kingdom core: {e}")
            raise HTTPException(status_code=503, detail="Kingdom core unavailable")
    
    return _kingdom_core

def get_war_council(request: Request):
    """Get the shared war council instance"""
    if hasattr(request.app.state, 'war_council'):
        return request.app.state.war_council
    
    # Fallback to global instance
    global _war_council
    if _war_council is None:
        logger.warning("⚠️ War council not initialized, creating fallback")
        try:
            from src.kingdom_architecture.war_council_integration import WarCouncilIntegration
            _war_council = WarCouncilIntegration()
        except ImportError as e:
            logger.error(f"❌ Failed to create war council: {e}")
            raise HTTPException(status_code=503, detail="War council unavailable")
    
    return _war_council

def get_medusa_queen(request: Request):
    """Get the shared MEDUSA Queen instance"""
    if hasattr(request.app.state, 'medusa_queen'):
        return request.app.state.medusa_queen
    
    # Fallback to global instance
    global _medusa_queen
    if _medusa_queen is None:
        logger.warning("⚠️ MEDUSA Queen not initialized, creating fallback")
        try:
            from kingdom.adapters.medusa_queen_adapter import MedusaQueenAdapter
            _medusa_queen = MedusaQueenAdapter()
        except ImportError as e:
            logger.error(f"❌ Failed to create MEDUSA Queen: {e}")
            raise HTTPException(status_code=503, detail="MEDUSA Queen unavailable")
    
    return _medusa_queen
